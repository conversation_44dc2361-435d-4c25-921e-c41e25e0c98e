import { DataTypes, Op } from 'sequelize';

export const up = async (queryInterface) => {
  await queryInterface.createTable('invoices', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    subscription_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'subscriptions',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    stripe_invoice_id: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: true,
    },
    invoice_number: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    status: {
      type: DataTypes.ENUM(
        'draft',
        'open',
        'paid',
        'void',
        'uncollectible'
      ),
      defaultValue: 'draft',
    },
    amount_due: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    amount_paid: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0,
    },
    amount_remaining: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    subtotal: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    tax_amount: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0,
    },
    discount_amount: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0,
    },
    total: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    currency: {
      type: DataTypes.STRING(3),
      defaultValue: 'INR',
    },
    period_start: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    period_end: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    due_date: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    paid_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    voided_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    attempt_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    next_payment_attempt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    billing_reason: {
      type: DataTypes.ENUM(
        'subscription_cycle',
        'subscription_create',
        'subscription_update',
        'subscription_threshold',
        'upcoming',
        'manual'
      ),
      allowNull: true,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    line_items: {
      type: DataTypes.JSONB,
      defaultValue: [],
    },
    metadata: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Create indexes
  await queryInterface.addIndex('invoices', ['tenant_id'], {
    name: 'invoices_tenant_id_idx',
  });

  await queryInterface.addIndex('invoices', ['subscription_id'], {
    name: 'invoices_subscription_id_idx',
  });

  await queryInterface.addIndex('invoices', ['stripe_invoice_id'], {
    unique: true,
    name: 'invoices_stripe_invoice_id_unique',
    where: {
      stripe_invoice_id: {
        [Op.ne]: null,
      },
    },
  });

  await queryInterface.addIndex('invoices', ['invoice_number'], {
    unique: true,
    name: 'invoices_invoice_number_unique',
  });

  await queryInterface.addIndex('invoices', ['status'], {
    name: 'invoices_status_idx',
  });

  await queryInterface.addIndex('invoices', ['due_date'], {
    name: 'invoices_due_date_idx',
  });
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('invoices');
};
