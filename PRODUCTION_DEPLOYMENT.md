# 🚀 TallyCRM SaaS Production Deployment Guide

This guide covers deploying your TallyCRM SaaS application to production with proper security, monitoring, and scalability.

## 📋 Pre-Deployment Checklist

### ✅ **Environment Setup**
- [ ] Production server with adequate resources (4+ CPU cores, 8+ GB RAM)
- [ ] PostgreSQL database (managed service recommended)
- [ ] Redis instance for caching and sessions
- [ ] SSL certificate for HTTPS
- [ ] Domain name configured
- [ ] Stripe production account setup

### ✅ **Security Requirements**
- [ ] Firewall configured (only ports 80, 443, 22 open)
- [ ] SSH key-based authentication
- [ ] Database access restricted to application servers
- [ ] Environment variables secured
- [ ] Regular security updates enabled

## 🔧 **Server Setup**

### 1. **System Requirements**

**Minimum Requirements:**
- Ubuntu 20.04+ or CentOS 8+
- 4 CPU cores, 8GB RAM, 50GB SSD
- Node.js 18+, PostgreSQL 13+, Redis 6+

**Recommended for Production:**
- 8 CPU cores, 16GB RAM, 100GB SSD
- Load balancer for multiple instances
- Managed database service (AWS RDS, Google Cloud SQL)

### 2. **Initial Server Setup**

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
sudo npm install -g pm2

# Install Nginx for reverse proxy
sudo apt install nginx -y

# Install PostgreSQL client
sudo apt install postgresql-client -y

# Install Redis
sudo apt install redis-server -y
```

### 3. **Application Deployment**

```bash
# Create application user
sudo useradd -m -s /bin/bash tallycrm
sudo usermod -aG sudo tallycrm

# Switch to application user
sudo su - tallycrm

# Clone repository
git clone <your-repository-url> /home/<USER>/tallycrm
cd /home/<USER>/tallycrm

# Install dependencies
npm run install:all

# Build frontend
cd frontend && npm run build && cd ..
```

## 🔐 **Environment Configuration**

### 1. **Production Environment Variables**

Create `/home/<USER>/tallycrm/backend/.env`:

```env
# Application Configuration
NODE_ENV=production
PORT=3001
APP_NAME=TallyCRM SaaS
APP_URL=https://yourdomain.com
FRONTEND_URL=https://yourdomain.com

# Database Configuration (Use managed service)
DB_HOST=your-db-host.amazonaws.com
DB_PORT=5432
DB_NAME=tallycrm_production
DB_USERNAME=tallycrm_user
DB_PASSWORD=your_secure_password
DB_SSL=true
DB_LOGGING=false

# Security Configuration
JWT_SECRET=your_super_secure_jwt_secret_64_chars_minimum
JWT_REFRESH_SECRET=your_super_secure_refresh_secret_64_chars_minimum
SESSION_SECRET=your_super_secure_session_secret_64_chars_minimum
BCRYPT_SALT_ROUNDS=12
ENCRYPTION_KEY=your_32_character_encryption_key

# Stripe Configuration (Production Keys)
STRIPE_SECRET_KEY=sk_live_your_live_secret_key
STRIPE_PUBLISHABLE_KEY=pk_live_your_live_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_live_webhook_secret

# Email Configuration (Production SMTP)
SMTP_HOST=smtp.yourdomain.com
SMTP_PORT=587
SMTP_SECURE=true
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
EMAIL_FROM=<EMAIL>

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# Security Settings
ENABLE_RATE_LIMITING=true
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CORS_ORIGINS=https://yourdomain.com

# Monitoring
LOG_LEVEL=error
ENABLE_MONITORING=true
ENABLE_SWAGGER=false

# SaaS Features
ENABLE_USAGE_TRACKING=true
ENABLE_BILLING=true
ENABLE_SUBSCRIPTIONS=true
ENABLE_MULTI_TENANT=true
```

### 2. **Frontend Environment Variables**

Create `/home/<USER>/tallycrm/frontend/.env.production`:

```env
REACT_APP_API_URL=https://yourdomain.com/api/v1
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_live_your_live_publishable_key
REACT_APP_ENVIRONMENT=production
GENERATE_SOURCEMAP=false
```

## 🗄️ **Database Setup**

### 1. **Production Database**

```bash
# Connect to production database
psql -h your-db-host -U tallycrm_user -d tallycrm_production

# Run migrations
cd /home/<USER>/tallycrm/backend
npm run migrate

# Seed subscription plans
npm run seed:plans
```

### 2. **Database Security**

```sql
-- Create read-only user for monitoring
CREATE USER tallycrm_readonly WITH PASSWORD 'readonly_password';
GRANT CONNECT ON DATABASE tallycrm_production TO tallycrm_readonly;
GRANT USAGE ON SCHEMA public TO tallycrm_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO tallycrm_readonly;

-- Set up connection limits
ALTER USER tallycrm_user CONNECTION LIMIT 20;
```

## 🔄 **Process Management with PM2**

### 1. **PM2 Configuration**

Create `/home/<USER>/tallycrm/ecosystem.config.js`:

```javascript
module.exports = {
  apps: [
    {
      name: 'tallycrm-backend',
      script: './backend/src/server.js',
      cwd: '/home/<USER>/tallycrm',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3001,
      },
      error_file: '/home/<USER>/logs/backend-error.log',
      out_file: '/home/<USER>/logs/backend-out.log',
      log_file: '/home/<USER>/logs/backend-combined.log',
      time: true,
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=1024',
    }
  ]
};
```

### 2. **Start Application**

```bash
# Create logs directory
mkdir -p /home/<USER>/logs

# Start application
cd /home/<USER>/tallycrm
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 startup script
pm2 startup
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u tallycrm --hp /home/<USER>
```

## 🌐 **Nginx Configuration**

### 1. **Nginx Setup**

Create `/etc/nginx/sites-available/tallycrm`:

```nginx
# Rate limiting
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;

# Upstream backend
upstream tallycrm_backend {
    least_conn;
    server 127.0.0.1:3001;
    keepalive 32;
}

server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL Configuration
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin";

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Frontend (React build)
    location / {
        root /home/<USER>/tallycrm/frontend/build;
        index index.html;
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API Routes
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        
        proxy_pass http://tallycrm_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # Webhooks (no rate limiting for Stripe)
    location /webhooks/ {
        proxy_pass http://tallycrm_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Auth endpoints with stricter rate limiting
    location ~ ^/api/v1/(auth|login|register) {
        limit_req zone=login burst=5 nodelay;
        
        proxy_pass http://tallycrm_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 2. **Enable Nginx Configuration**

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/tallycrm /etc/nginx/sites-enabled/

# Test configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

## 🔐 **SSL Certificate Setup**

### Using Let's Encrypt (Recommended)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Obtain certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Test auto-renewal
sudo certbot renew --dry-run
```

## 📊 **Monitoring and Logging**

### 1. **Application Monitoring**

```bash
# Install monitoring tools
npm install -g pm2-logrotate

# Configure log rotation
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
pm2 set pm2-logrotate:compress true
```

### 2. **System Monitoring**

Create `/home/<USER>/scripts/health-check.sh`:

```bash
#!/bin/bash
# Health check script

# Check if backend is running
if ! pm2 list | grep -q "tallycrm-backend.*online"; then
    echo "Backend is down, restarting..."
    pm2 restart tallycrm-backend
fi

# Check database connectivity
if ! pg_isready -h your-db-host -p 5432 -U tallycrm_user; then
    echo "Database connection failed"
    # Send alert
fi

# Check disk space
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "Disk usage is above 80%: $DISK_USAGE%"
    # Send alert
fi
```

### 3. **Setup Cron Jobs**

```bash
# Edit crontab
crontab -e

# Add health check (every 5 minutes)
*/5 * * * * /home/<USER>/scripts/health-check.sh

# Daily database backup
0 2 * * * pg_dump -h your-db-host -U tallycrm_user tallycrm_production | gzip > /home/<USER>/backups/db-$(date +\%Y\%m\%d).sql.gz

# Clean old logs (weekly)
0 0 * * 0 find /home/<USER>/logs -name "*.log" -mtime +30 -delete
```

## 🔄 **Deployment Automation**

### 1. **Deployment Script**

Create `/home/<USER>/scripts/deploy.sh`:

```bash
#!/bin/bash
set -e

echo "🚀 Starting TallyCRM deployment..."

# Navigate to app directory
cd /home/<USER>/tallycrm

# Pull latest changes
git pull origin main

# Install/update dependencies
npm run install:all

# Build frontend
cd frontend && npm run build && cd ..

# Run database migrations
cd backend && npm run migrate && cd ..

# Restart application
pm2 restart tallycrm-backend

# Wait for app to start
sleep 10

# Health check
if curl -f http://localhost:3001/health; then
    echo "✅ Deployment successful!"
else
    echo "❌ Deployment failed - health check failed"
    exit 1
fi
```

## 🔒 **Security Hardening**

### 1. **Firewall Configuration**

```bash
# Configure UFW firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### 2. **Fail2Ban Setup**

```bash
# Install Fail2Ban
sudo apt install fail2ban -y

# Configure for Nginx
sudo tee /etc/fail2ban/jail.local << EOF
[nginx-http-auth]
enabled = true
filter = nginx-http-auth
logpath = /var/log/nginx/error.log
maxretry = 3
bantime = 3600

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
logpath = /var/log/nginx/error.log
maxretry = 10
bantime = 600
EOF

sudo systemctl restart fail2ban
```

## 📈 **Performance Optimization**

### 1. **Database Optimization**

```sql
-- Create indexes for better performance
CREATE INDEX CONCURRENTLY idx_customers_tenant_id ON customers(tenant_id);
CREATE INDEX CONCURRENTLY idx_subscriptions_tenant_id ON subscriptions(tenant_id);
CREATE INDEX CONCURRENTLY idx_usage_records_tenant_metric ON usage_records(tenant_id, metric_name);

-- Analyze tables
ANALYZE customers;
ANALYZE subscriptions;
ANALYZE usage_records;
```

### 2. **Redis Caching**

Update backend configuration to use Redis for sessions and caching.

## 🚨 **Backup Strategy**

### 1. **Database Backups**

```bash
# Create backup script
cat > /home/<USER>/scripts/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
pg_dump -h your-db-host -U tallycrm_user tallycrm_production | gzip > $BACKUP_DIR/db_$DATE.sql.gz

# Application backup
tar -czf $BACKUP_DIR/app_$DATE.tar.gz -C /home/<USER>

# Clean old backups (keep 30 days)
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
EOF

chmod +x /home/<USER>/scripts/backup.sh
```

## ✅ **Post-Deployment Checklist**

- [ ] Application is accessible via HTTPS
- [ ] SSL certificate is valid and auto-renewing
- [ ] Database migrations completed successfully
- [ ] Stripe webhooks are configured and working
- [ ] Email notifications are working
- [ ] Monitoring and alerting is set up
- [ ] Backups are running automatically
- [ ] Security headers are present
- [ ] Rate limiting is working
- [ ] Log rotation is configured
- [ ] Health checks are passing

## 🆘 **Troubleshooting**

### Common Issues:

1. **Application won't start:**
   ```bash
   pm2 logs tallycrm-backend
   ```

2. **Database connection issues:**
   ```bash
   pg_isready -h your-db-host -p 5432 -U tallycrm_user
   ```

3. **SSL certificate issues:**
   ```bash
   sudo certbot certificates
   sudo nginx -t
   ```

4. **High memory usage:**
   ```bash
   pm2 monit
   ```

Your TallyCRM SaaS application is now ready for production! 🎉
