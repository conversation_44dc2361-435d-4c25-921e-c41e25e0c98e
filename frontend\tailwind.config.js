/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      // Custom breakpoints
      screens: {
        'xs': '475px',
      },
      // Custom color palette matching the existing Bootstrap theme
      colors: {
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#0d6efd', // Bootstrap primary
          700: '#0b5ed7',
          800: '#1e40af',
          900: '#1e3a8a',
          950: '#172554',
        },
        secondary: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#6c757d', // Bootstrap secondary
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
        },
        success: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e',
          600: '#198754', // Bootstrap success
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },
        danger: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc3545', // Bootstrap danger
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        },
        warning: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#ffc107', // Bootstrap warning
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
        },
        info: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#0dcaf0', // Bootstrap info
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
        },
        // Tally brand colors
        tally: {
          red: '#e31e24',
          blue: '#1e3a8a',
          green: '#16a34a',
          orange: '#ea580c',
        },
        // Purple theme colors
        purple: {
          50: '#faf5ff',
          100: '#f3e8ff',
          200: '#e9d5ff',
          300: '#d8b4fe',
          400: '#c084fc',
          500: '#a855f7',
          600: '#9333ea',
          700: '#7c3aed',
          800: '#6b21a8',
          900: '#581c87',
          950: '#3b0764',
        },
        // Custom gradient colors for the theme
        gradient: {
          'purple-start': '#8b5cf6',
          'purple-end': '#a855f7',
          'purple-dark': '#6b21a8',
          'purple-light': '#c084fc',
        },
        // Neutral colors
        light: '#f8f9fa',
        dark: '#212529',
        muted: '#6c757d',
      },
      // Custom font family
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      // Custom spacing
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      // Custom border radius
      borderRadius: {
        'sm': '0.25rem',
        'DEFAULT': '0.375rem',
        'lg': '0.5rem',
      },
      // Custom shadows
      boxShadow: {
        'sm': '0 0.125rem 0.25rem rgba(0, 0, 0, 0.075)',
        'DEFAULT': '0 0.5rem 1rem rgba(0, 0, 0, 0.15)',
        'lg': '0 1rem 3rem rgba(0, 0, 0, 0.175)',
      },
      // Custom transitions
      transitionDuration: {
        '150': '0.15s',
        '300': '0.3s',
        '500': '0.5s',
      },
      // Layout dimensions
      width: {
        'sidebar': '220px',
        'sidebar-collapsed': '64px',
      },
      height: {
        'header': '60px',
        'footer': '50px',
      },
      margin: {
        'sidebar': '220px',
        'sidebar-collapsed': '64px',
      },
      // Z-index scale
      zIndex: {
        'dropdown': '1000',
        'sticky': '1020',
        'fixed': '1030',
        'modal-backdrop': '1040',
        'modal': '1050',
        'popover': '1060',
        'tooltip': '1070',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
}

