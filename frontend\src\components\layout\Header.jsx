import React, { useState } from 'react';
import { useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { cn } from '../../utils/helpers';

const Header = ({ onToggleSidebar, onToggleSidebarMobile, sidebarCollapsed }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const { user, logout } = useAuth();
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);

  // Function to get dynamic page title based on current route
  const getPageTitle = () => {
    const pathname = location.pathname;
    const tab = searchParams.get('tab');

    // Dashboard
    if (pathname === '/dashboard') {
      return 'Dashboard';
    }

    // Customers
    if (pathname.startsWith('/customers')) {
      if (pathname.includes('/add')) return 'Add Customer';
      if (pathname.includes('/edit')) return 'Edit Customer';
      if (pathname.includes('/details')) return 'Customer Details';
      return 'Customers';
    }

    // Services
    if (pathname.startsWith('/services')) {
      if (pathname.includes('/add')) return 'Add Service Call';
      if (pathname.includes('/edit')) return 'Edit Service Call';
      if (pathname.includes('/details')) return 'Service Call Details';
      return 'Service Calls';
    }

    // Sales
    if (pathname.startsWith('/sales')) {
      if (pathname.includes('/add')) return 'Add Sale';
      if (pathname.includes('/edit')) return 'Edit Sale';
      if (pathname.includes('/details')) return 'Sale Details';
      return 'Sales';
    }

    // Masters with tab-based titles
    if (pathname.startsWith('/masters')) {
      const masterTitles = {
        'license-editions': 'License Editions',
        'designations': 'Designations',
        'tally-products': 'Tally Products',
        'staff-roles': 'Staff Roles',
        'executives': 'Executives',
        'industries': 'Industries',
        'areas': 'Areas',
        'nature-of-issues': 'Nature of Issues',
        'additional-services': 'Additional Services',
        'call-statuses': 'Call Statuses'
      };
      return masterTitles[tab] || 'Master Data';
    }

    // Reports
    if (pathname.startsWith('/reports')) {
      return 'Reports';
    }

    // Settings
    if (pathname.startsWith('/settings')) {
      return 'Settings';
    }

    // Profile
    if (pathname.startsWith('/profile')) {
      return 'Profile';
    }

    // Billing
    if (pathname.startsWith('/billing')) {
      if (pathname.includes('/plans')) return 'Subscription Plans';
      if (pathname.includes('/dashboard')) return 'Billing Dashboard';
      if (pathname.includes('/history')) return 'Billing History';
      if (pathname.includes('/success')) return 'Payment Success';
      return 'Billing';
    }

    // Default fallback
    return 'Dashboard';
  };

  const handleProfileClick = () => {
    console.log('🔄 Profile button clicked, navigating to /profile');
    navigate('/profile');
  };

  const handleSettingsClick = () => {
    navigate('/settings');
  };

  const handleLogoutClick = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <header className="bg-white border-b border-gray-200 px-4 h-16">
      <div className="flex items-center w-full h-full">
        {/* Mobile Menu Toggle */}
        <button
          className="inline-flex items-center justify-center p-0 mr-3 text-gray-600 hover:text-gray-900 md:hidden focus:outline-none focus-primary"
          onClick={onToggleSidebarMobile}
        >
          <i className="bi bi-list text-xl"></i>
        </button>

        {/* Desktop Menu Toggle */}
        <button
          className="hidden md:inline-flex items-center justify-center p-0 mr-3 text-gray-600 hover:text-gray-900 focus:outline-none focus-primary"
          onClick={onToggleSidebar}
        >
          <i className={cn('bi text-lg', sidebarCollapsed ? 'bi-chevron-right' : 'bi-chevron-left')}></i>
        </button>

        {/* Page Title */}
        <h6 className="text-lg font-semibold text-gray-900 flex-1">{getPageTitle()}</h6>

        {/* Right Side Items */}
        <div className="flex items-center ml-auto">
          {/* Notifications */}
          <div className="relative mr-3">
            <button
              className="relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 rounded"
              onClick={() => setNotificationsOpen(!notificationsOpen)}
            >
              <i className="bi bi-bell text-lg"></i>
              <span className="absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full">
                3
              </span>
            </button>

            {notificationsOpen && (
              <div className="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50">
                <div className="py-1">
                  <div className="px-4 py-2 text-sm font-semibold text-gray-900 border-b border-gray-200">
                    Notifications
                  </div>
                  <div className="px-4 py-3 hover:bg-gray-50">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <i className="bi bi-info-circle text-blue-500"></i>
                      </div>
                      <div className="ml-3 flex-1">
                        <h6 className="text-sm font-medium text-gray-900 mb-1">New customer registered</h6>
                        <p className="text-sm text-gray-600 mb-1">John Doe has registered as a new customer</p>
                        <small className="text-xs text-gray-500">2 minutes ago</small>
                      </div>
                    </div>
                  </div>
                  <div className="border-t border-gray-200"></div>
                  <div className="px-4 py-2 text-center">
                    <small className="text-sm text-gray-600">View all notifications</small>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* User Profile */}
          <div className="relative">
            <button
              className="flex items-center p-0 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 rounded"
              onClick={() => setUserMenuOpen(!userMenuOpen)}
            >
              <div className="flex items-center">
                <div className="mr-2 hidden sm:block text-right">
                  <div className="font-semibold text-gray-900">
                    {user ? `${user.first_name || ''} ${user.last_name || ''}`.trim() || user.email : 'User'}
                  </div>
                  <small className="text-gray-500">
                    {user?.roles?.[0]?.name || 'User'}
                  </small>
                </div>
                <div className="w-10 h-10 rounded-full bg-purple-600 flex items-center justify-center">
                  <i className="bi bi-person-fill text-white"></i>
                </div>
              </div>
            </button>

            {userMenuOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50">
                <div className="py-1">
                  {/* Profile button temporarily commented out due to 431 header size error */}
                  {/*
                  <button
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 focus:outline-none focus:bg-gray-50"
                    onClick={handleProfileClick}
                  >
                    <i className="bi bi-person mr-2"></i>
                    Profile
                  </button>
                  */}
                  <button
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 focus:outline-none focus:bg-gray-50"
                    onClick={handleSettingsClick}
                  >
                    <i className="bi bi-gear mr-2"></i>
                    Settings
                  </button>
                  <div className="border-t border-gray-100"></div>
                  <button
                    className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-50 focus:outline-none focus:bg-gray-50"
                    onClick={handleLogoutClick}
                  >
                    <i className="bi bi-box-arrow-right mr-2"></i>
                    Logout
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
