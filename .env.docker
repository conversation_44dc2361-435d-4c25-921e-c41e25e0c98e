# =================================
# TallyCRM Docker Environment Configuration
# =================================

# Database Configuration
DB_NAME=tallycrm_prod
DB_USERNAME=postgres
DB_PASSWORD=your_secure_postgres_password_here
DB_PORT=5432

# Redis Configuration
REDIS_PASSWORD=your_secure_redis_password_here
REDIS_PORT=6379

# Application Configuration
APP_PORT=8080
APP_URL=https://yourdomain.com

# JWT Configuration
JWT_SECRET=your_very_secure_jwt_secret_key_here_minimum_32_characters
JWT_REFRESH_SECRET=your_very_secure_jwt_refresh_secret_key_here_minimum_32_characters

# Stripe Configuration
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# Security Note:
# 1. Copy this file to .env.prod for production use
# 2. Replace all placeholder values with actual secure values
# 3. Never commit .env.prod to version control
# 4. Use strong, unique passwords for database and Redis
# 5. Generate secure JWT secrets (use: openssl rand -base64 32)
