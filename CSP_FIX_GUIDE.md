# Content Security Policy (CSP) Fix Guide

## 🚨 Issue Identified

**Error:** `Refused to connect to 'http://localhost:3002/api/v1/auth/login' because it violates the following Content Security Policy directive: "connect-src 'self'".`

## 🔍 Root Cause Analysis

1. **CSP Blocking**: The backend's Content Security Policy only allows connections to `'self'`
2. **Wrong API URL**: Frontend is trying to connect to `localhost:3002` instead of using same-origin
3. **Port Mismatch**: Container runs on port 8080, accessible via 5372, but frontend defaults to 3002

## ✅ Fixes Applied

### Fix 1: Updated Content Security Policy
**File:** `backend/src/server.js`

**Before:**
```javascript
connectSrc: ["'self'"],
```

**After:**
```javascript
connectSrc: ["'self'", "http://localhost:*", "https://localhost:*", "ws://localhost:*", "wss://localhost:*"],
```

This allows connections to any localhost port, fixing the CSP issue.

### Fix 2: Updated Frontend API URL Detection
**File:** `frontend/src/utils/constants.js`

**Changes:**
- Added Docker container port detection (5372, 8080)
- When running on these ports, uses relative path `/api/v1`
- Improved port mapping for development scenarios

### Fix 3: Updated Frontend Environment Template
**File:** `frontend/.env.example`

**Changes:**
- Commented out `VITE_API_BASE_URL` for production
- Added instructions for development vs production usage

## 🚀 Solution Steps

### Step 1: Rebuild the Docker Image
```bash
# Stop existing container
docker stop tallycrmv1 && docker rm tallycrmv1

# Rebuild with fixes
docker build -t cloudstier/tally-crm:v1 .
```

### Step 2: Create Proper Environment Files

**Create `frontend/.env`:**
```bash
cat > frontend/.env << 'EOF'
# Production: Use relative path (no VITE_API_BASE_URL)
VITE_APP_NAME=TallyCRM
VITE_API_TIMEOUT=30000
EOF
```

**Create `backend/.env`:**
```bash
cat > backend/.env << 'EOF'
NODE_ENV=production
PORT=8080
DB_HOST=your_database_host
DB_PORT=5432
DB_NAME=tallycrm_prod
DB_USERNAME=postgres
DB_PASSWORD=your_secure_password
JWT_SECRET=your_very_secure_jwt_secret_32_chars_minimum
JWT_REFRESH_SECRET=your_very_secure_refresh_secret_32_chars_minimum
ENABLE_CORS=false
APP_URL=http://localhost:5372
EOF
```

### Step 3: Run the Fixed Container
```bash
# Run with correct port mapping
docker run -d -p 5372:8080 --name tallycrmv1 cloudstier/tally-crm:v1
```

### Step 4: Verify the Fix
```bash
# Check container logs
docker logs tallycrmv1

# Test health endpoint
curl http://localhost:5372/health

# Test frontend
curl http://localhost:5372/

# Check API endpoint
curl http://localhost:5372/api/v1/health
```

## 🔍 Verification Commands

### Check API URL in Browser Console
Open browser console at `http://localhost:5372` and run:
```javascript
// Check what API URL is being used
console.log('API Base URL:', window.API_CONFIG?.BASE_URL || 'Not available');

// Check current location
console.log('Current URL:', window.location.href);
console.log('Port:', window.location.port);
```

### Check CSP Headers
```bash
# Check CSP headers
curl -I http://localhost:5372/

# Should show updated CSP with localhost:* allowed
```

### Test API Call from Browser
Open browser console and test:
```javascript
// Test API call
fetch('/api/v1/health')
  .then(response => response.json())
  .then(data => console.log('API Response:', data))
  .catch(error => console.error('API Error:', error));
```

## 🎯 Expected Results

After applying these fixes:

1. **Frontend loads successfully** at `http://localhost:5372`
2. **API calls use relative paths** (`/api/v1/auth/login` instead of `http://localhost:3002/api/v1/auth/login`)
3. **No CSP violations** in browser console
4. **Authentication works** without CORS issues

## 🔧 Alternative Solutions

### Option 1: Disable CSP (Not Recommended)
If you still have issues, you can temporarily disable CSP:

```javascript
// In backend/src/server.js
app.use(helmet({
  contentSecurityPolicy: false, // Disable CSP entirely
  crossOriginEmbedderPolicy: false,
}));
```

### Option 2: Set Explicit API URL
Force the frontend to use same-origin:

```bash
# In frontend/.env
echo "VITE_API_BASE_URL=/api/v1" > frontend/.env
```

### Option 3: Use Environment Variable Override
```bash
# Run container with API URL override
docker run -d -p 5372:8080 --name tallycrmv1 \
  -e VITE_API_BASE_URL=/api/v1 \
  cloudstier/tally-crm:v1
```

## 🚨 Troubleshooting

### If you still see CSP errors:
1. **Clear browser cache** and hard refresh (Ctrl+Shift+R)
2. **Check browser console** for the exact API URL being used
3. **Verify CSP headers** with `curl -I http://localhost:5372/`

### If API calls fail:
1. **Check backend logs**: `docker logs tallycrmv1`
2. **Verify API endpoint**: `curl http://localhost:5372/api/v1/health`
3. **Check database connection** in backend logs

### If frontend doesn't load:
1. **Check if static files are served**: `curl http://localhost:5372/`
2. **Verify container is running**: `docker ps | grep tallycrmv1`
3. **Check port mapping**: Should be `5372:8080`

## ✅ Success Indicators

- ✅ No CSP errors in browser console
- ✅ Frontend loads at `http://localhost:5372`
- ✅ API calls use relative paths (`/api/v1/...`)
- ✅ Authentication/login works
- ✅ No CORS errors

Your application should now work correctly without CSP violations!
