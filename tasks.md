🎉 ALL TASKS COMPLETED SUCCESSFULLY! 🎉
See tasks_completed.md for detailed completion status.

✅ task1: COMPLETED
adding new customer there is a TDL & Addons this is not input this wanna change like TSS if yes these inputs wanna show there expiry date so make the TDL & addons like that

✅ task2: COMPLETED
the additional features also like a TSS if the user check that ex: auto backup the input should shows like a TSS expiry date
i need like this for whatsapp telegram group, auto backup, cloud user, mobile app

✅ task3: COMPLETED
TSS, AMC, TDL&Addons,Additional Features
📱 WhatsApp/Telegram Group
💾 Auto Backup
☁️ Cloud User
📱 Mobile App these all move to single section if yes show the expiry date (Note: this is not AMC, AMC already perfect state just move to one section)

✅ task4: COMPLETED
in the customer address book section have these inputs
Contact #1
Type *

Select type
Contact Person *
Enter contact person name
Phone
Enter phone number
Email *
<EMAIL>
Mobile Numbers *
user should add the owner type row this is must if there is no owner selected pop will show "customer address book must add the owner type" if user can add one more type owner, accountant ..etc but every customer must have one owner

✅ task 5: COMPLETED
the location
1️⃣ Use Google Maps JavaScript API
You can leverage the Google Maps JavaScript API (specifically the Places library and Geolocation API) to:

Show a map in a modal or popup.

Let users pick a location (e.g. by clicking on the map or dragging a marker).

Retrieve the latitude and longitude.

2️⃣ Example Flow
Here’s the typical flow:

User clicks an <input> field.

A modal or popup opens with an embedded Google Map.

User selects a location by clicking on the map or dragging a marker.

You capture the latitude and longitude and populate the input field.

✅ task 6: COMPLETED
optimize the customer list table rows that's have a horizantal scrollbar in my laptop in 24 inch monitor ok but in laptop it have horizantal scrollbar it doesn't need please optimize that

✅ task7: COMPLETED
seach customers bar
if i try to search anything in that searchbar if i enter 'a' that's trying to search from db that good but instantly doing that and i can search using customer name,contact,tally version,status,services and customer customer tally serial number the result should comes here

✅ task 8: COMPLETED
in customer list table view actions button not properly working if i click the view button that's still loading the customer details page but not showing the data pleae check that

✅ task 9: COMPLETED
customer active inactive status need in the adding new customer place and editing customer pop up please