import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';
import { apiService } from '../services/api';
import { handleAuthError } from '../utils/authErrorHandler';
import { Card, CardHeader, CardBody, Table, TableHead, TableBody, TableRow, TableHeader, TableCell, Badge, Spinner, Alert, Button } from '../components/ui';
import LoadingScreen from '../components/ui/LoadingScreen';

const Dashboard = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    totalCustomers: 0,
    activeServices: 0,
    monthlyRevenue: 0,
    openServiceCalls: 0
  });
  const [recentCustomers, setRecentCustomers] = useState([]);
  const [recentServiceCalls, setRecentServiceCalls] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    period: '30d',
    dateFrom: null,
    dateTo: null,
    customDateRange: false
  });
  const [showPeriodDropdown, setShowPeriodDropdown] = useState(false);
  const [showDateRangeDropdown, setShowDateRangeDropdown] = useState(false);

  useEffect(() => {
    fetchDashboardData();
  }, [filters]); // Re-fetch when filters change

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.filter-dropdown')) {
        setShowPeriodDropdown(false);
        setShowDateRangeDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Fetching dashboard data with filters:', filters);

      // Build query parameters for filtering
      const queryParams = new URLSearchParams();

      // Add date filters
      if (filters.customDateRange && filters.dateFrom && filters.dateTo) {
        queryParams.append('dateFrom', filters.dateFrom);
        queryParams.append('dateTo', filters.dateTo);
      } else if (filters.period) {
        queryParams.append('period', filters.period);
      }

      const queryString = queryParams.toString();
      const dashboardQuery = queryString ? `?${queryString}` : '';
      const customersQuery = queryString ? `?${queryString}&limit=10&sort=created_at&order=desc` : '?limit=10&sort=created_at&order=desc';
      const serviceCallsQuery = queryString ? `?${queryString}&limit=10&sort=created_at&order=desc` : '?limit=10&sort=created_at&order=desc';

      // Fetch dashboard data from real API endpoints
      const [dashboardRes, customersRes, serviceCallsRes] = await Promise.all([
        apiService.get(`/dashboard/overview${dashboardQuery}`).catch((err) => {
          console.log('Dashboard overview API failed:', err.response?.status, err.message);
          return { data: { data: null } };
        }),
        apiService.get(`/customers${customersQuery}`).catch((err) => {
          console.log('Customers API failed:', err.response?.status, err.message);
          return { data: { data: { customers: [] } } };
        }),
        apiService.get(`/service-calls${serviceCallsQuery}`).catch((err) => {
          console.log('Service calls API failed:', err.response?.status, err.message);
          return { data: { data: { serviceCalls: [] } } };
        })
      ]);

      console.log('📊 API Responses:', {
        dashboard: dashboardRes.data,
        customers: customersRes.data,
        serviceCalls: serviceCallsRes.data
      });

      // Debug: Log the actual data structure
      console.log('🔍 Debug - Customers data structure:', customersRes.data?.data);
      console.log('🔍 Debug - Service calls data structure:', serviceCallsRes.data?.data);

      // Extract data from API responses
      const customers = customersRes.data?.data?.customers || [];
      const serviceCalls = serviceCallsRes.data?.data?.serviceCalls || [];

      // Use dashboard overview data if available, otherwise use calculated values
      const dashboardData = dashboardRes.data?.data;
      if (dashboardData) {
        setStats({
          totalCustomers: dashboardData.summary?.totalCustomers || 0,
          activeServices: dashboardData.summary?.totalServiceCalls || 0,
          monthlyRevenue: dashboardData.revenue?.salesTotal || 0,
          openServiceCalls: dashboardData.summary?.openServiceCalls || 0
        });

        // Set recent data from dashboard
        setRecentServiceCalls(dashboardData.recentActivity?.recentServiceCalls || serviceCalls.slice(0, 5));
      } else {
        // Use calculated values from individual API calls
        setStats({
          totalCustomers: customers.length,
          activeServices: serviceCalls.length,
          monthlyRevenue: 0, // Will be calculated from sales data when available
          openServiceCalls: serviceCalls.filter(sc =>
            sc.status?.category === 'open' ||
            sc.status?.name?.toLowerCase().includes('open') ||
            sc.status?.name?.toLowerCase().includes('pending')
          ).length
        });
      }

      // Set recent data
      setRecentCustomers(customers.slice(0, 10));
      setRecentServiceCalls(serviceCalls.slice(0, 10));

      console.log('✅ Dashboard data loaded successfully');
      console.log('📈 Final stats:', {
        totalCustomers: customers.length,
        totalServiceCalls: serviceCalls.length,
        recentCustomersCount: customers.slice(0, 10).length,
        recentServiceCallsCount: serviceCalls.slice(0, 10).length
      });

    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);

      // Handle authentication errors
      if (handleAuthError(error)) {
        return; // Auth error handled, component will unmount
      }

      setError('Failed to load dashboard data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Filter helper functions
  const periodOptions = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '3m', label: 'Last 3 months' },
    { value: '1y', label: 'Last 1 year' },
    { value: 'custom', label: 'Custom Range' }
  ];

  const getPeriodLabel = (period) => {
    const option = periodOptions.find(opt => opt.value === period);
    return option ? option.label : 'Last 30 days';
  };

  const getDateRangeLabel = () => {
    if (filters.customDateRange && filters.dateFrom && filters.dateTo) {
      const fromDate = new Date(filters.dateFrom).toLocaleDateString('en-IN', { month: 'short', day: 'numeric' });
      const toDate = new Date(filters.dateTo).toLocaleDateString('en-IN', { month: 'short', day: 'numeric', year: 'numeric' });
      return `${fromDate} - ${toDate}`;
    }

    const now = new Date();
    const period = filters.period;

    switch (period) {
      case '7d':
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        return `${weekAgo.toLocaleDateString('en-IN', { month: 'short', day: 'numeric' })} - ${now.toLocaleDateString('en-IN', { month: 'short', day: 'numeric' })}`;
      case '3m':
        const threeMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());
        return `${threeMonthsAgo.toLocaleDateString('en-IN', { month: 'short', year: 'numeric' })} - ${now.toLocaleDateString('en-IN', { month: 'short', year: 'numeric' })}`;
      case '1y':
        const yearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
        return `${yearAgo.toLocaleDateString('en-IN', { month: 'short', year: 'numeric' })} - ${now.toLocaleDateString('en-IN', { month: 'short', year: 'numeric' })}`;
      default: // 30d
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        return `${monthAgo.toLocaleDateString('en-IN', { month: 'short', day: 'numeric' })} - ${now.toLocaleDateString('en-IN', { month: 'short', day: 'numeric' })}`;
    }
  };

  const handlePeriodChange = (period) => {
    if (period === 'custom') {
      setFilters(prev => ({
        ...prev,
        period: '30d',
        customDateRange: true,
        dateFrom: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        dateTo: new Date().toISOString().split('T')[0]
      }));
    } else {
      setFilters(prev => ({
        ...prev,
        period,
        customDateRange: false,
        dateFrom: null,
        dateTo: null
      }));
    }
    setShowPeriodDropdown(false);
  };

  const handleDateRangeChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (loading) {
    return (
      <LoadingScreen
        title="Loading Dashboard..."
        subtitle="Fetching your business insights"
        variant="dashboard"
      />
    );
  }

  if (error) {
    return (
      <div className="w-full">
        <Alert variant="danger" className="mt-6">
          <h4 className="text-lg font-semibold mb-2">Error Loading Dashboard</h4>
          <p className="mb-3">{error}</p>
          <Button variant="outline" onClick={fetchDashboardData}>
            Try Again
          </Button>
        </Alert>
      </div>
    );
  }

  // Calculate percentage changes (mock data for now - in real app, compare with previous period)
  const calculateChange = (current, previous = 0) => {
    if (previous === 0) return '+0%';
    const change = ((current - previous) / previous) * 100;
    return `${change >= 0 ? '+' : ''}${change.toFixed(1)}%`;
  };

  const statsCards = [
    {
      title: 'Total Customers',
      value: stats.totalCustomers,
      icon: 'bi-people',
      color: 'purple',
      change: '+4.6%',
      changeType: 'positive',
      subtitle: 'vs last month'
    },
    {
      title: 'Active Services',
      value: stats.activeServices,
      icon: 'bi-tools',
      color: 'white',
      change: '+2.1%',
      changeType: 'positive',
      subtitle: 'vs last month'
    },
    {
      title: 'Monthly Revenue',
      value: formatCurrency(stats.monthlyRevenue),
      icon: 'bi-currency-rupee',
      color: 'white',
      change: '+8.2%',
      changeType: 'positive',
      subtitle: 'vs last month'
    }
  ];



  return (
    <>
      <Helmet>
        <title>Dashboard - TallyCRM</title>
      </Helmet>

      <div className="w-full">
        {/* Page Header */}
        <div className="mb-6 sm:mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div>
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mb-1 sm:mb-2">Dashboard</h1>
              <p className="text-gray-600 text-sm sm:text-base">Welcome back! Here's what's happening with your business today.</p>
            </div>
            <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
              {/* Period Filter Dropdown */}
              <div className="relative filter-dropdown">
                <div
                  className="flex items-center space-x-3 bg-white border border-gray-300 rounded-lg px-4 py-2 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => {
                    setShowPeriodDropdown(!showPeriodDropdown);
                    setShowDateRangeDropdown(false);
                  }}
                >
                  <i className="bi bi-clock text-purple-600 text-lg"></i>
                  <span className="text-gray-700 font-medium">{getPeriodLabel(filters.period)}</span>
                  <i className={`bi bi-chevron-down text-gray-400 transition-transform ${showPeriodDropdown ? 'rotate-180' : ''}`}></i>
                </div>

                {showPeriodDropdown && (
                  <div className="absolute top-full left-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                    {periodOptions.map((option) => (
                      <button
                        key={option.value}
                        className={`w-full text-left px-4 py-2 hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg transition-colors ${
                          filters.period === option.value && !filters.customDateRange ? 'bg-purple-50 text-purple-700' : 'text-gray-700'
                        }`}
                        onClick={() => handlePeriodChange(option.value)}
                      >
                        {option.label}
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Date Range Filter */}
              <div className="relative filter-dropdown">
                <div
                  className="flex items-center space-x-3 bg-white border border-gray-300 rounded-lg px-4 py-2 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => {
                    setShowDateRangeDropdown(!showDateRangeDropdown);
                    setShowPeriodDropdown(false);
                  }}
                >
                  <i className="bi bi-calendar-range text-purple-600 text-lg"></i>
                  <span className="text-gray-700 font-medium">{getDateRangeLabel()}</span>
                  <i className={`bi bi-chevron-down text-gray-400 transition-transform ${showDateRangeDropdown ? 'rotate-180' : ''}`}></i>
                </div>

                {showDateRangeDropdown && (
                  <div className="absolute top-full left-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50 p-4">
                    <div className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="customRange"
                          checked={filters.customDateRange}
                          onChange={(e) => {
                            if (e.target.checked) {
                              handlePeriodChange('custom');
                            } else {
                              handlePeriodChange('30d');
                            }
                          }}
                          className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                        />
                        <label htmlFor="customRange" className="text-sm font-medium text-gray-700">
                          Custom Date Range
                        </label>
                      </div>

                      {filters.customDateRange && (
                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <label className="block text-xs font-medium text-gray-700 mb-1">From Date</label>
                            <input
                              type="date"
                              value={filters.dateFrom || ''}
                              onChange={(e) => handleDateRangeChange('dateFrom', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-purple-500 focus:border-purple-500"
                            />
                          </div>
                          <div>
                            <label className="block text-xs font-medium text-gray-700 mb-1">To Date</label>
                            <input
                              type="date"
                              value={filters.dateTo || ''}
                              onChange={(e) => handleDateRangeChange('dateTo', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-purple-500 focus:border-purple-500"
                            />
                          </div>
                        </div>
                      )}

                      <div className="flex justify-end space-x-2 pt-2 border-t">
                        <button
                          onClick={() => setShowDateRangeDropdown(false)}
                          className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
                        >
                          Close
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Filter Status Indicator */}
              {(filters.customDateRange || filters.period !== '30d') && (
                <div className="flex items-center space-x-2 text-sm text-purple-600">
                  <i className="bi bi-funnel-fill"></i>
                  <span>Filtered</span>
                  <button
                    onClick={() => setFilters({ period: '30d', dateFrom: null, dateTo: null, customDateRange: false })}
                    className="text-purple-600 hover:text-purple-800 underline"
                  >
                    Clear
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
            {statsCards.map((stat, index) => (
              <Card key={index} className={`border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 ${
                stat.color === 'purple'
                  ? 'stats-card-primary'
                  : 'bg-white'
              }`}>
                <CardBody className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center">
                      <div className={`rounded-lg p-3 mr-3 ${
                        stat.color === 'purple'
                          ? ''
                          : 'white-stats-icon-bg'
                      }`} style={stat.color === 'purple' ? { backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)' } : {}}>
                        <i className={`bi ${stat.icon} text-2xl ${
                          stat.color === 'purple' ? '' : 'white-stats-icon-text'
                        }`} style={stat.color === 'purple' ? { color: 'var(--primary-text, #ffffff)' } : {}}></i>
                      </div>
                      <div>
                        <p className={`text-sm font-medium uppercase tracking-wide ${
                          stat.color === 'purple' ? '' : 'text-gray-600'
                        }`} style={stat.color === 'purple' ? { color: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)' } : {}}>
                          {stat.title}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <i className="bi bi-three-dots text-gray-400"></i>
                    </div>
                  </div>

                  <div className="mb-4">
                    <h3 className={`text-xl sm:text-2xl font-bold ${
                      stat.color === 'purple' ? '' : 'text-gray-900'
                    }`} style={stat.color === 'purple' ? { color: 'var(--primary-text, #ffffff)' } : {}}>
                      {stat.value}
                    </h3>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        stat.changeType === 'positive'
                          ? stat.color === 'purple'
                            ? 'bg-green-500/20 text-green-300'
                            : 'bg-green-100 text-green-600'
                          : stat.color === 'purple'
                            ? 'bg-red-500/20 text-red-300'
                            : 'bg-red-100 text-red-600'
                      }`}>
                        <i className={`bi ${stat.changeType === 'positive' ? 'bi-arrow-up' : 'bi-arrow-down'} mr-1`}></i>
                        {stat.change}
                      </div>
                    </div>
                    <p className={`text-xs ${
                      stat.color === 'purple' ? '' : 'text-gray-500'
                    }`} style={stat.color === 'purple' ? { color: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.7)' } : {}}>
                      {stat.subtitle}
                    </p>
                  </div>
                </CardBody>
              </Card>
            ))}
          </div>

          {/* Recent Customers and Service Calls Section */}

          <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6 lg:gap-8 mb-6 sm:mb-8">
            {/* Recent Customers */}
            <Card className="bg-white border-0 shadow-lg">
              <CardHeader className="border-b border-gray-200 px-6 py-4">
                <div className="flex items-center justify-between">
                  <h5 className="text-base sm:text-lg font-semibold text-gray-900 mb-0">Recent Customers</h5>
                  <span className="text-sm text-gray-500">See all</span>
                </div>
              </CardHeader>
              <CardBody className="p-0">
                <div className="overflow-x-auto">
                  <Table className="mb-0 text-xs sm:text-sm">
                    <TableHead className="bg-gray-50">
                      <TableRow>
                        <TableHeader className="text-left font-medium text-gray-600 uppercase text-xs tracking-wide px-3 sm:px-6 py-2 sm:py-3">Name</TableHeader>
                        <TableHeader className="text-left font-medium text-gray-600 uppercase text-xs tracking-wide px-3 sm:px-6 py-2 sm:py-3 hidden sm:table-cell">Email</TableHeader>
                        <TableHeader className="text-left font-medium text-gray-600 uppercase text-xs tracking-wide px-3 sm:px-6 py-2 sm:py-3">Phone</TableHeader>
                        <TableHeader className="text-left font-medium text-gray-600 uppercase text-xs tracking-wide px-3 sm:px-6 py-2 sm:py-3 hidden md:table-cell">Created</TableHeader>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {recentCustomers.length > 0 ? (
                        recentCustomers.slice(0, 5).map((customer, index) => (
                          <TableRow key={customer.id || index} className="hover:bg-gray-50 transition-colors border-b border-gray-100">
                            <TableCell className="px-3 sm:px-6 py-2 sm:py-4">
                              <div className="flex items-center">
                                <div className="w-6 h-6 sm:w-8 sm:h-8 white-stats-icon-bg rounded-full flex items-center justify-center mr-2 sm:mr-3 flex-shrink-0">
                                  <span className="white-stats-icon-text font-medium text-xs sm:text-sm">
                                    {(customer.company_name || customer.display_name || 'N')?.charAt(0)?.toUpperCase()}
                                  </span>
                                </div>
                                <span className="font-medium text-gray-900 text-xs sm:text-sm truncate">
                                  {customer.company_name || customer.display_name || 'Unknown Company'}
                                </span>
                              </div>
                            </TableCell>
                            <TableCell className="px-3 sm:px-6 py-2 sm:py-4 text-gray-600 text-xs sm:text-sm hidden sm:table-cell">{customer.email || 'N/A'}</TableCell>
                            <TableCell className="px-3 sm:px-6 py-2 sm:py-4 text-gray-600 text-xs sm:text-sm">{customer.phone || 'N/A'}</TableCell>
                            <TableCell className="px-3 sm:px-6 py-2 sm:py-4 text-gray-600 text-xs sm:text-sm hidden md:table-cell">{formatDate(customer.created_at)}</TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={4} className="text-center py-8 text-gray-500">
                            No customers found
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardBody>
            </Card>

            {/* Recent Service Calls */}
            <Card className="bg-white border-0 shadow-lg">
              <CardHeader className="border-b border-gray-200 px-6 py-4">
                <div className="flex items-center justify-between">
                  <h5 className="text-base sm:text-lg font-semibold text-gray-900 mb-0">Recent Service Calls</h5>
                  <span className="text-sm text-gray-500">See all</span>
                </div>
              </CardHeader>
              <CardBody className="p-0">
                <div className="overflow-x-auto">
                  <Table className="mb-0 text-xs sm:text-sm">
                    <TableHead className="bg-gray-50">
                      <TableRow>
                        <TableHeader className="text-left font-medium text-gray-600 uppercase text-xs tracking-wide px-3 sm:px-6 py-2 sm:py-3">Call #</TableHeader>
                        <TableHeader className="text-left font-medium text-gray-600 uppercase text-xs tracking-wide px-3 sm:px-6 py-2 sm:py-3">Customer</TableHeader>
                        <TableHeader className="text-left font-medium text-gray-600 uppercase text-xs tracking-wide px-3 sm:px-6 py-2 sm:py-3 hidden md:table-cell">Subject</TableHeader>
                        <TableHeader className="text-left font-medium text-gray-600 uppercase text-xs tracking-wide px-3 sm:px-6 py-2 sm:py-3 hidden sm:table-cell">Priority</TableHeader>
                        <TableHeader className="text-left font-medium text-gray-600 uppercase text-xs tracking-wide px-3 sm:px-6 py-2 sm:py-3">Status</TableHeader>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {recentServiceCalls.length > 0 ? (
                        recentServiceCalls.slice(0, 5).map((call, index) => (
                          <TableRow key={call.id || index} className="hover:bg-gray-50 transition-colors border-b border-gray-100">
                            <TableCell className="px-3 sm:px-6 py-2 sm:py-4">
                              <span className="font-medium text-gray-900 text-xs sm:text-sm">{call.call_number || `SC-${index + 1}`}</span>
                            </TableCell>
                            <TableCell className="px-3 sm:px-6 py-2 sm:py-4 text-gray-600 text-xs sm:text-sm">
                              <span className="truncate block max-w-[120px] sm:max-w-none">
                                {call.customer?.company_name || call.customer?.display_name || 'Unknown Customer'}
                              </span>
                            </TableCell>
                            <TableCell className="px-3 sm:px-6 py-2 sm:py-4 text-gray-600 text-xs sm:text-sm hidden md:table-cell">
                              <span className="truncate block max-w-[150px]">
                                {call.subject || 'No subject'}
                              </span>
                            </TableCell>
                            <TableCell className="px-3 sm:px-6 py-2 sm:py-4 hidden sm:table-cell">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                call.priority === 'high' || call.priority === 'critical'
                                  ? 'bg-red-100 text-red-600'
                                  : call.priority === 'medium'
                                  ? 'bg-yellow-100 text-yellow-600'
                                  : 'bg-green-100 text-green-600'
                              }`}>
                                {call.priority || 'Medium'}
                              </span>
                            </TableCell>
                            <TableCell className="px-3 sm:px-6 py-2 sm:py-4">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                call.status?.category === 'open' || call.status?.name?.toLowerCase().includes('open')
                                  ? 'bg-blue-100 text-blue-600'
                                  : call.status?.name?.toLowerCase().includes('completed')
                                  ? 'bg-green-100 text-green-600'
                                  : 'bg-gray-100 text-gray-600'
                              }`}>
                                {call.status?.name || 'Open'}
                              </span>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                            <div className="flex flex-col items-center">
                              <i className="bi bi-telephone text-4xl text-gray-300 mb-2"></i>
                              <span>No service calls found</span>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardBody>
            </Card>


          </div>

        {/* Quick Actions */}
        <Card className="border-0 shadow-lg mt-6 bg-gradient-to-r from-gray-50 to-gray-100">
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <h5 className="text-base sm:text-lg font-semibold text-gray-900 mb-0">Quick Actions</h5>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
              <Button
                className="w-full btn-primary border-0 shadow-md hover:shadow-lg transition-all duration-200"
                onClick={() => navigate('/customers/add')}
              >
                <i className="bi bi-person-plus mr-2"></i>
                Add New Customer
              </Button>
              <Button
                variant="outline"
                className="w-full border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400"
                onClick={() => navigate('/services/add')}
              >
                <i className="bi bi-tools mr-2"></i>
                Create Service Call
              </Button>
              <Button
                variant="outline"
                className="w-full border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400"
                onClick={() => navigate('/sales/add')}
              >
                <i className="bi bi-graph-up mr-2"></i>
                Record Sale
              </Button>
              <Button
                variant="outline"
                className="w-full border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400"
                onClick={() => navigate('/reports')}
              >
                <i className="bi bi-file-earmark-text mr-2"></i>
                Generate Report
              </Button>
            </div>
          </div>
        </Card>


      </div>
    </>
  );
};

export default Dashboard;
