{"name": "tallycrm-backend", "version": "1.0.0", "description": "Backend API for TallyCRM SaaS - CRM for Tally Resellers", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/", "migrate": "node src/migrations/migrate.js up", "migrate:down": "node src/migrations/migrate.js down", "seed": "node -e \"import('./src/seeders/001-seed-master-data.js').then(m => m.default())\"", "seed:admin": "node -e \"import('./src/seeders/000-seed-admin-user.js').then(m => m.default())\"", "seed:plans": "node -e \"import('./src/seeders/002-seed-subscription-plans.js').then(m => m.default())\"", "seed:master": "node -e \"import('./src/seeders/003-seed-master-data.js').then(m => m.default())\"", "seed:sample": "node -e \"import('./src/seeders/004-seed-sample-data.js').then(m => m.default())\"", "seed:all": "node seed-all.js", "db:setup": "npm run migrate && npm run seed:all", "db:reset": "npm run migrate:down 20 && npm run migrate && npm run seed:all", "db:push": "npm run migrate && npm run seed", "db:create": "sequelize-cli db:create", "db:drop": "sequelize-cli db:drop", "build": "node build.js", "validate": "npm run lint && npm run test", "cleanup:production": "node cleanup-test-files.js", "production:setup": "npm run migrate && npm run seed:all && npm run cleanup:production", "setup:fresh": "node setup-fresh-database.js"}, "keywords": ["crm", "tally", "saas", "express", "nodejs", "postgresql", "jwt", "rbac"], "author": "Cloudstier Solutions", "license": "PROPRIETARY", "dependencies": {"bcrypt": "6.0.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "connect-session-sequelize": "^7.1.7", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "node-fetch": "3.3.2", "nodemailer": "^6.9.7", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.35.2", "sharp": "^0.32.6", "sqlite3": "5.1.7", "stripe": "^14.12.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "tallycrm-workspace": "file:..", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "xlsx": "^0.18.5"}, "devDependencies": {"cross-env": "^7.0.3", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-node": "^11.1.0", "husky": "^8.0.3", "jest": "^29.7.0", "lint-staged": "^15.2.0", "nodemon": "^3.0.2", "prettier": "^3.1.1", "sequelize-cli": "^6.6.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/cloudstier/tallycrm.git"}, "bugs": {"url": "https://github.com/cloudstier/tallycrm/issues"}, "homepage": "https://github.com/cloudstier/tallycrm#readme", "lint-staged": {"*.js": ["eslint --fix", "prettier --write"]}}