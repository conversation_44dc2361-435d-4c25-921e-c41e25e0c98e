{"name": "tallycrm-workspace", "version": "1.0.0", "description": "TallyCRM SaaS - Complete workspace for frontend and backend", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"install:all": "npm install && npm run install:frontend && npm run install:backend", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm run test", "test:backend": "cd backend && npm run test", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "lint:fix": "npm run lint:fix:frontend && npm run lint:fix:backend", "lint:fix:frontend": "cd frontend && npm run lint:fix", "lint:fix:backend": "cd backend && npm run lint:fix", "format": "npm run format:frontend && npm run format:backend", "format:frontend": "cd frontend && npm run format", "format:backend": "cd backend && npm run format", "clean": "npm run clean:frontend && npm run clean:backend", "clean:frontend": "cd frontend && rm -rf node_modules dist", "clean:backend": "cd backend && rm -rf node_modules build", "reset": "npm run clean && npm run install:all", "db:setup": "cd backend && npm run db:create && npm run migrate && npm run seed", "db:reset": "cd backend && npm run db:drop && npm run db:create && npm run migrate && npm run seed", "docker:dev": "docker-compose up -d", "docker:dev:build": "docker-compose up -d --build", "docker:dev:down": "docker-compose down", "docker:dev:logs": "docker-compose logs -f", "docker:prod": "docker-compose -f docker-compose.prod.yml up -d", "docker:prod:build": "docker-compose -f docker-compose.prod.yml up -d --build", "docker:prod:down": "docker-compose -f docker-compose.prod.yml down", "docker:build": "docker build -t tallycrm:latest .", "docker:run": "docker run -p 8080:8080 --env-file .env.docker tallycrm:latest", "docker:test": "node scripts/docker-test.js", "docker:deploy": "bash scripts/deploy-production.sh", "prepare": "husky install"}, "keywords": ["crm", "tally", "saas", "react", "nodejs", "express", "postgresql", "workspace"], "author": "Cloudstier Solutions", "license": "PROPRIETARY", "devDependencies": {"concurrently": "^8.2.2", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/cloudstier/tallycrm.git"}, "bugs": {"url": "https://github.com/cloudstier/tallycrm/issues"}, "homepage": "https://github.com/cloudstier/tallycrm#readme"}