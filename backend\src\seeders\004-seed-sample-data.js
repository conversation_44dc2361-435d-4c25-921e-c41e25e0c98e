#!/usr/bin/env node

/**
 * Sample Data Seeder
 * Seeds sample customers, service calls, and sales data
 *
 * FIXED: CustomerContact validation errors
 * - Added proper first_name and last_name fields (required)
 * - Removed invalid is_primary field
 * - Added proper contact type fields (is_decision_maker, is_billing_contact, is_technical_contact)
 */

import models from '../models/index.js';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger.js';

const seedSampleData = async () => {
  try {
    logger.info('🌱 Seeding sample data...');

    // Get any available tenant (prefer demo, fallback to default, then any active tenant)
    let tenant = await models.Tenant.findOne({ where: { slug: 'demo' } });

    if (!tenant) {
      tenant = await models.Tenant.findOne({ where: { slug: 'default' } });
    }

    if (!tenant) {
      tenant = await models.Tenant.findOne({
        where: { is_active: true },
        order: [['created_at', 'ASC']]
      });
    }

    if (!tenant) {
      logger.warn('⚠️ No tenant found for sample data. Skipping sample data seeding.');
      return;
    }

    logger.info(`📋 Using tenant: ${tenant.name} (${tenant.slug}) for sample data`);

    // Get master data for references
    const industries = await models.Industry.findAll();
    const areas = await models.Area.findAll();
    const licenseEditions = await models.LicenseEdition.findAll();
    const tallyProducts = await models.TallyProduct.findAll();
    const executives = await models.Executive.findAll({ where: { tenant_id: tenant.id } });
    const designations = await models.Designation.findAll();

    // Check if we have minimum required master data
    if (industries.length === 0 || areas.length === 0 || licenseEditions.length === 0 ||
        tallyProducts.length === 0 || executives.length === 0 || designations.length === 0) {
      logger.warn('⚠️ Missing master data required for sample data. Please run master data seeding first.');
      logger.info('📊 Available data:');
      logger.info(`   Industries: ${industries.length}`);
      logger.info(`   Areas: ${areas.length}`);
      logger.info(`   License Editions: ${licenseEditions.length}`);
      logger.info(`   Tally Products: ${tallyProducts.length}`);
      logger.info(`   Executives: ${executives.length}`);
      logger.info(`   Designations: ${designations.length}`);
      return;
    }

    // Sample Customers
    const sampleCustomers = [
      {
        id: uuidv4(),
        tenant_id: tenant.id,
        customer_code: 'CUST001',
        company_name: 'ABC Manufacturing Pvt Ltd',
        display_name: 'ABC Manufacturing',
        tally_serial_number: 'TLY001234',
        gst_number: '27**********1Z5',
        pan_number: '**********',
        industry_id: industries.find(i => i.code === 'MFG')?.id || industries[0]?.id,
        area_id: areas.find(a => a.code === 'MUM')?.id || areas[0]?.id,
        customer_type: 'customer',
        business_type: 'private_limited',
        assigned_executive_id: executives[0]?.id,
        address_line_1: '123 Industrial Area, Andheri East',
        city: 'Mumbai',
        state: 'Maharashtra',
        postal_code: '400069',
        country: 'India',
        phone: '+91-22-12345678',
        email: '<EMAIL>',
        website: 'www.abcmanufacturing.com',
        contact_person: 'Rajesh Kumar',
        alternate_phone: '+91-**********',
        notes: 'Key customer with multiple locations',
        tally_version: 'ERP 9 Release 6.6.3',
        license_type: 'Gold Multi-User'
      },
      {
        id: uuidv4(),
        tenant_id: tenant.id,
        customer_code: 'CUST002',
        company_name: 'XYZ Trading Company',
        display_name: 'XYZ Trading',
        tally_serial_number: 'TLY005678',
        gst_number: '29**********1Z2',
        pan_number: '**********',
        industry_id: industries.find(i => i.code === 'TRD')?.id || industries[1]?.id || industries[0]?.id,
        area_id: areas.find(a => a.code === 'BLR')?.id || areas[1]?.id || areas[0]?.id,
        customer_type: 'customer',
        business_type: 'partnership',
        assigned_executive_id: executives[1]?.id || executives[0]?.id,
        address_line_1: '456 Commercial Street, Koramangala',
        city: 'Bangalore',
        state: 'Karnataka',
        postal_code: '560034',
        country: 'India',
        phone: '+91-80-87654321',
        email: '<EMAIL>',
        website: 'www.xyztrading.com',
        contact_person: 'Priya Sharma',
        alternate_phone: '+91-**********',
        notes: 'Growing business, potential for upgrade',
        tally_version: 'TallyPrime Release 3.0',
        license_type: 'Silver Single-User'
      },
      {
        id: uuidv4(),
        tenant_id: tenant.id,
        customer_code: 'CUST003',
        company_name: 'Tech Solutions India',
        display_name: 'Tech Solutions',
        tally_serial_number: 'TLY009012',
        gst_number: '07**********1Z8',
        pan_number: '**********',
        industry_id: industries.find(i => i.code === 'IT')?.id || industries[2]?.id || industries[0]?.id,
        area_id: areas.find(a => a.code === 'DEL')?.id || areas[2]?.id || areas[0]?.id,
        customer_type: 'customer',
        business_type: 'private_limited',
        assigned_executive_id: executives[0]?.id,
        address_line_1: '789 Cyber City, Sector 24',
        city: 'Gurgaon',
        state: 'Haryana',
        postal_code: '122002',
        country: 'India',
        phone: '+91-124-4567890',
        email: '<EMAIL>',
        website: 'www.techsolutions.in',
        contact_person: 'Amit Patel',
        alternate_phone: '+91-**********',
        notes: 'IT company with advanced requirements',
        tally_version: 'TallyPrime Release 3.0',
        license_type: 'Prime Gold Multi-User'
      }
    ];

    const createdCustomers = [];
    for (const customer of sampleCustomers) {
      const [createdCustomer] = await models.Customer.findOrCreate({
        where: {
          tally_serial_number: customer.tally_serial_number,
          tenant_id: tenant.id
        },
        defaults: customer
      });
      createdCustomers.push(createdCustomer);
    }

    // Sample Customer Contacts
    for (let i = 0; i < createdCustomers.length; i++) {
      const customer = createdCustomers[i];

      // Parse contact person name into first and last name
      const contactPersonParts = customer.contact_person ? customer.contact_person.split(' ') : ['Contact', 'Person'];
      const firstName = contactPersonParts[0] || 'Contact';
      const lastName = contactPersonParts.slice(1).join(' ') || 'Person';

      const contactData = [
        {
          id: uuidv4(),
          customer_id: customer.id,
          designation_id: designations.find(d => d.code === 'MD')?.id || designations[0]?.id,
          contact_type: 'primary',
          first_name: firstName,
          last_name: lastName,
          phone: customer.alternate_phone || customer.phone,
          email: customer.email,
          is_decision_maker: true,
          is_billing_contact: true,
          is_technical_contact: false,
          country: 'India',
          preferred_communication: 'email',
          is_active: true
        },
        {
          id: uuidv4(),
          customer_id: customer.id,
          designation_id: designations.find(d => d.code === 'ACC')?.id || designations[1]?.id || designations[0]?.id,
          contact_type: 'accounts',
          first_name: 'Accounts',
          last_name: `Manager ${i + 1}`,
          phone: `+91-98765${4321 + i}`,
          email: `accounts${i + 1}@${customer.email.split('@')[1]}`,
          is_decision_maker: false,
          is_billing_contact: false,
          is_technical_contact: false,
          country: 'India',
          preferred_communication: 'phone',
          is_active: true
        }
      ];

      for (const contact of contactData) {
        await models.CustomerContact.findOrCreate({
          where: { customer_id: customer.id, email: contact.email },
          defaults: contact
        });
      }
    }

    logger.info('✅ Sample data seeded successfully');
    logger.info(`📊 Created ${createdCustomers.length} sample customers with contacts for tenant: ${tenant.name}`);

  } catch (error) {
    logger.error('❌ Error seeding sample data:', error);
    throw error;
  }
};

export default seedSampleData;
