import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { apiService } from '../../services/api';
import ResponsiveTable from '../../components/ui/ResponsiveTable';
import useAppStore from '../../store/appStore';
import {
  FaPlus,
  FaEdit,
  FaEye,
  FaTrash,
  FaSearch,
  FaFilter,
  FaDownload,
  FaPhone,
  FaEnvelope,
  FaMapMarkerAlt,
  FaUsers,
  FaCheckCircle,
  FaClock,
  FaRupeeSign
} from 'react-icons/fa';
import LoadingScreen from '../../components/ui/LoadingScreen';

const CustomerList = () => {
  const navigate = useNavigate();
  const { updateMasterCounts } = useAppStore();
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [customersPerPage] = useState(10);

  // Fetch customers data from API - Combined useEffect with proper dependencies
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchCustomersData();
    }, searchTerm ? 500 : 0); // Debounce only for search, immediate for other changes

    return () => clearTimeout(timeoutId);
  }, [searchTerm, filterStatus, currentPage]); // Removed duplicate useEffect

  const fetchCustomersData = async () => {
    try {
      setLoading(true);
      const response = await apiService.get('/customers', {
        params: {
          page: currentPage,
          limit: customersPerPage,
          ...(searchTerm && { search: searchTerm }),
          ...(filterStatus !== 'all' && { isActive: filterStatus === 'active' }),
        }
      });

      if (response.data?.success) {
        const customersData = response.data.data.customers || [];
        // Transform API data to match frontend expectations
        const transformedCustomers = customersData.map(customer => ({
          id: customer.id,
          name: customer.company_name || customer.display_name || 'Unknown Company',
          contactPerson: customer.contact_person || 'N/A',
          email: customer.email || 'N/A',
          phone: customer.phone || 'N/A',
          city: customer.city || 'N/A',
          state: customer.state || 'N/A',
          status: customer.is_active ? 'active' : 'inactive',
          customerType: customer.customer_type || 'prospect', // Keep customer type separate
          tallyVersion: 'Prime', // This would come from customer's Tally license info
          lastContact: customer.last_contact_date || customer.updatedAt || 'N/A',
          totalServices: 0, // This would come from service calls count
          pendingAmount: customer.pending_amount || 0
        }));

        setCustomers(transformedCustomers);

        // Update customer count in the store
        updateMasterCounts({ customers: transformedCustomers.length });
      } else {
        console.error('Failed to fetch customers data:', response.data?.message);
        setCustomers([]);
        updateMasterCounts({ customers: 0 });
      }
    } catch (error) {
      console.error('Error fetching customers data:', error);
      toast.error('Failed to load customers data');
      setCustomers([]);
      updateMasterCounts({ customers: 0 });
    } finally {
      setLoading(false);
    }
  };

  // Filter customers based on search and status
  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || customer.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  // Calculate stats
  const totalRevenue = customers.reduce((sum, customer) => sum + (customer.pendingAmount || 0), 0);
  const activeCustomers = customers.filter(c => c.status === 'active').length;
  const inactiveCustomers = customers.filter(c => c.status === 'inactive').length;

  // Pagination
  const indexOfLastCustomer = currentPage * customersPerPage;
  const indexOfFirstCustomer = indexOfLastCustomer - customersPerPage;
  const currentCustomers = filteredCustomers.slice(indexOfFirstCustomer, indexOfLastCustomer);
  const totalPages = Math.ceil(filteredCustomers.length / customersPerPage);

  const handleDelete = async (customerId) => {
    if (window.confirm('Are you sure you want to delete this customer?')) {
      try {
        const response = await apiService.delete(`/customers/${customerId}`);
        if (response.data?.success) {
          setCustomers(customers.filter(customer => customer.id !== customerId));
          toast.success('Customer deleted successfully');
        } else {
          toast.error(response.data?.message || 'Failed to delete customer');
        }
      } catch (error) {
        console.error('Error deleting customer:', error);
        toast.error('Failed to delete customer');
      }
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      'active': { bg: 'bg-gradient-to-r from-green-100 to-emerald-100', text: 'text-green-800', icon: '✅' },
      'inactive': { bg: 'bg-gradient-to-r from-gray-100 to-slate-100', text: 'text-gray-800', icon: '❌' }
    };

    const config = statusConfig[status] || statusConfig['inactive'];
    const displayText = status.charAt(0).toUpperCase() + status.slice(1);

    return (
      <span className={`inline-flex items-center px-3 py-1.5 text-xs font-bold rounded-full shadow-sm ${config.bg} ${config.text} border border-opacity-20`}>
        <span className="mr-1">{config.icon}</span>
        {displayText}
      </span>
    );
  };

  const handleExport = () => {
    try {
      // Prepare data for export
      const exportData = filteredCustomers.map(customer => ({
        'Customer Name': customer.name,
        'Contact Person': customer.contactPerson,
        'Email': customer.email,
        'Phone': customer.phone,
        'City': customer.city,
        'State': customer.state,
        'Status': customer.status,
        'Tally Version': customer.tallyVersion,
        'Total Services': customer.totalServices,
        'Pending Amount': customer.pendingAmount,
        'Last Contact': customer.lastContact
      }));

      // Convert to CSV
      const headers = Object.keys(exportData[0] || {});
      const csvContent = [
        headers.join(','),
        ...exportData.map(row =>
          headers.map(header =>
            `"${(row[header] || '').toString().replace(/"/g, '""')}"`
          ).join(',')
        )
      ].join('\n');

      // Download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `customers_export_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success(`Exported ${exportData.length} customers successfully`);
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export customers');
    }
  };

  if (loading) {
    return (
      <LoadingScreen
        title="Loading Customers..."
        subtitle="Please wait while we fetch your customer data"
        variant="page"
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100">
      <div className="w-full">
        {/* Colorful Header */}
        <div className="mb-8">
          <div className="header-gradient rounded-2xl shadow-xl p-6 text-white">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
              <div>
                <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-2 flex items-center">
                  <div className="w-12 h-12 rounded-full flex items-center justify-center mr-4" style={{ backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)' }}>
                    <FaUsers className="text-xl" style={{ color: 'var(--primary-text, #ffffff)' }} />
                  </div>
                  Customer Management
                </h2>
                <p className="text-sm sm:text-base" style={{ color: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)' }}>Manage your customer database efficiently</p>
              </div>
              <div className="flex flex-col sm:flex-row gap-3">
                <button
                  className="inline-flex items-center justify-center px-6 py-3 border-2 border-opacity-30 text-sm font-medium rounded-xl bg-opacity-10 hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-all duration-200 backdrop-blur-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{
                    borderColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.3)',
                    color: 'var(--primary-text, #ffffff)',
                    backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.1)',
                    '--tw-ring-color': 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.5)'
                  }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)'}
                  onMouseLeave={(e) => e.target.style.backgroundColor = 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.1)'}
                  onClick={handleExport}
                  disabled={filteredCustomers.length === 0}
                >
                  <FaDownload className="mr-2" />
                  Export ({filteredCustomers.length})
                </button>
                <Link
                  to="/customers/add"
                  className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-sm font-medium rounded-xl focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-all duration-200 shadow-lg"
                  style={{
                    backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.95)',
                    color: 'var(--primary-color, #7c3aed)',
                    backdropFilter: 'blur(10px)'
                  }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.85)'}
                  onMouseLeave={(e) => e.target.style.backgroundColor = 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.95)'}
                >
                  <FaPlus className="mr-2" />
                  Add Customer
                </Link>
              </div>
            </div>
          </div>
        </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="stats-card-primary rounded-lg shadow-lg hover:shadow-xl transition-all duration-300">
          <div className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="text-xl sm:text-2xl font-bold mb-0">{customers.length}</h4>
                <p className="mb-0 text-sm" style={{ color: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)' }}>Total Customers</p>
              </div>
              <div className="rounded-lg p-3" style={{ backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)' }}>
                <FaUsers className="h-6 w-6" style={{ color: 'var(--primary-text, #ffffff)' }} />
              </div>
            </div>
            <div className="mt-3 flex items-center">
              <div className="bg-green-500/20 text-green-300 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                <i className="bi bi-arrow-up mr-1"></i>
                +12%
              </div>
              <span className="text-white/70 text-xs ml-2">vs last month</span>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
          <div className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="text-xl sm:text-2xl font-bold mb-0 text-gray-900">{activeCustomers}</h4>
                <p className="text-gray-600 mb-0 text-sm">Active Customers</p>
              </div>
              <div className="white-stats-icon-bg rounded-lg p-3">
                <FaCheckCircle className="h-6 w-6 white-stats-icon-text" />
              </div>
            </div>
            <div className="mt-3 flex items-center">
              <div className="bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                <i className="bi bi-arrow-up mr-1"></i>
                +8%
              </div>
              <span className="text-gray-500 text-xs ml-2">vs last month</span>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
          <div className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="text-xl sm:text-2xl font-bold mb-0 text-gray-900">{inactiveCustomers}</h4>
                <p className="text-gray-600 mb-0 text-sm">Inactive Customers</p>
              </div>
              <div className="white-stats-icon-bg rounded-lg p-3">
                <FaClock className="h-6 w-6 white-stats-icon-text" />
              </div>
            </div>
            <div className="mt-3 flex items-center">
              <div className="bg-red-100 text-red-600 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                <i className="bi bi-arrow-down mr-1"></i>
                -3%
              </div>
              <span className="text-gray-500 text-xs ml-2">vs last month</span>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
          <div className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="text-xl sm:text-2xl font-bold mb-0 text-gray-900">₹{totalRevenue.toLocaleString()}</h4>
                <p className="text-gray-600 mb-0 text-sm">Total Revenue</p>
              </div>
              <div className="white-stats-icon-bg rounded-lg p-3">
                <FaRupeeSign className="h-6 w-6 white-stats-icon-text" />
              </div>
            </div>
            <div className="mt-3 flex items-center">
              <div className="bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                <i className="bi bi-arrow-up mr-1"></i>
                +15%
              </div>
              <span className="text-gray-500 text-xs ml-2">vs last month</span>
            </div>
          </div>
        </div>
      </div>

        {/* Enhanced Filters */}
        <div className="bg-white rounded-2xl shadow-xl p-6 mb-8 border border-gray-100">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="sm:col-span-2">
              <label className="block text-sm font-bold text-gray-700 mb-2">🔍 Search Customers</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <FaSearch className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  className="block w-full pl-12 pr-4 py-3 border-2 border-gray-200 rounded-xl leading-5 bg-white placeholder-gray-400 focus:outline-none focus:placeholder-gray-300 focus:ring-2 focus:border-gray-500 transition-all duration-200 sm:text-sm"
                  style={{ '--tw-ring-color': 'var(--primary-color)', 'borderColor': 'var(--primary-color)' }}
                  placeholder="Search by name, email, phone..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-bold text-blue-700 mb-2">📊 Status Filter</label>
              <select
                className="block w-full px-4 py-3 border-2 border-blue-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white hover:border-blue-300 transition-all duration-200 sm:text-sm"
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <option value="all">🌟 All Status</option>
                <option value="active">✅ Active</option>
                <option value="inactive">❌ Inactive</option>
              </select>
            </div>
            <div className="flex items-end">
              <button className="w-full inline-flex items-center justify-center px-4 py-3 border-2 border-orange-200 rounded-xl shadow-sm text-sm font-medium text-orange-700 bg-white hover:bg-orange-50 hover:border-orange-300 focus:outline-none focus:ring-2 focus:ring-orange-500 transition-all duration-200">
                <FaFilter className="mr-2 h-4 w-4" />
                More Filters
              </button>
            </div>
          </div>
        </div>

      {/* Customer Table - Optimized for responsiveness (Task 6) */}
      <ResponsiveTable
        columns={[
          {
            header: 'Customer',
            key: 'name',
            className: 'min-w-0 w-1/4',
            render: (customer) => (
              <div className="min-w-0">
                <div className="text-xs font-medium text-gray-900 truncate">{customer.name}</div>
                <div className="text-xs text-gray-500 truncate">{customer.contactPerson}</div>
              </div>
            )
          },
          {
            header: 'Contact',
            key: 'contact',
            className: 'min-w-0 w-1/5',
            render: (customer) => (
              <div className="min-w-0">
                <div className="flex items-center mb-1">
                  <FaPhone className="text-gray-400 mr-1 h-2 w-2 flex-shrink-0" />
                  <span className="text-xs text-gray-900 truncate">{customer.phone}</span>
                </div>
                <div className="flex items-center">
                  <FaEnvelope className="text-gray-400 mr-1 h-2 w-2 flex-shrink-0" />
                  <span className="text-xs text-gray-900 truncate">{customer.email}</span>
                </div>
              </div>
            )
          },
          {
            header: 'Location',
            key: 'location',
            className: 'min-w-0 w-1/6',
            render: (customer) => (
              <div className="flex items-center min-w-0">
                <FaMapMarkerAlt className="text-gray-400 mr-1 h-2 w-2 flex-shrink-0" />
                <span className="text-xs text-gray-900 truncate">{customer.city}</span>
              </div>
            )
          },
          {
            header: 'Version',
            key: 'tallyVersion',
            className: 'w-16',
            render: (customer) => (
              <span className="inline-flex px-1 py-1 text-xs font-semibold rounded-full bg-info-100 text-info-800">
                {customer.tallyVersion}
              </span>
            )
          },
          {
            header: 'Status',
            key: 'status',
            className: 'w-20',
            render: (customer) => getStatusBadge(customer.status)
          },
          {
            header: 'Svc',
            key: 'totalServices',
            className: 'w-12 text-center',
            render: (customer) => (
              <span className="inline-flex px-1 py-1 text-xs font-semibold rounded-full bg-primary-100 text-primary-800">
                {customer.totalServices}
              </span>
            )
          },
          {
            header: 'Amount',
            key: 'pendingAmount',
            className: 'w-20 text-right',
            render: (customer) => (
              <span className={`text-xs font-bold ${customer.pendingAmount > 0 ? 'text-danger-600' : 'text-success-600'}`}>
                ₹{customer.pendingAmount ? customer.pendingAmount.toLocaleString('en-IN', { maximumFractionDigits: 0 }) : '0'}
              </span>
            )
          },
          {
            header: 'Actions',
            key: 'actions',
            className: 'w-24',
            render: (customer) => (
              <div className="flex space-x-1">
                <button
                  className="p-1 rounded hover:bg-gray-50 transition-colors"
                  style={{ color: 'var(--primary-color)' }}
                  onClick={() => navigate(`/customers/${customer.id}`)}
                  title="View Details"
                >
                  <FaEye className="h-3 w-3" />
                </button>
                <button
                  className="text-gray-600 hover:text-gray-900 p-1 rounded hover:bg-gray-50"
                  onClick={() => navigate(`/customers/${customer.id}/edit`)}
                  title="Edit"
                >
                  <FaEdit className="h-3 w-3" />
                </button>
                <button
                  className="text-danger-600 hover:text-danger-900 p-1 rounded hover:bg-danger-50"
                  onClick={() => handleDelete(customer.id)}
                  title="Delete"
                >
                  <FaTrash className="h-3 w-3" />
                </button>
              </div>
            )
          }
        ]}
        data={currentCustomers}
        loading={loading}
        emptyMessage="No customers found. Start by adding your first customer."
        mobileCardRender={(customer) => (
          <div className="space-y-3">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-lg font-medium text-gray-900">{customer.name}</h3>
                <p className="text-sm text-gray-500">{customer.contactPerson}</p>
              </div>
              {getStatusBadge(customer.status)}
            </div>

            <div className="grid grid-cols-2 gap-3 text-sm">
              <div>
                <span className="text-gray-500">Phone:</span>
                <p className="font-medium">{customer.phone}</p>
              </div>
              <div>
                <span className="text-gray-500">Location:</span>
                <p className="font-medium">{customer.city}, {customer.state}</p>
              </div>
              <div>
                <span className="text-gray-500">Services:</span>
                <p className="font-medium">{customer.totalServices}</p>
              </div>
              <div>
                <span className="text-gray-500">Pending:</span>
                <p className={`font-medium ${customer.pendingAmount > 0 ? 'text-danger-600' : 'text-success-600'}`}>
                  ₹{customer.pendingAmount.toLocaleString()}
                </p>
              </div>
            </div>

            <div className="flex space-x-2 pt-2 border-t border-gray-100">
              <button
                className="flex-1 btn-primary px-3 py-2 rounded text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200"
                onClick={() => navigate(`/customers/${customer.id}`)}
              >
                View Details
              </button>
              <button
                className="px-3 py-2 border border-gray-300 rounded text-sm font-medium text-gray-700 hover:bg-gray-50"
                onClick={() => navigate(`/customers/${customer.id}/edit`)}
              >
                Edit
              </button>
            </div>
          </div>
        )}
      />

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 px-4 py-3 mt-4">
          <div className="flex flex-col sm:flex-row items-center justify-between space-y-3 sm:space-y-0">
            <div className="text-sm text-gray-700 text-center sm:text-left">
              Showing page <span className="font-medium">{currentPage}</span> of{' '}
              <span className="font-medium">{totalPages}</span>
              {' '}({filteredCustomers.length} total customers)
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <i className="bi bi-chevron-left mr-1"></i>
                Previous
              </button>

              {/* Page numbers - show limited on mobile */}
              <div className="hidden sm:flex items-center space-x-1">
                {[...Array(Math.min(totalPages, 5))].map((_, index) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = index + 1;
                  } else if (currentPage <= 3) {
                    pageNum = index + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + index;
                  } else {
                    pageNum = currentPage - 2 + index;
                  }

                  return (
                    <button
                      key={pageNum}
                      onClick={() => setCurrentPage(pageNum)}
                      className={`relative inline-flex items-center px-3 py-2 border text-sm font-medium rounded-md ${
                        currentPage === pageNum
                          ? 'btn-primary border-transparent'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
              </div>

              {/* Mobile page indicator */}
              <div className="sm:hidden px-3 py-2 text-sm text-gray-500">
                {currentPage} / {totalPages}
              </div>

              <button
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
                <i className="bi bi-chevron-right ml-1"></i>
              </button>
            </div>
          </div>
        </div>
      )}
      </div>
    </div>
  );
};

export default CustomerList;
