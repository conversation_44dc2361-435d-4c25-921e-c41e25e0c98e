import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const TallyProduct = sequelize.define('TallyProduct', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 100],
      },
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        len: [2, 20],
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    category: {
      type: DataTypes.ENUM('software', 'hardware', 'service', 'addon', 'training'),
      allowNull: false,
      defaultValue: 'software',
    },
    version: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    cost_price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    hsn_code: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'HSN/SAC code for GST',
    },
    gst_rate: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      defaultValue: 18.00,
      comment: 'GST rate percentage',
    },
    unit: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'Nos',
    },
    is_service: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    specifications: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: 'Product specifications and features',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'tally_products',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['code'],
        unique: true,
      },
      {
        fields: ['category'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['sort_order'],
      },
    ],
  });

  // Class methods
  TallyProduct.getDefaultProducts = function() {
    return [
      {
        name: 'Tally.ERP 9 Silver',
        code: 'TERP9_SILVER',
        description: 'Single user accounting software',
        category: 'software',
        version: '6.6.3',
        price: 18000.00,
        cost_price: 15000.00,
        hsn_code: '998361',
        gst_rate: 18.00,
        unit: 'Nos',
        is_service: false,
        sort_order: 1,
      },
      {
        name: 'Tally.ERP 9 Gold',
        code: 'TERP9_GOLD',
        description: 'Multi-user accounting software',
        category: 'software',
        version: '6.6.3',
        price: 54000.00,
        cost_price: 45000.00,
        hsn_code: '998361',
        gst_rate: 18.00,
        unit: 'Nos',
        is_service: false,
        sort_order: 2,
      },
      {
        name: 'TallyPrime Silver',
        code: 'TPRIME_SILVER',
        description: 'Single user business management software',
        category: 'software',
        version: '4.0',
        price: 18000.00,
        cost_price: 15000.00,
        hsn_code: '998361',
        gst_rate: 18.00,
        unit: 'Nos',
        is_service: false,
        sort_order: 3,
      },
      {
        name: 'TallyPrime Gold',
        code: 'TPRIME_GOLD',
        description: 'Multi-user business management software',
        category: 'software',
        version: '4.0',
        price: 54000.00,
        cost_price: 45000.00,
        hsn_code: '998361',
        gst_rate: 18.00,
        unit: 'Nos',
        is_service: false,
        sort_order: 4,
      },
      {
        name: 'Tally Training',
        code: 'TALLY_TRAINING',
        description: 'Tally software training',
        category: 'training',
        price: 5000.00,
        cost_price: 3000.00,
        hsn_code: '998399',
        gst_rate: 18.00,
        unit: 'Hours',
        is_service: true,
        sort_order: 5,
      },
      {
        name: 'Data Migration Service',
        code: 'DATA_MIGRATION',
        description: 'Data migration and setup service',
        category: 'service',
        price: 3000.00,
        cost_price: 2000.00,
        hsn_code: '998399',
        gst_rate: 18.00,
        unit: 'Service',
        is_service: true,
        sort_order: 6,
      },
    ];
  };

  // Instance methods
  TallyProduct.prototype.getMarginPercentage = function() {
    if (this.cost_price > 0) {
      return ((this.price - this.cost_price) / this.cost_price) * 100;
    }
    return 0;
  };

  TallyProduct.prototype.getGSTAmount = function(quantity = 1) {
    const baseAmount = this.price * quantity;
    return (baseAmount * this.gst_rate) / 100;
  };

  TallyProduct.prototype.getTotalAmount = function(quantity = 1) {
    const baseAmount = this.price * quantity;
    const gstAmount = this.getGSTAmount(quantity);
    return baseAmount + gstAmount;
  };

  // Associations
  TallyProduct.associate = function(models) {
    TallyProduct.hasMany(models.SaleItem, {
      foreignKey: 'product_id',
      as: 'saleItems',
    });

    TallyProduct.hasMany(models.ServiceCallItem, {
      foreignKey: 'product_id',
      as: 'serviceCallItems',
    });
  };

  return TallyProduct;
}
