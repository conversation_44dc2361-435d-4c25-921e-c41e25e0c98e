import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { Toaster } from 'react-hot-toast';

// Layout Components
import MainLayout from './components/layout/MainLayout';
import AuthLayout from './components/layout/AuthLayout';

// Page Components
import Dashboard from './pages/Dashboard';
import Login from './pages/auth/Login';
import Register from './pages/auth/Register';
import ForgotPassword from './pages/auth/ForgotPassword';
import NotFound from './pages/NotFound';

// Route Components
import CustomerRoutes from './pages/customers/CustomerRoutes';
import ServiceRoutes from './pages/services/ServiceRoutes';
import SalesRoutes from './pages/sales/SalesRoutes';
import MastersRoutes from './pages/masters/MastersRoutes';
import Reports from './pages/Reports';
import Settings from './pages/Settings';
import Profile from './pages/Profile';

// SaaS Components
import SubscriptionPlans from './pages/billing/SubscriptionPlans';
import BillingDashboard from './pages/billing/BillingDashboard';
import BillingHistory from './pages/billing/BillingHistory';
import CheckoutSuccess from './pages/billing/CheckoutSuccess';

// Hooks
import { useAuth } from './hooks/useAuth';
import { useUserPreferences } from './hooks/useUserPreferences';

// Utils
import { ProtectedRoute } from './utils/ProtectedRoute';
import themeManager from './utils/themeManager';

// Styles
import './styles/App.css';

function App() {
  const { isAuthenticated, isLoading } = useAuth();

  // Load user preferences including theme color
  const { preferences } = useUserPreferences(isAuthenticated);

  // Initialize theme manager for non-authenticated users
  React.useEffect(() => {
    if (!isAuthenticated) {
      themeManager.init();
    }
  }, [isAuthenticated]);

  // Debug logging
  console.log('App render - isAuthenticated:', isAuthenticated, 'isLoading:', isLoading);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600" role="status">
          <span className="sr-only">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>TallyCRM - CRM for Tally Resellers</title>
        <meta name="description" content="Complete CRM solution for Tally Software Resellers" />
      </Helmet>

      <div className="App">
        <Routes>
          {/* Root redirect */}
          <Route
            path="/"
            element={
              isAuthenticated ? (
                <Navigate to="/dashboard" replace />
              ) : (
                <Navigate to="/auth/login" replace />
              )
            }
          />

          {/* Public Routes */}
          <Route path="/auth" element={<AuthLayout />}>
            <Route path="login" element={<Login />} />
            <Route path="register" element={<Register />} />
            <Route path="forgot-password" element={<ForgotPassword />} />
            <Route index element={<Navigate to="login" replace />} />
          </Route>

          {/* Protected Routes */}
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<Dashboard />} />
          </Route>

          <Route
            path="/customers/*"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route path="*" element={<CustomerRoutes />} />
          </Route>

          <Route
            path="/services/*"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route path="*" element={<ServiceRoutes />} />
          </Route>

          <Route
            path="/sales/*"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route path="*" element={<SalesRoutes />} />
          </Route>

          <Route
            path="/masters/*"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route path="*" element={<MastersRoutes />} />
          </Route>

          <Route
            path="/reports"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<Reports />} />
          </Route>

          <Route
            path="/settings"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<Settings />} />
          </Route>

          <Route
            path="/profile"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<Profile />} />
          </Route>

          {/* Billing Routes */}
          <Route
            path="/billing/plans"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<SubscriptionPlans />} />
          </Route>

          <Route
            path="/billing/dashboard"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<BillingDashboard />} />
          </Route>

          <Route
            path="/billing/history"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<BillingHistory />} />
          </Route>

          <Route
            path="/billing/success"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<CheckoutSuccess />} />
          </Route>

          {/* 404 Route */}
          <Route path="*" element={<NotFound />} />
        </Routes>

        {/* Toast Notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
            success: {
              duration: 3000,
              theme: {
                primary: 'green',
                secondary: 'black',
              },
            },
          }}
        />
      </div>
    </>
  );
}

export default App;
