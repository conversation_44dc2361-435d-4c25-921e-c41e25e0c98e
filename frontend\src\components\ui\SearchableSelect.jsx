import React, { useState, useEffect, useRef } from 'react';
import { FaSearch, FaChevronDown, FaTimes } from 'react-icons/fa';

const SearchableSelect = ({
  options = [],
  value,
  onChange,
  placeholder = "Search...",
  searchFields = ['name'],
  displayField = 'name',
  valueField = 'id',
  className = '',
  error = false,
  disabled = false,
  minSearchLength = 2,
  maxResults = 10,
  renderOption = null,
  renderSelected = null,
  allowClear = true,
  noResultsText = "No results found",
  searchingText = "Type to search...",
  ...props
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredOptions, setFilteredOptions] = useState([]);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);

  const containerRef = useRef(null);
  const inputRef = useRef(null);
  const listRef = useRef(null);

  // Ensure options is always an array
  const safeOptions = Array.isArray(options) ? options : [];

  // Find selected option
  const selectedOption = safeOptions.find(option => option[valueField] === value);

  // Filter options based on search term
  useEffect(() => {
    if (!searchTerm || searchTerm.length < minSearchLength) {
      setFilteredOptions([]);
      return;
    }

    const filtered = safeOptions.filter(option => {
      return searchFields.some(field => {
        const fieldValue = option[field];
        if (!fieldValue) return false;
        return fieldValue.toString().toLowerCase().includes(searchTerm.toLowerCase());
      });
    }).slice(0, maxResults);

    setFilteredOptions(filtered);
    setHighlightedIndex(-1);
  }, [searchTerm, safeOptions, searchFields, minSearchLength, maxResults]);

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle keyboard navigation
  const handleKeyDown = (e) => {
    if (!isOpen) {
      if (e.key === 'Enter' || e.key === 'ArrowDown') {
        setIsOpen(true);
        e.preventDefault();
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex(prev =>
          prev < filteredOptions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedIndex >= 0 && filteredOptions[highlightedIndex]) {
          handleSelect(filteredOptions[highlightedIndex]);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSearchTerm('');
        inputRef.current?.blur();
        break;
    }
  };

  // Handle option selection
  const handleSelect = (option) => {
    if (option && typeof onChange === 'function') {
      onChange(option[valueField]);
    }
    setIsOpen(false);
    setSearchTerm('');
    inputRef.current?.blur();
  };

  // Handle clear selection
  const handleClear = (e) => {
    e.stopPropagation();
    if (typeof onChange === 'function') {
      onChange(null);
    }
    setSearchTerm('');
    inputRef.current?.focus();
  };

  // Handle input focus
  const handleFocus = () => {
    setIsOpen(true);
    if (selectedOption && !searchTerm) {
      setSearchTerm('');
    }
  };

  // Handle input change
  const handleInputChange = (e) => {
    setSearchTerm(e.target.value);
    if (!isOpen) {
      setIsOpen(true);
    }
  };

  // Default option renderer
  const defaultRenderOption = (option, isHighlighted) => {
    if (!option) return null;

    return (
      <div className={`px-4 py-3 cursor-pointer transition-colors duration-150 ${
        isHighlighted
          ? 'bg-indigo-50 text-indigo-900'
          : 'text-gray-900 hover:bg-gray-50'
      }`}>
        <div className="font-medium">{option[displayField] || 'Unknown'}</div>
        {option.description && (
          <div className="text-sm text-gray-500 mt-1">{option.description}</div>
        )}
        {option.category && (
          <div className="text-xs text-indigo-600 mt-1 font-medium">
            {option.category}
          </div>
        )}
      </div>
    );
  };

  // Default selected renderer
  const defaultRenderSelected = (option) => {
    if (!option) return null;
    return (
      <span className="block truncate">{option[displayField] || 'Unknown'}</span>
    );
  };

  const baseInputClasses = `
    block w-full px-4 py-3 pr-10 border-2 rounded-xl shadow-sm
    focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm
    ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white cursor-text'}
  `;

  const inputClasses = error
    ? `${baseInputClasses} border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50`
    : `${baseInputClasses} border-indigo-200 focus:ring-indigo-500 focus:border-indigo-500 hover:border-indigo-300`;

  return (
    <div className={`relative ${className}`} ref={containerRef}>
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          className={inputClasses}
          placeholder={selectedOption ? selectedOption[displayField] || placeholder : placeholder}
          value={searchTerm}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onKeyDown={handleKeyDown}
          disabled={disabled}
          autoComplete="off"
          {...props}
        />

        {/* Right side icons */}
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 space-x-1">
          {selectedOption && allowClear && !disabled && (
            <button
              type="button"
              onClick={handleClear}
              className="text-gray-400 hover:text-gray-600 transition-colors duration-150"
            >
              <FaTimes className="h-4 w-4" />
            </button>
          )}

          <div className="text-gray-400">
            {isOpen ? (
              <FaChevronDown className="h-4 w-4 transform rotate-180 transition-transform duration-150" />
            ) : (
              <FaSearch className="h-4 w-4" />
            )}
          </div>
        </div>
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 mt-2 w-full bg-white border border-gray-200 rounded-xl shadow-lg max-h-60 overflow-auto">
          {searchTerm.length < minSearchLength ? (
            <div className="px-4 py-3 text-sm text-gray-500 text-center">
              {searchingText}
            </div>
          ) : filteredOptions.length === 0 ? (
            <div className="px-4 py-3 text-sm text-gray-500 text-center">
              {noResultsText}
            </div>
          ) : (
            <div ref={listRef}>
              {filteredOptions.map((option, index) => (
                <div
                  key={option[valueField]}
                  onClick={() => handleSelect(option)}
                  className="border-b border-gray-100 last:border-b-0"
                >
                  {(renderOption || defaultRenderOption)(option, index === highlightedIndex)}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SearchableSelect;
