import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  // Create nature_of_issues table
  await queryInterface.createTable('nature_of_issues', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    category: {
      type: DataTypes.ENUM('technical', 'functional', 'data', 'installation', 'training', 'other'),
      allowNull: false,
      defaultValue: 'technical',
    },
    severity: {
      type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
      allowNull: false,
      defaultValue: 'medium',
    },
    estimated_resolution_time: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    requires_onsite: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    common_solutions: {
      type: DataTypes.JSONB,
      defaultValue: [],
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Create additional_services table
  await queryInterface.createTable('additional_services', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    category: {
      type: DataTypes.ENUM('training', 'customization', 'integration', 'support', 'consulting', 'other'),
      allowNull: false,
      defaultValue: 'support',
    },
    service_type: {
      type: DataTypes.ENUM('onetime', 'recurring', 'hourly', 'project'),
      allowNull: false,
      defaultValue: 'onetime',
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    cost_price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    unit: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'Service',
    },
    duration_hours: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    hsn_code: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: '998399',
    },
    gst_rate: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false,
      defaultValue: 18.00,
    },
    requires_approval: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    is_billable: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    prerequisites: {
      type: DataTypes.JSONB,
      defaultValue: [],
    },
    deliverables: {
      type: DataTypes.JSONB,
      defaultValue: [],
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Create call_statuses table
  await queryInterface.createTable('call_statuses', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    category: {
      type: DataTypes.ENUM('open', 'in_progress', 'resolved', 'closed', 'cancelled'),
      allowNull: false,
      defaultValue: 'open',
    },
    color: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: '#6c757d',
    },
    is_final: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    requires_approval: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    auto_close_after_days: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    is_billable: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes for nature_of_issues
  await queryInterface.addIndex('nature_of_issues', ['code'], { unique: true });
  await queryInterface.addIndex('nature_of_issues', ['category']);
  await queryInterface.addIndex('nature_of_issues', ['severity']);
  await queryInterface.addIndex('nature_of_issues', ['requires_onsite']);
  await queryInterface.addIndex('nature_of_issues', ['is_active']);
  await queryInterface.addIndex('nature_of_issues', ['sort_order']);

  // Add indexes for additional_services
  await queryInterface.addIndex('additional_services', ['code'], { unique: true });
  await queryInterface.addIndex('additional_services', ['category']);
  await queryInterface.addIndex('additional_services', ['service_type']);
  await queryInterface.addIndex('additional_services', ['is_billable']);
  await queryInterface.addIndex('additional_services', ['is_active']);
  await queryInterface.addIndex('additional_services', ['sort_order']);

  // Add indexes for call_statuses
  await queryInterface.addIndex('call_statuses', ['code'], { unique: true });
  await queryInterface.addIndex('call_statuses', ['category']);
  await queryInterface.addIndex('call_statuses', ['is_final']);
  await queryInterface.addIndex('call_statuses', ['is_active']);
  await queryInterface.addIndex('call_statuses', ['sort_order']);
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('call_statuses');
  await queryInterface.dropTable('additional_services');
  await queryInterface.dropTable('nature_of_issues');
};
