# Multi-stage Dockerfile for TallyCRM
# Stage 1: Build Frontend
FROM node:18-alpine AS frontend-builder

WORKDIR /app/frontend

# Copy frontend package files
COPY frontend/package*.json ./
RUN npm i

# Copy frontend source code
COPY frontend/ ./

# Build frontend for production
RUN npm run build

# Stage 2: Build Backend
FROM node:18-alpine AS backend-builder

WORKDIR /app/backend

# Copy backend package files
COPY backend/package*.json ./
RUN npm i

# Copy backend source code
COPY backend/ ./

# Copy root package.json for workspace dependencies
COPY package*.json /app/

# Stage 3: Production Runtime
FROM node:18-alpine AS production

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Create app user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S tallycrm -u 1001

WORKDIR /app

# Copy backend from builder stage
COPY --from=backend-builder --chown=tallycrm:nodejs /app/backend ./backend

# Copy built frontend from builder stage
COPY --from=frontend-builder --chown=tallycrm:nodejs /app/frontend/dist ./frontend/dist
COPY --from=frontend-builder --chown=tallycrm:nodejs /app/frontend/.env ./frontend/dist

# Create necessary directories
RUN mkdir -p /app/logs /app/uploads && \
    chown -R tallycrm:nodejs /app/logs /app/uploads

# Switch to non-root user
USER tallycrm

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:8080/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Set environment
ENV NODE_ENV=production
ENV PORT=8080

# Start the application
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "backend/src/server.js"]
