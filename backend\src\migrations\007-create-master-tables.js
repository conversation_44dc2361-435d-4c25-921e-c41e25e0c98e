import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  // Create license_editions table
  await queryInterface.createTable('license_editions', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    version: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    annual_maintenance_charge: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    features: {
      type: DataTypes.JSONB,
      defaultValue: [],
    },
    max_companies: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    max_users: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Create designations table
  await queryInterface.createTable('designations', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    level: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
    },
    department: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Create tally_products table
  await queryInterface.createTable('tally_products', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    category: {
      type: DataTypes.ENUM('software', 'hardware', 'service', 'addon', 'training'),
      allowNull: false,
      defaultValue: 'software',
    },
    version: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    cost_price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    hsn_code: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    gst_rate: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      defaultValue: 18.00,
    },
    unit: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'Nos',
    },
    is_service: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    specifications: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes for license_editions
  await queryInterface.addIndex('license_editions', ['code'], { unique: true });
  await queryInterface.addIndex('license_editions', ['is_active']);
  await queryInterface.addIndex('license_editions', ['sort_order']);

  // Add indexes for designations
  await queryInterface.addIndex('designations', ['code'], { unique: true });
  await queryInterface.addIndex('designations', ['is_active']);
  await queryInterface.addIndex('designations', ['level']);
  await queryInterface.addIndex('designations', ['department']);
  await queryInterface.addIndex('designations', ['sort_order']);

  // Add indexes for tally_products
  await queryInterface.addIndex('tally_products', ['code'], { unique: true });
  await queryInterface.addIndex('tally_products', ['category']);
  await queryInterface.addIndex('tally_products', ['is_active']);
  await queryInterface.addIndex('tally_products', ['sort_order']);
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('tally_products');
  await queryInterface.dropTable('designations');
  await queryInterface.dropTable('license_editions');
};
