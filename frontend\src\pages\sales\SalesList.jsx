import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { apiService } from '../../services/api';
import {
  FaPlus,
  FaEdit,
  FaEye,
  FaTrash,
  FaSearch,
  FaFilter,
  FaDownload,
  FaRupeeSign,
  FaUser,
  FaCalendar,
  FaChartLine,
  FaTrophy,
  FaHandshake
} from 'react-icons/fa';
import LoadingScreen from '../../components/ui/LoadingScreen';

const SalesList = () => {
  const navigate = useNavigate();
  const [sales, setSales] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterStage, setFilterStage] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [salesPerPage] = useState(10);

  // Fetch sales data from API - Combined useEffect with proper dependencies
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchSalesData();
    }, searchTerm ? 500 : 0); // Debounce only for search, immediate for other changes

    return () => clearTimeout(timeoutId);
  }, [searchTerm, filterStatus, filterStage, currentPage]); // Removed duplicate useEffect

  const fetchSalesData = async () => {
    try {
      setLoading(true);
      const response = await apiService.get('/sales', {
        params: {
          page: currentPage,
          limit: salesPerPage,
          ...(searchTerm && { search: searchTerm }),
          ...(filterStatus !== 'all' && { status: filterStatus }),
          ...(filterStage !== 'all' && { stage: filterStage }),
        }
      });

      if (response.data?.success) {
        const salesData = response.data.data.sales || [];
        // Transform API data to match frontend expectations
        const transformedSales = salesData.map(sale => ({
          id: sale.id,
          leadNumber: sale.saleNumber || `SAL-${sale.id}`,
          customer: sale.customer?.company_name || sale.customer?.display_name || 'Unknown Customer',
          customerId: sale.customer?.id,
          contactPerson: sale.customer?.contact_person || 'N/A',
          email: sale.customer?.email || 'N/A',
          phone: sale.customer?.phone || 'N/A',
          product: 'Tally Product', // This would come from sale items
          productType: 'Software License',
          stage: sale.status === 'draft' ? 'lead' :
                 sale.status === 'confirmed' ? 'closed-won' :
                 sale.paymentStatus === 'paid' ? 'closed-won' : 'proposal',
          status: sale.status === 'confirmed' && sale.paymentStatus === 'paid' ? 'won' :
                  sale.status === 'cancelled' ? 'lost' : 'active',
          priority: 'medium', // This would need to be added to the Sale model
          source: 'Direct', // This would need to be added to the Sale model
          assignedTo: sale.salesExecutive?.name || 'Unassigned',
          expectedValue: sale.totalAmount || 0,
          probability: sale.status === 'confirmed' ? 100 :
                      sale.paymentStatus === 'paid' ? 100 : 50,
          expectedCloseDate: sale.saleDate || sale.createdAt,
          createdDate: sale.createdAt,
          lastActivity: sale.updatedAt,
          notes: sale.description || 'No notes available'
        }));

        setSales(transformedSales);
      } else {
        console.error('Failed to fetch sales data:', response.data?.message);
        setSales([]);
      }
    } catch (error) {
      console.error('Error fetching sales data:', error);
      toast.error('Failed to load sales data');
      setSales([]);
    } finally {
      setLoading(false);
    }
  };

  // Filter sales based on search, status, and stage
  const filteredSales = sales.filter(sale => {
    const matchesSearch = sale.leadNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sale.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sale.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sale.product.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || sale.status === filterStatus;
    const matchesStage = filterStage === 'all' || sale.stage === filterStage;
    return matchesSearch && matchesStatus && matchesStage;
  });

  // Pagination
  const indexOfLastSale = currentPage * salesPerPage;
  const indexOfFirstSale = indexOfLastSale - salesPerPage;
  const currentSales = filteredSales.slice(indexOfFirstSale, indexOfLastSale);
  const totalPages = Math.ceil(filteredSales.length / salesPerPage);

  // Calculate stats
  const totalValue = sales.reduce((sum, sale) => sum + sale.expectedValue, 0);
  const wonDeals = sales.filter(sale => sale.status === 'won');
  const activeDeals = sales.filter(sale => sale.status === 'active');
  const avgDealSize = sales.length > 0 ? totalValue / sales.length : 0;

  const handleDelete = async (saleId) => {
    if (window.confirm('Are you sure you want to delete this sales opportunity?')) {
      try {
        const response = await apiService.delete(`/sales/${saleId}`);
        if (response.data?.success) {
          setSales(sales.filter(sale => sale.id !== saleId));
          toast.success('Sales opportunity deleted successfully');
        } else {
          toast.error(response.data?.message || 'Failed to delete sales opportunity');
        }
      } catch (error) {
        console.error('Error deleting sale:', error);
        toast.error('Failed to delete sales opportunity');
      }
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      'won': { bg: 'bg-gradient-to-r from-green-100 to-emerald-100', text: 'text-green-800', icon: '🏆' },
      'lost': { bg: 'bg-gradient-to-r from-red-100 to-pink-100', text: 'text-red-800', icon: '❌' },
      'active': { bg: 'bg-gradient-to-r from-blue-100 to-cyan-100', text: 'text-blue-800', icon: '🔄' }
    };

    const config = statusConfig[status] || statusConfig['active'];
    const displayText = status.charAt(0).toUpperCase() + status.slice(1);

    return (
      <span className={`inline-flex items-center px-3 py-1.5 text-xs font-bold rounded-full shadow-sm ${config.bg} ${config.text} border border-opacity-20`}>
        <span className="mr-1">{config.icon}</span>
        {displayText}
      </span>
    );
  };

  const getStageBadge = (stage) => {
    const stageConfig = {
      'lead': { bg: 'bg-gradient-to-r from-gray-100 to-slate-100', text: 'text-gray-800', icon: '🎯' },
      'qualification': { bg: 'bg-gradient-to-r from-blue-100 to-indigo-100', text: 'text-blue-800', icon: '🔍' },
      'proposal': { bg: 'bg-gradient-to-r from-yellow-100 to-orange-100', text: 'text-yellow-800', icon: '📋' },
      'negotiation': { bg: 'bg-gradient-to-r from-purple-100 to-indigo-100', text: 'text-purple-800', icon: '🤝' },
      'closed-won': { bg: 'bg-gradient-to-r from-green-100 to-emerald-100', text: 'text-green-800', icon: '✅' },
      'closed-lost': { bg: 'bg-gradient-to-r from-red-100 to-pink-100', text: 'text-red-800', icon: '❌' }
    };

    const config = stageConfig[stage] || stageConfig['lead'];
    const displayText = stage.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());

    return (
      <span className={`inline-flex items-center px-3 py-1.5 text-xs font-bold rounded-full shadow-sm ${config.bg} ${config.text} border border-opacity-20`}>
        <span className="mr-1">{config.icon}</span>
        {displayText}
      </span>
    );
  };

  const getPriorityBadge = (priority) => {
    const priorityConfig = {
      'high': { bg: 'bg-gradient-to-r from-red-100 to-pink-100', text: 'text-red-800', icon: '🔥' },
      'medium': { bg: 'bg-gradient-to-r from-yellow-100 to-amber-100', text: 'text-yellow-800', icon: '⚡' },
      'low': { bg: 'bg-gradient-to-r from-green-100 to-emerald-100', text: 'text-green-800', icon: '🌱' }
    };

    const config = priorityConfig[priority] || priorityConfig['medium'];
    const displayText = priority.charAt(0).toUpperCase() + priority.slice(1);

    return (
      <span className={`inline-flex items-center px-3 py-1.5 text-xs font-bold rounded-full shadow-sm ${config.bg} ${config.text} border border-opacity-20`}>
        <span className="mr-1">{config.icon}</span>
        {displayText}
      </span>
    );
  };

  const handleExport = () => {
    try {
      // Prepare data for export
      const exportData = filteredSales.map(sale => ({
        'Lead Number': sale.leadNumber,
        'Customer': sale.customer,
        'Contact Person': sale.contactPerson,
        'Product': sale.product,
        'Product Type': sale.productType,
        'Stage': sale.stage,
        'Status': sale.status,
        'Priority': sale.priority,
        'Assigned To': sale.assignedTo,
        'Expected Value': sale.expectedValue,
        'Probability': sale.probability + '%',
        'Expected Close Date': new Date(sale.expectedCloseDate).toLocaleDateString()
      }));

      // Convert to CSV
      const headers = Object.keys(exportData[0] || {});
      const csvContent = [
        headers.join(','),
        ...exportData.map(row =>
          headers.map(header =>
            `"${(row[header] || '').toString().replace(/"/g, '""')}"`
          ).join(',')
        )
      ].join('\n');

      // Download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `sales_export_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success(`Exported ${exportData.length} sales opportunities successfully`);
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export sales data');
    }
  };

  if (loading) {
    return (
      <LoadingScreen
        title="Loading Sales Data..."
        subtitle="Please wait while we fetch your sales pipeline"
        variant="page"
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-100">
      <div className="w-full">
        {/* Colorful Header */}
        <div className="mb-8">
          <div className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 rounded-2xl shadow-xl p-6 text-white">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-2 flex items-center">
                  <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-4">
                    <FaChartLine className="text-white text-xl" />
                  </div>
                  Sales Management
                </h2>
                <p className="text-green-100 text-sm sm:text-base">Track leads, opportunities, and sales pipeline</p>
              </div>
              <div className="flex gap-3">
                <button
                  className="inline-flex items-center px-6 py-3 border-2 border-opacity-30 text-sm font-medium rounded-xl bg-opacity-10 hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-all duration-200 backdrop-blur-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{
                    borderColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.3)',
                    color: 'var(--primary-text, #ffffff)',
                    backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.1)',
                    '--tw-ring-color': 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.5)'
                  }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)'}
                  onMouseLeave={(e) => e.target.style.backgroundColor = 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.1)'}
                  onClick={handleExport}
                  disabled={filteredSales.length === 0}
                >
                  <FaDownload className="mr-2 h-4 w-4" />
                  Export ({filteredSales.length})
                </button>
                <Link
                  to="/sales/add"
                  className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-xl text-green-600 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-all duration-200 shadow-lg"
                >
                  <FaPlus className="mr-2 h-4 w-4" />
                  New Lead
                </Link>
              </div>
            </div>
          </div>
        </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="stats-card-primary rounded-lg shadow-lg hover:shadow-xl transition-all duration-300">
          <div className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="text-xl sm:text-2xl font-bold mb-0">{sales.length}</h4>
                <p className="mb-0 text-sm" style={{ color: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)' }}>Total Opportunities</p>
              </div>
              <div className="rounded-lg p-3" style={{ backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)' }}>
                <FaChartLine className="h-6 w-6" style={{ color: 'var(--primary-text, #ffffff)' }} />
              </div>
            </div>
            <div className="mt-3 flex items-center">
              <div className="bg-green-500/20 text-green-300 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                <i className="bi bi-arrow-up mr-1"></i>
                +25%
              </div>
              <span className="text-xs ml-2" style={{ color: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.7)' }}>vs last month</span>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
          <div className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="text-xl sm:text-2xl font-bold mb-0 text-gray-900">{wonDeals.length}</h4>
                <p className="text-gray-600 mb-0 text-sm">Won Deals</p>
              </div>
              <div className="white-stats-icon-bg rounded-lg p-3">
                <FaTrophy className="h-6 w-6 white-stats-icon-text" />
              </div>
            </div>
            <div className="mt-3 flex items-center">
              <div className="bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                <i className="bi bi-arrow-up mr-1"></i>
                +18%
              </div>
              <span className="text-gray-500 text-xs ml-2">vs last month</span>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
          <div className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="text-xl sm:text-2xl font-bold mb-0 text-gray-900">₹{totalValue.toLocaleString()}</h4>
                <p className="text-gray-600 mb-0 text-sm">Pipeline Value</p>
              </div>
              <div className="white-stats-icon-bg rounded-lg p-3">
                <FaRupeeSign className="h-6 w-6 white-stats-icon-text" />
              </div>
            </div>
            <div className="mt-3 flex items-center">
              <div className="bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                <i className="bi bi-arrow-up mr-1"></i>
                +32%
              </div>
              <span className="text-gray-500 text-xs ml-2">vs last month</span>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
          <div className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="text-xl sm:text-2xl font-bold mb-0 text-gray-900">₹{Math.round(avgDealSize).toLocaleString()}</h4>
                <p className="text-gray-600 mb-0 text-sm">Avg Deal Size</p>
              </div>
              <div className="white-stats-icon-bg rounded-lg p-3">
                <FaHandshake className="h-6 w-6 white-stats-icon-text" />
              </div>
            </div>
            <div className="mt-3 flex items-center">
              <div className="bg-blue-100 text-blue-600 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                <i className="bi bi-arrow-up mr-1"></i>
                +8%
              </div>
              <span className="text-gray-500 text-xs ml-2">vs last month</span>
            </div>
          </div>
        </div>
      </div>

        {/* Enhanced Filters */}
        <div className="bg-white rounded-2xl shadow-xl p-6 mb-8 border border-green-100">
          <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
            <div className="md:col-span-4">
              <label className="block text-sm font-bold text-green-700 mb-2">🔍 Search Opportunities</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <FaSearch className="h-5 w-5 text-green-400" />
                </div>
                <input
                  type="text"
                  className="block w-full pl-12 pr-4 py-3 border-2 border-green-200 rounded-xl leading-5 bg-white placeholder-green-400 focus:outline-none focus:placeholder-green-300 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 sm:text-sm"
                  placeholder="Search by lead number, customer..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div className="md:col-span-3">
              <label className="block text-sm font-bold text-blue-700 mb-2">📊 Status Filter</label>
              <select
                className="block w-full px-4 py-3 border-2 border-blue-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white hover:border-blue-300 transition-all duration-200 sm:text-sm"
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <option value="all">🌟 All Status</option>
                <option value="active">🔄 Active</option>
                <option value="won">🏆 Won</option>
                <option value="lost">❌ Lost</option>
              </select>
            </div>
            <div className="md:col-span-3">
              <label className="block text-sm font-bold text-purple-700 mb-2">🎯 Stage Filter</label>
              <select
                className="block w-full px-4 py-3 border-2 border-purple-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white hover:border-purple-300 transition-all duration-200 sm:text-sm"
                value={filterStage}
                onChange={(e) => setFilterStage(e.target.value)}
              >
                <option value="all">🌈 All Stages</option>
                <option value="lead">🎯 Lead</option>
                <option value="qualification">🔍 Qualification</option>
                <option value="proposal">📋 Proposal</option>
                <option value="negotiation">🤝 Negotiation</option>
                <option value="closed-won">✅ Closed Won</option>
                <option value="closed-lost">❌ Closed Lost</option>
              </select>
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-bold text-orange-700 mb-2">⚡ Actions</label>
              <button className="w-full inline-flex items-center justify-center px-4 py-3 border-2 border-orange-200 rounded-xl shadow-sm text-sm font-medium text-orange-700 bg-white hover:bg-orange-50 hover:border-orange-300 focus:outline-none focus:ring-2 focus:ring-orange-500 transition-all duration-200">
                <FaFilter className="mr-2 h-4 w-4" />
                More Filters
              </button>
            </div>
          </div>
        </div>

      {/* Sales Table */}
      <div className="grid grid-cols-12 gap-4">
        <div className="col-span-12">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-4">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 hover:bg-gray-50">
                  <thead className="bg-gray-50">
                    <tr>
                      <th>Lead #</th>
                      <th>Customer</th>
                      <th>Product</th>
                      <th>Stage</th>
                      <th>Status</th>
                      <th>Priority</th>
                      <th>Assigned To</th>
                      <th>Value</th>
                      <th>Probability</th>
                      <th>Close Date</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {currentSales.map(sale => (
                      <tr key={sale.id}>
                        <td>
                          <strong>{sale.leadNumber}</strong>
                        </td>
                        <td>
                          <div>
                            <h6 className="mb-0">{sale.customer}</h6>
                            <small className="text-gray-600">{sale.contactPerson}</small>
                          </div>
                        </td>
                        <td>
                          <div>
                            <span className="fw-bold">{sale.product}</span>
                            <br />
                            <small className="text-gray-600">{sale.productType}</small>
                          </div>
                        </td>
                        <td>{getStageBadge(sale.stage)}</td>
                        <td>{getStatusBadge(sale.status)}</td>
                        <td>{getPriorityBadge(sale.priority)}</td>
                        <td>{sale.assignedTo}</td>
                        <td>
                          <div className="flex items-center">
                            <FaRupeeSign className="text-gray-600 mr-1" size={12} />
                            <strong>{sale.expectedValue.toLocaleString()}</strong>
                          </div>
                        </td>
                        <td>
                          <div className="flex items-center">
                            <div className="progress mr-2" style={{ width: '60px', height: '8px' }}>
                              <div
                                className="progress-bar bg-green-600"
                                style={{ width: `${sale.probability}%` }}
                              ></div>
                            </div>
                            <small>{sale.probability}%</small>
                          </div>
                        </td>
                        <td>
                          <div className="flex items-center">
                            <FaCalendar className="text-gray-600 mr-2" size={12} />
                            {new Date(sale.expectedCloseDate).toLocaleDateString()}
                          </div>
                        </td>
                        <td>
                          <div className="inline-flex rounded-md shadow-sm" role="group">
                            <button
                              className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 px-3 py-1 text-sm border hover:bg-gray-50"
                              style={{ borderColor: 'var(--primary-color)', color: 'var(--primary-color)' }}
                              onClick={() => navigate(`/sales/${sale.id}`)}
                              title="View Details"
                            >
                              <FaEye />
                            </button>
                            <button
                              className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 px-3 py-1 text-sm border border-gray-600 text-gray-600 hover:bg-gray-50 focus:ring-gray-500"
                              onClick={() => navigate(`/sales/${sale.id}/edit`)}
                              title="Edit"
                            >
                              <FaEdit />
                            </button>
                            <button
                              className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 px-3 py-1 text-sm border border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500"
                              onClick={() => handleDelete(sale.id)}
                              title="Delete"
                            >
                              <FaTrash />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <nav className="mt-6">
                  <ul className="pagination justify-center">
                    <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
                      <button
                        className="page-link"
                        onClick={() => setCurrentPage(currentPage - 1)}
                        disabled={currentPage === 1}
                      >
                        Previous
                      </button>
                    </li>
                    {[...Array(totalPages)].map((_, index) => (
                      <li key={index} className={`page-item ${currentPage === index + 1 ? 'active' : ''}`}>
                        <button
                          className="page-link"
                          onClick={() => setCurrentPage(index + 1)}
                        >
                          {index + 1}
                        </button>
                      </li>
                    ))}
                    <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>
                      <button
                        className="page-link"
                        onClick={() => setCurrentPage(currentPage + 1)}
                        disabled={currentPage === totalPages}
                      >
                        Next
                      </button>
                    </li>
                  </ul>
                </nav>
              )}
            </div>
          </div>
        </div>
      </div>
      </div>
    </div>
  );
};

export default SalesList;
