import { Sequelize } from 'sequelize';
import config from '../../config/database.js';
import { logger } from '../utils/logger.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

logger.info('🔧 Database configuration:', {
  env,
  dialect: dbConfig.dialect,
  database: dbConfig.database,
  storage: dbConfig.storage,
  host: dbConfig.host,
  port: dbConfig.port
});

// Initialize Sequelize
const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.username,
  dbConfig.password,
  {
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    storage: dbConfig.storage,
    logging: dbConfig.logging,
    pool: dbConfig.pool,
    define: dbConfig.define,
    dialectOptions: dbConfig.dialectOptions,
  }
);

// Create migrations table if it doesn't exist
const createMigrationsTable = async () => {
  if (dbConfig.dialect === 'sqlite') {
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS migrations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        executed_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
  } else {
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS migrations (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
  }
};

// Get executed migrations
const getExecutedMigrations = async () => {
  const [results] = await sequelize.query('SELECT name FROM migrations ORDER BY id');
  return results.map(row => row.name);
};

// Mark migration as executed
const markMigrationExecuted = async (migrationName) => {
  if (dbConfig.dialect === 'sqlite') {
    await sequelize.query('INSERT INTO migrations (name) VALUES (?)', {
      replacements: [migrationName],
    });
  } else {
    await sequelize.query('INSERT INTO migrations (name) VALUES (:name)', {
      replacements: { name: migrationName },
    });
  }
};

// Get all migration files
const getMigrationFiles = () => {
  const migrationFiles = fs.readdirSync(__dirname)
    .filter(file => file.endsWith('.js') && file !== 'migrate.js')
    .sort();
  return migrationFiles;
};

// Run migrations
const runMigrations = async () => {
  try {
    logger.info('🚀 Starting database migrations...');

    // Test database connection
    await sequelize.authenticate();
    logger.info('✅ Database connection established');

    // Create migrations table
    await createMigrationsTable();
    logger.info('✅ Migrations table ready');

    // Get executed migrations
    const executedMigrations = await getExecutedMigrations();
    logger.info(`📋 Found ${executedMigrations.length} executed migrations`);

    // Get all migration files
    const migrationFiles = getMigrationFiles();
    logger.info(`📁 Found ${migrationFiles.length} migration files`);

    // Run pending migrations
    let executedCount = 0;
    for (const file of migrationFiles) {
      const migrationName = file.replace('.js', '');

      if (executedMigrations.includes(migrationName)) {
        logger.info(`⏭️  Skipping ${migrationName} (already executed)`);
        continue;
      }

      logger.info(`🔄 Running migration: ${migrationName}`);

      try {
        // Import and run migration
        const migrationPath = path.join(__dirname, file);
        const migrationUrl = `file://${migrationPath.replace(/\\/g, '/')}`;
        const migration = await import(migrationUrl);

        if (typeof migration.up !== 'function') {
          throw new Error(`Migration ${migrationName} does not export an 'up' function`);
        }

        // Run the migration
        await migration.up(sequelize.getQueryInterface());

        // Mark as executed
        await markMigrationExecuted(migrationName);

        logger.info(`✅ Completed migration: ${migrationName}`);
        executedCount++;
      } catch (error) {
        logger.error(`❌ Failed to run migration ${migrationName}:`, error);
        throw error;
      }
    }

    if (executedCount === 0) {
      logger.info('✨ All migrations are up to date');
    } else {
      logger.info(`✅ Successfully executed ${executedCount} migrations`);
    }

  } catch (error) {
    logger.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
};

// Rollback migrations
const rollbackMigrations = async (steps = 1) => {
  try {
    logger.info(`🔄 Rolling back ${steps} migration(s)...`);

    // Test database connection
    await sequelize.authenticate();

    // Create migrations table
    await createMigrationsTable();

    // Get executed migrations (in reverse order)
    const [results] = await sequelize.query('SELECT name FROM migrations ORDER BY id DESC LIMIT :steps', {
      replacements: { steps: steps },
    });

    if (results.length === 0) {
      logger.info('ℹ️  No migrations to rollback');
      return;
    }

    // Rollback migrations
    for (const row of results) {
      const migrationName = row.name;
      logger.info(`🔄 Rolling back migration: ${migrationName}`);

      try {
        // Import and run rollback
        const migrationPath = path.join(__dirname, `${migrationName}.js`);
        const migrationUrl = `file://${migrationPath.replace(/\\/g, '/')}`;
        const migration = await import(migrationUrl);

        if (typeof migration.down !== 'function') {
          throw new Error(`Migration ${migrationName} does not export a 'down' function`);
        }

        // Run the rollback
        await migration.down(sequelize.getQueryInterface());

        // Remove from migrations table
        if (dbConfig.dialect === 'sqlite') {
          await sequelize.query('DELETE FROM migrations WHERE name = ?', {
            replacements: [migrationName],
          });
        } else {
          await sequelize.query('DELETE FROM migrations WHERE name = :name', {
            replacements: { name: migrationName },
          });
        }

        logger.info(`✅ Rolled back migration: ${migrationName}`);
      } catch (error) {
        logger.error(`❌ Failed to rollback migration ${migrationName}:`, error);
        throw error;
      }
    }

    logger.info(`✅ Successfully rolled back ${results.length} migration(s)`);

  } catch (error) {
    logger.error('❌ Rollback failed:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
};

// CLI interface
const command = process.argv[2];
const arg = process.argv[3];

switch (command) {
  case 'up':
    runMigrations();
    break;
  case 'down':
    const steps = arg ? parseInt(arg) : 1;
    rollbackMigrations(steps);
    break;
  default:
    logger.info('Usage:');
    logger.info('  node migrate.js up          - Run all pending migrations');
    logger.info('  node migrate.js down [n]    - Rollback n migrations (default: 1)');
    process.exit(1);
}

export { runMigrations, rollbackMigrations };
