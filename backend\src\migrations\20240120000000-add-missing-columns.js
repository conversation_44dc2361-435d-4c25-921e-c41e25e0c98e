import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Helper function to check if column exists
      const columnExists = async (tableName, columnName) => {
        const [results] = await queryInterface.sequelize.query(
          `SELECT column_name FROM information_schema.columns WHERE table_name = '${tableName}' AND column_name = '${columnName}';`,
          { transaction }
        );
        return results.length > 0;
      };

      // Add missing columns to users table
      if (!(await columnExists('users', 'avatar_url'))) {
        await queryInterface.addColumn('users', 'avatar_url', {
          type: DataTypes.STRING,
          allowNull: true,
          comment: 'URL to user profile picture'
        }, { transaction });
      }

      if (!(await columnExists('users', 'timezone'))) {
        await queryInterface.addColumn('users', 'timezone', {
          type: DataTypes.STRING,
          allowNull: true,
          defaultValue: 'Asia/Kolkata',
          comment: 'User timezone preference'
        }, { transaction });
      }

      if (!(await columnExists('users', 'language'))) {
        await queryInterface.addColumn('users', 'language', {
          type: DataTypes.STRING,
          allowNull: true,
          defaultValue: 'en',
          comment: 'User language preference'
        }, { transaction });
      }

      if (!(await columnExists('users', 'theme'))) {
        await queryInterface.addColumn('users', 'theme', {
          type: DataTypes.STRING,
          allowNull: true,
          defaultValue: 'light',
          comment: 'User theme preference'
        }, { transaction });
      }

      if (!(await columnExists('users', 'notifications_enabled'))) {
        await queryInterface.addColumn('users', 'notifications_enabled', {
          type: DataTypes.BOOLEAN,
          allowNull: false,
          defaultValue: true,
          comment: 'Whether notifications are enabled'
        }, { transaction });
      }

      if (!(await columnExists('users', 'email_notifications'))) {
        await queryInterface.addColumn('users', 'email_notifications', {
          type: DataTypes.BOOLEAN,
          allowNull: false,
          defaultValue: true,
          comment: 'Whether email notifications are enabled'
        }, { transaction });
      }

      if (!(await columnExists('users', 'preferences'))) {
        await queryInterface.addColumn('users', 'preferences', {
          type: DataTypes.JSONB,
          allowNull: true,
          defaultValue: {},
          comment: 'User preferences JSON'
        }, { transaction });
      }

      // Add missing columns to tenants table
      if (!(await columnExists('tenants', 'timezone'))) {
        await queryInterface.addColumn('tenants', 'timezone', {
          type: DataTypes.STRING,
          allowNull: true,
          defaultValue: 'Asia/Kolkata',
          comment: 'Tenant timezone'
        }, { transaction });
      }

      if (!(await columnExists('tenants', 'currency'))) {
        await queryInterface.addColumn('tenants', 'currency', {
          type: DataTypes.STRING,
          allowNull: true,
          defaultValue: 'INR',
          comment: 'Tenant default currency'
        }, { transaction });
      }

      if (!(await columnExists('tenants', 'date_format'))) {
        await queryInterface.addColumn('tenants', 'date_format', {
          type: DataTypes.STRING,
          allowNull: true,
          defaultValue: 'DD/MM/YYYY',
          comment: 'Tenant date format preference'
        }, { transaction });
      }

      if (!(await columnExists('tenants', 'time_format'))) {
        await queryInterface.addColumn('tenants', 'time_format', {
          type: DataTypes.STRING,
          allowNull: true,
          defaultValue: '24',
          comment: 'Tenant time format preference (12/24)'
        }, { transaction });
      }

      if (!(await columnExists('tenants', 'logo_url'))) {
        await queryInterface.addColumn('tenants', 'logo_url', {
          type: DataTypes.STRING,
          allowNull: true,
          comment: 'URL to tenant logo'
        }, { transaction });
      }

      // Add virtual name column support for executives
      // We'll handle this in the model getter instead of adding a physical column

      await transaction.commit();
      console.log('Successfully added missing columns');
    } catch (error) {
      await transaction.rollback();
      console.error('Error adding missing columns:', error);
      throw error;
    }
};

export const down = async (queryInterface) => {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Remove columns from users table
      await queryInterface.removeColumn('users', 'avatar_url', { transaction });
      await queryInterface.removeColumn('users', 'timezone', { transaction });
      await queryInterface.removeColumn('users', 'language', { transaction });
      await queryInterface.removeColumn('users', 'theme', { transaction });
      await queryInterface.removeColumn('users', 'notifications_enabled', { transaction });
      await queryInterface.removeColumn('users', 'email_notifications', { transaction });
      await queryInterface.removeColumn('users', 'preferences', { transaction });

      // Remove columns from tenants table
      await queryInterface.removeColumn('tenants', 'timezone', { transaction });
      await queryInterface.removeColumn('tenants', 'currency', { transaction });
      await queryInterface.removeColumn('tenants', 'date_format', { transaction });
      await queryInterface.removeColumn('tenants', 'time_format', { transaction });
      await queryInterface.removeColumn('tenants', 'logo_url', { transaction });

      await transaction.commit();
      console.log('Successfully removed added columns');
    } catch (error) {
      await transaction.rollback();
      console.error('Error removing columns:', error);
      throw error;
    }
};
