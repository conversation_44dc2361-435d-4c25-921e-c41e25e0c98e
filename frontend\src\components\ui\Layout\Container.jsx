import React from 'react';
import { cn } from '../../../utils/helpers';

const Container = React.forwardRef(({
  children,
  className,
  fluid = false,
  size = 'default',
  ...props
}, ref) => {
  const baseClasses = 'mx-auto px-4';
  
  const sizes = {
    sm: 'max-w-screen-sm',
    md: 'max-w-screen-md',
    lg: 'max-w-screen-lg',
    xl: 'max-w-screen-xl',
    '2xl': 'max-w-screen-2xl',
    default: 'max-w-7xl',
  };

  const containerClasses = cn(
    baseClasses,
    !fluid && sizes[size],
    className
  );

  return (
    <div
      ref={ref}
      className={containerClasses}
      {...props}
    >
      {children}
    </div>
  );
});

Container.displayName = 'Container';

export default Container;
