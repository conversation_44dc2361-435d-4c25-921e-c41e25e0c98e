import models from '../models/index.js';
import { logger } from '../utils/logger.js';

class UsageTrackingService {
  /**
   * Track user count for a tenant
   */
  static async trackUsers(tenantId) {
    try {
      const subscription = await models.Subscription.findOne({
        where: { tenant_id: tenantId },
      });

      if (!subscription) {
        logger.warn('No subscription found for tenant:', tenantId);
        return;
      }

      const userCount = await models.User.count({
        where: { 
          tenant_id: tenantId,
          is_active: true,
        },
      });

      await models.UsageRecord.recordUsage(
        tenantId,
        subscription.id,
        'users',
        userCount
      );

      logger.debug('User usage tracked:', { tenantId, userCount });
    } catch (error) {
      logger.error('Error tracking user usage:', error);
    }
  }

  /**
   * Track customer count for a tenant
   */
  static async trackCustomers(tenantId) {
    try {
      const subscription = await models.Subscription.findOne({
        where: { tenant_id: tenantId },
      });

      if (!subscription) {
        logger.warn('No subscription found for tenant:', tenantId);
        return;
      }

      const customerCount = await models.Customer.count({
        where: { tenant_id: tenantId },
      });

      await models.UsageRecord.recordUsage(
        tenantId,
        subscription.id,
        'customers',
        customerCount
      );

      logger.debug('Customer usage tracked:', { tenantId, customerCount });
    } catch (error) {
      logger.error('Error tracking customer usage:', error);
    }
  }

  /**
   * Track service calls for a tenant
   */
  static async trackServiceCalls(tenantId) {
    try {
      const subscription = await models.Subscription.findOne({
        where: { tenant_id: tenantId },
      });

      if (!subscription) {
        logger.warn('No subscription found for tenant:', tenantId);
        return;
      }

      const now = new Date();
      const periodStart = new Date(now.getFullYear(), now.getMonth(), 1);
      const periodEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);

      const serviceCallCount = await models.ServiceCall.count({
        where: { 
          tenant_id: tenantId,
          created_at: {
            [models.Sequelize.Op.between]: [periodStart, periodEnd],
          },
        },
      });

      await models.UsageRecord.recordUsage(
        tenantId,
        subscription.id,
        'service_calls',
        serviceCallCount
      );

      logger.debug('Service call usage tracked:', { tenantId, serviceCallCount });
    } catch (error) {
      logger.error('Error tracking service call usage:', error);
    }
  }

  /**
   * Track storage usage for a tenant (placeholder)
   */
  static async trackStorage(tenantId) {
    try {
      const subscription = await models.Subscription.findOne({
        where: { tenant_id: tenantId },
      });

      if (!subscription) {
        logger.warn('No subscription found for tenant:', tenantId);
        return;
      }

      // TODO: Implement actual storage calculation
      // This could include file uploads, document storage, etc.
      const storageUsageGB = 0.1; // Placeholder

      await models.UsageRecord.recordUsage(
        tenantId,
        subscription.id,
        'storage_gb',
        Math.ceil(storageUsageGB)
      );

      logger.debug('Storage usage tracked:', { tenantId, storageUsageGB });
    } catch (error) {
      logger.error('Error tracking storage usage:', error);
    }
  }

  /**
   * Track API calls for a tenant
   */
  static async trackApiCalls(tenantId, increment = 1) {
    try {
      const subscription = await models.Subscription.findOne({
        where: { tenant_id: tenantId },
      });

      if (!subscription) {
        logger.warn('No subscription found for tenant:', tenantId);
        return;
      }

      const currentUsage = await models.UsageRecord.getCurrentUsage(tenantId, 'api_calls');
      const newUsage = currentUsage + increment;

      await models.UsageRecord.recordUsage(
        tenantId,
        subscription.id,
        'api_calls',
        newUsage
      );

      logger.debug('API call usage tracked:', { tenantId, newUsage });
    } catch (error) {
      logger.error('Error tracking API call usage:', error);
    }
  }

  /**
   * Check if tenant has exceeded limits
   */
  static async checkLimits(tenantId) {
    try {
      const subscription = await models.Subscription.findOne({
        where: { tenant_id: tenantId },
        include: [
          {
            model: models.SubscriptionPlan,
            as: 'plan',
          },
        ],
      });

      if (!subscription || !subscription.plan) {
        logger.warn('No subscription or plan found for tenant:', tenantId);
        return { hasViolations: false, violations: {} };
      }

      const limits = {
        users: subscription.plan.max_users,
        customers: subscription.plan.max_customers,
        service_calls: subscription.plan.max_service_calls,
        storage_gb: subscription.plan.max_storage_gb,
      };

      const limitCheck = await models.UsageRecord.checkLimits(tenantId, limits);
      
      if (limitCheck.hasViolations) {
        logger.warn('Usage limits exceeded:', {
          tenantId,
          violations: limitCheck.violations,
        });
      }

      return limitCheck;
    } catch (error) {
      logger.error('Error checking usage limits:', error);
      return { hasViolations: false, violations: {} };
    }
  }

  /**
   * Update all usage metrics for a tenant
   */
  static async updateAllUsage(tenantId) {
    try {
      await Promise.all([
        this.trackUsers(tenantId),
        this.trackCustomers(tenantId),
        this.trackServiceCalls(tenantId),
        this.trackStorage(tenantId),
      ]);

      logger.info('All usage metrics updated for tenant:', tenantId);
    } catch (error) {
      logger.error('Error updating all usage metrics:', error);
    }
  }

  /**
   * Get usage summary for a tenant
   */
  static async getUsageSummary(tenantId) {
    try {
      const subscription = await models.Subscription.findOne({
        where: { tenant_id: tenantId },
        include: [
          {
            model: models.SubscriptionPlan,
            as: 'plan',
          },
        ],
      });

      if (!subscription) {
        return null;
      }

      const currentUsage = await models.UsageRecord.getUsageSummary(tenantId);
      const limitCheck = await this.checkLimits(tenantId);

      return {
        subscription,
        currentUsage,
        limits: {
          users: subscription.plan.max_users,
          customers: subscription.plan.max_customers,
          service_calls: subscription.plan.max_service_calls,
          storage_gb: subscription.plan.max_storage_gb,
        },
        limitCheck,
      };
    } catch (error) {
      logger.error('Error getting usage summary:', error);
      return null;
    }
  }

  /**
   * Middleware to track API calls
   */
  static trackApiCallMiddleware() {
    return async (req, res, next) => {
      // Only track for authenticated requests with tenant context
      if (req.user && req.user.tenantId) {
        // Track API call asynchronously to not block the request
        setImmediate(() => {
          this.trackApiCalls(req.user.tenantId, 1);
        });
      }
      next();
    };
  }

  /**
   * Middleware to check usage limits
   */
  static checkLimitsMiddleware(metricName) {
    return async (req, res, next) => {
      if (!req.user || !req.user.tenantId) {
        return next();
      }

      try {
        const limitCheck = await this.checkLimits(req.user.tenantId);
        
        if (limitCheck.hasViolations && limitCheck.violations[metricName]) {
          return res.status(429).json({
            success: false,
            message: `Usage limit exceeded for ${metricName}`,
            error: 'USAGE_LIMIT_EXCEEDED',
            data: {
              current: limitCheck.violations[metricName].current,
              limit: limitCheck.violations[metricName].limit,
              exceeded: limitCheck.violations[metricName].exceeded,
            },
          });
        }

        next();
      } catch (error) {
        logger.error('Error in usage limit middleware:', error);
        next(); // Continue on error to not block the request
      }
    };
  }
}

export default UsageTrackingService;
