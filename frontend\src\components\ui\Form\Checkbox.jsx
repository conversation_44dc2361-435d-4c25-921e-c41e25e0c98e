import React from 'react';
import { cn } from '../../../utils/helpers';

const Checkbox = React.forwardRef(({
  children,
  className,
  id,
  disabled,
  error,
  ...props
}, ref) => {
  const checkboxClasses = cn(
    'h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500 focus:ring-2 disabled:opacity-50 disabled:cursor-not-allowed',
    error && 'border-danger-500 focus:ring-danger-500'
  );

  const labelClasses = cn(
    'ml-2 text-sm text-gray-700',
    disabled && 'opacity-50 cursor-not-allowed'
  );

  return (
    <div className={cn('flex items-center', className)}>
      <input
        ref={ref}
        type="checkbox"
        id={id}
        className={checkboxClasses}
        disabled={disabled}
        {...props}
      />
      {children && (
        <label htmlFor={id} className={labelClasses}>
          {children}
        </label>
      )}
    </div>
  );
});

Checkbox.displayName = 'Checkbox';

export default Checkbox;
