import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import { testConnection } from '../utils/database.js';

const seedMasterData = async () => {
  try {
    logger.info('🌱 Starting master data seeding...');

    // Test database connection first
    const isConnected = await testConnection();
    if (!isConnected) {
      throw new Error('Database connection failed');
    }

    // Seed Roles
    logger.info('📝 Seeding roles...');
    const roles = models.Role.getSystemRoles();
    for (const roleData of roles) {
      await models.Role.findOrCreate({
        where: { slug: roleData.slug },
        defaults: roleData,
      });
    }
    logger.info(`✅ Seeded ${roles.length} roles`);

    // Seed Permissions
    logger.info('📝 Seeding permissions...');
    const permissions = models.Permission.getSystemPermissions();
    for (const permissionData of permissions) {
      await models.Permission.findOrCreate({
        where: { slug: permissionData.slug },
        defaults: permissionData,
      });
    }
    logger.info(`✅ Seeded ${permissions.length} permissions`);

    // Seed License Editions
    logger.info('📝 Seeding license editions...');
    const licenseEditions = models.LicenseEdition.getDefaultEditions();
    for (const editionData of licenseEditions) {
      await models.LicenseEdition.findOrCreate({
        where: { code: editionData.code },
        defaults: editionData,
      });
    }
    logger.info(`✅ Seeded ${licenseEditions.length} license editions`);

    // Seed Designations
    logger.info('📝 Seeding designations...');
    const designations = models.Designation.getDefaultDesignations();
    for (const designationData of designations) {
      await models.Designation.findOrCreate({
        where: { code: designationData.code },
        defaults: designationData,
      });
    }
    logger.info(`✅ Seeded ${designations.length} designations`);

    // Seed Tally Products
    logger.info('📝 Seeding tally products...');
    const tallyProducts = models.TallyProduct.getDefaultProducts();
    for (const productData of tallyProducts) {
      await models.TallyProduct.findOrCreate({
        where: { code: productData.code },
        defaults: productData,
      });
    }
    logger.info(`✅ Seeded ${tallyProducts.length} tally products`);

    // Seed Staff Roles
    logger.info('📝 Seeding staff roles...');
    const staffRoles = models.StaffRole.getDefaultRoles();
    for (const roleData of staffRoles) {
      await models.StaffRole.findOrCreate({
        where: { code: roleData.code },
        defaults: roleData,
      });
    }
    logger.info(`✅ Seeded ${staffRoles.length} staff roles`);

    // Seed Industries
    logger.info('📝 Seeding industries...');
    const industries = models.Industry.getDefaultIndustries();
    for (const industryData of industries) {
      await models.Industry.findOrCreate({
        where: { code: industryData.code },
        defaults: industryData,
      });
    }
    logger.info(`✅ Seeded ${industries.length} industries`);

    // Seed Areas
    logger.info('📝 Seeding areas...');
    const areas = models.Area.getDefaultAreas();
    for (const areaData of areas) {
      await models.Area.findOrCreate({
        where: { code: areaData.code },
        defaults: areaData,
      });
    }
    logger.info(`✅ Seeded ${areas.length} areas`);

    // Seed Nature of Issues
    logger.info('📝 Seeding nature of issues...');
    const natureOfIssues = models.NatureOfIssue.getDefaultIssues();
    for (const issueData of natureOfIssues) {
      await models.NatureOfIssue.findOrCreate({
        where: { code: issueData.code },
        defaults: issueData,
      });
    }
    logger.info(`✅ Seeded ${natureOfIssues.length} nature of issues`);

    // Seed Additional Services
    logger.info('📝 Seeding additional services...');
    const additionalServices = models.AdditionalService.getDefaultServices();
    for (const serviceData of additionalServices) {
      await models.AdditionalService.findOrCreate({
        where: { code: serviceData.code },
        defaults: serviceData,
      });
    }
    logger.info(`✅ Seeded ${additionalServices.length} additional services`);

    // Seed Call Statuses
    logger.info('📝 Seeding call statuses...');
    const callStatuses = models.CallStatus.getDefaultStatuses();
    for (const statusData of callStatuses) {
      await models.CallStatus.findOrCreate({
        where: { code: statusData.code },
        defaults: statusData,
      });
    }
    logger.info(`✅ Seeded ${callStatuses.length} call statuses`);

    logger.info('✅ Master data seeding completed successfully!');
  } catch (error) {
    logger.error('❌ Master data seeding failed:', error);
    throw error;
  }
};

export default seedMasterData;
