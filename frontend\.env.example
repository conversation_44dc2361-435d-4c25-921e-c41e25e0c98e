# =================================
# TallyCRM Frontend Environment Configuration
# =================================

# Application Configuration
VITE_APP_NAME=TallyCRM
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=CRM for Tally Resellers

# API Configuration
# For development: use full URL with port
# For production: use relative path (comment out VITE_API_BASE_URL)
# VITE_API_BASE_URL=http://localhost:3001/api/v1
VITE_API_TIMEOUT=30000

# Development Configuration
VITE_DEV_PORT=3000
VITE_DEV_HOST=localhost

# Authentication Configuration
VITE_JWT_STORAGE_KEY=tallycrm_token
VITE_REFRESH_TOKEN_KEY=tallycrm_refresh_token
VITE_USER_STORAGE_KEY=tallycrm_user

# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key

# Google Maps Configuration (Optional)
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# Feature Flags
VITE_ENABLE_MOCK_API=true
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_PERFORMANCE_MONITORING=false

# UI Configuration
VITE_DEFAULT_THEME=light
VITE_ENABLE_DARK_MODE=true
VITE_DEFAULT_LANGUAGE=en

# File Upload Configuration
VITE_MAX_FILE_SIZE=5242880
VITE_ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx,xls,xlsx

# Pagination Configuration
VITE_DEFAULT_PAGE_SIZE=10
VITE_MAX_PAGE_SIZE=100

# Cache Configuration
VITE_CACHE_DURATION=300000
VITE_ENABLE_OFFLINE_MODE=false

# Development Tools
VITE_ENABLE_REDUX_DEVTOOLS=true
VITE_ENABLE_REACT_QUERY_DEVTOOLS=true

# Error Handling
VITE_ERROR_BOUNDARY_FALLBACK=true
VITE_TOAST_DURATION=5000

# Production Configuration (Override in production)
# VITE_ENABLE_MOCK_API=false
# VITE_ENABLE_ANALYTICS=true
# VITE_ENABLE_REDUX_DEVTOOLS=false
# VITE_ENABLE_REACT_QUERY_DEVTOOLS=false
