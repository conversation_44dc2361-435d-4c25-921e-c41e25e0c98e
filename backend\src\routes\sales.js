import express from 'express';
import { body, query, param } from 'express-validator';
import { validateRequest as validate } from '../middleware/validation.js';
import { authenticateToken, requirePermission, requireTenantAccess } from '../middleware/auth.js';
import {
  getSales,
  getSaleById,
  createSale,
  updateSale,
  deleteSale,
  getSalesStats,
} from '../controllers/salesController.js';

const router = express.Router();

// Apply authentication and tenant access to all routes
router.use(authenticateToken);
router.use(requireTenantAccess);

/**
 * @route   GET /api/sales
 * @desc    Get all sales with pagination and filters
 * @access  Private (requires sales.read permission)
 */
router.get('/', [
  requirePermission('sales.read'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('search')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters'),
  query('customerId')
    .optional()
    .isUUID()
    .withMessage('Customer ID must be a valid UUID'),
  query('salesExecutiveId')
    .optional()
    .isUUID()
    .withMessage('Sales executive ID must be a valid UUID'),
  query('saleType')
    .optional()
    .isIn(['new', 'renewal', 'upgrade', 'additional'])
    .withMessage('Invalid sale type'),
  query('status')
    .optional()
    .isIn(['draft', 'confirmed', 'delivered', 'cancelled'])
    .withMessage('Invalid status'),
  query('paymentStatus')
    .optional()
    .isIn(['pending', 'partial', 'paid', 'overdue'])
    .withMessage('Invalid payment status'),
  query('dateFrom')
    .optional()
    .isISO8601()
    .withMessage('Date from must be a valid date'),
  query('dateTo')
    .optional()
    .isISO8601()
    .withMessage('Date to must be a valid date'),
  query('sortBy')
    .optional()
    .isIn(['created_at', 'updated_at', 'sale_date', 'total_amount', 'sale_number'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['ASC', 'DESC'])
    .withMessage('Sort order must be ASC or DESC'),
  validate,
], getSales);

/**
 * @route   GET /api/sales/stats
 * @desc    Get sales statistics
 * @access  Private (requires sales.read permission)
 */
router.get('/stats', [
  requirePermission('sales.read'),
], getSalesStats);

/**
 * @route   GET /api/sales/:id
 * @desc    Get sale by ID
 * @access  Private (requires sales.read permission)
 */
router.get('/:id', [
  requirePermission('sales.read'),
  param('id')
    .isUUID()
    .withMessage('Sale ID must be a valid UUID'),
  validate,
], getSaleById);

/**
 * @route   POST /api/sales
 * @desc    Create new sale
 * @access  Private (requires sales.create permission)
 */
router.post('/', [
  requirePermission('sales.create'),
  body('customer_id')
    .notEmpty()
    .withMessage('Customer ID is required')
    .custom((value) => {
      // Allow both UUID and integer IDs for backward compatibility
      if (typeof value === 'string' && value.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        return true; // Valid UUID
      }
      if (typeof value === 'string' && value.match(/^\d+$/)) {
        return true; // Valid integer ID as string
      }
      if (typeof value === 'number' && Number.isInteger(value)) {
        return true; // Valid integer ID
      }
      throw new Error('Customer ID must be a valid UUID or integer');
    }),
  body('sales_executive_id')
    .optional()
    .custom((value) => {
      if (!value) return true; // Optional field
      // Allow both UUID and integer IDs for backward compatibility
      if (typeof value === 'string' && value.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        return true; // Valid UUID
      }
      if (typeof value === 'string' && value.match(/^\d+$/)) {
        return true; // Valid integer ID as string
      }
      if (typeof value === 'number' && Number.isInteger(value)) {
        return true; // Valid integer ID
      }
      throw new Error('Sales executive ID must be a valid UUID or integer');
    }),
  body('sale_type')
    .optional()
    .isIn(['new', 'renewal', 'upgrade', 'additional'])
    .withMessage('Invalid sale type'),
  body('sale_date')
    .optional()
    .isISO8601()
    .withMessage('Sale date must be a valid date'),
  body('total_amount')
    .isFloat({ min: 0 })
    .withMessage('Total amount must be a positive number'),
  body('paid_amount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Paid amount must be a positive number'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description must not exceed 500 characters'),
  body('remarks')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Remarks must not exceed 1000 characters'),
  body('sale_items')
    .optional()
    .isArray()
    .withMessage('Sale items must be an array'),
  body('sale_items.*.product_id')
    .optional()
    .isUUID()
    .withMessage('Product ID must be a valid UUID'),
  body('sale_items.*.quantity')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Quantity must be a positive integer'),
  body('sale_items.*.unit_price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Unit price must be a positive number'),
  body('sale_items.*.total_price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Total price must be a positive number'),
  validate,
], createSale);

/**
 * @route   PUT /api/sales/:id
 * @desc    Update sale
 * @access  Private (requires sales.update permission)
 */
router.put('/:id', [
  requirePermission('sales.update'),
  param('id')
    .isUUID()
    .withMessage('Sale ID must be a valid UUID'),
  body('customer_id')
    .optional()
    .custom((value) => {
      if (!value) return true; // Optional field
      // Allow both UUID and integer IDs for backward compatibility
      if (typeof value === 'string' && value.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        return true; // Valid UUID
      }
      if (typeof value === 'string' && value.match(/^\d+$/)) {
        return true; // Valid integer ID as string
      }
      if (typeof value === 'number' && Number.isInteger(value)) {
        return true; // Valid integer ID
      }
      throw new Error('Customer ID must be a valid UUID or integer');
    }),
  body('sales_executive_id')
    .optional()
    .custom((value) => {
      if (!value) return true; // Optional field
      // Allow both UUID and integer IDs for backward compatibility
      if (typeof value === 'string' && value.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        return true; // Valid UUID
      }
      if (typeof value === 'string' && value.match(/^\d+$/)) {
        return true; // Valid integer ID as string
      }
      if (typeof value === 'number' && Number.isInteger(value)) {
        return true; // Valid integer ID
      }
      throw new Error('Sales executive ID must be a valid UUID or integer');
    }),
  body('sale_type')
    .optional()
    .isIn(['new', 'renewal', 'upgrade', 'additional'])
    .withMessage('Invalid sale type'),
  body('sale_date')
    .optional()
    .isISO8601()
    .withMessage('Sale date must be a valid date'),
  body('total_amount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Total amount must be a positive number'),
  body('paid_amount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Paid amount must be a positive number'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description must not exceed 500 characters'),
  body('remarks')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Remarks must not exceed 1000 characters'),
  body('status')
    .optional()
    .isIn(['draft', 'confirmed', 'delivered', 'cancelled'])
    .withMessage('Invalid status'),
  validate,
], updateSale);

/**
 * @route   DELETE /api/sales/:id
 * @desc    Delete sale
 * @access  Private (requires sales.delete permission)
 */
router.delete('/:id', [
  requirePermission('sales.delete'),
  param('id')
    .isUUID()
    .withMessage('Sale ID must be a valid UUID'),
  validate,
], deleteSale);

export default router;
