import models from '../models/index.js';
import { logger } from '../utils/logger.js';

const subscriptionPlans = [
  {
    name: 'Starter',
    slug: 'starter',
    description: 'Perfect for small Tally resellers just getting started',
    price_monthly: 999.00,
    price_yearly: 9990.00,
    currency: 'INR',
    trial_days: 14,
    max_users: 3,
    max_customers: 50,
    max_service_calls: 25,
    max_storage_gb: 1,
    features: {
      customer_management: true,
      service_calls: true,
      basic_reports: true,
      email_support: true,
      mobile_app: false,
      advanced_reports: false,
      api_access: false,
      custom_branding: false,
      priority_support: false,
      data_export: false,
    },
    is_active: true,
    is_popular: false,
    sort_order: 1,
  },
  {
    name: 'Professional',
    slug: 'professional',
    description: 'Ideal for growing Tally resellers with expanding customer base',
    price_monthly: 2499.00,
    price_yearly: 24990.00,
    currency: 'INR',
    trial_days: 14,
    max_users: 10,
    max_customers: 200,
    max_service_calls: 100,
    max_storage_gb: 5,
    features: {
      customer_management: true,
      service_calls: true,
      basic_reports: true,
      email_support: true,
      mobile_app: true,
      advanced_reports: true,
      api_access: true,
      custom_branding: false,
      priority_support: false,
      data_export: true,
      bulk_operations: true,
      custom_fields: true,
    },
    is_active: true,
    is_popular: true,
    sort_order: 2,
  },
  {
    name: 'Business',
    slug: 'business',
    description: 'Comprehensive solution for established Tally resellers',
    price_monthly: 4999.00,
    price_yearly: 49990.00,
    currency: 'INR',
    trial_days: 14,
    max_users: 25,
    max_customers: 500,
    max_service_calls: 250,
    max_storage_gb: 15,
    features: {
      customer_management: true,
      service_calls: true,
      basic_reports: true,
      email_support: true,
      mobile_app: true,
      advanced_reports: true,
      api_access: true,
      custom_branding: true,
      priority_support: true,
      data_export: true,
      bulk_operations: true,
      custom_fields: true,
      workflow_automation: true,
      advanced_analytics: true,
      white_labeling: true,
    },
    is_active: true,
    is_popular: false,
    sort_order: 3,
  },
  {
    name: 'Enterprise',
    slug: 'enterprise',
    description: 'Ultimate solution for large Tally reseller organizations',
    price_monthly: 9999.00,
    price_yearly: 99990.00,
    currency: 'INR',
    trial_days: 30,
    max_users: 100,
    max_customers: 2000,
    max_service_calls: 1000,
    max_storage_gb: 50,
    features: {
      customer_management: true,
      service_calls: true,
      basic_reports: true,
      email_support: true,
      mobile_app: true,
      advanced_reports: true,
      api_access: true,
      custom_branding: true,
      priority_support: true,
      data_export: true,
      bulk_operations: true,
      custom_fields: true,
      workflow_automation: true,
      advanced_analytics: true,
      white_labeling: true,
      dedicated_support: true,
      custom_integrations: true,
      sla_guarantee: true,
      audit_logs: true,
      advanced_security: true,
    },
    is_active: true,
    is_popular: false,
    sort_order: 4,
  },
];

export default async function seedSubscriptionPlans() {
  try {
    logger.info('🌱 Seeding subscription plans...');

    // Check if plans already exist
    const existingPlans = await models.SubscriptionPlan.findAll();
    if (existingPlans.length > 0) {
      logger.info('📋 Subscription plans already exist, skipping seed');
      return;
    }

    // Create subscription plans
    for (const planData of subscriptionPlans) {
      await models.SubscriptionPlan.create(planData);
      logger.info(`✅ Created subscription plan: ${planData.name}`);
    }

    logger.info('🎉 Subscription plans seeded successfully');
  } catch (error) {
    logger.error('❌ Error seeding subscription plans:', error);
    throw error;
  }
}
