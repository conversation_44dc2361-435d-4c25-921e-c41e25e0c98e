import axios from 'axios';
import { API_CONFIG, STORAGE_KEYS, FEATURES } from '../utils/constants';

// Create axios instance
const api = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Helper function to clear auth data and redirect to login
const clearAuthAndRedirect = () => {
  console.log('Clearing authentication data and redirecting to login');

  // Clear all possible token storage keys
  localStorage.removeItem(STORAGE_KEYS.TOKEN);
  localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
  localStorage.removeItem(STORAGE_KEYS.USER);

  // Also clear any legacy keys that might exist
  localStorage.removeItem('token');
  localStorage.removeItem('refreshToken');
  localStorage.removeItem('user');
  localStorage.removeItem('auth-storage');

  // Avoid redirect loop if already on login page
  if (!window.location.pathname.includes('/auth/login')) {
    window.location.href = '/auth/login';
  }
};

// Helper function to check if error indicates invalid token
const isInvalidTokenError = (error) => {
  const response = error.response;
  if (!response) return false;

  // Check for 401 status
  if (response.status === 401) return true;

  // Check for specific error codes in response data
  const data = response.data;
  if (data && (
    data.code === 'INVALID_TOKEN' ||
    data.code === 'TOKEN_EXPIRED' ||
    data.code === 'AUTH_REQUIRED' ||
    data.message === 'Invalid access token' ||
    data.message === 'Token expired' ||
    data.message === 'Authentication required'
  )) {
    return true;
  }

  return false;
};

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle invalid token errors
    if (isInvalidTokenError(error) && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh token
        const refreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
        if (refreshToken) {
          const response = await axios.post(`${API_CONFIG.BASE_URL}/auth/refresh`, {
            refreshToken,
          });

          const { token } = response.data;
          localStorage.setItem(STORAGE_KEYS.TOKEN, token);

          // Retry original request
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return api(originalRequest);
        } else {
          // No refresh token, redirect to login
          clearAuthAndRedirect();
          return Promise.reject(error);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        console.error('Token refresh failed:', refreshError);
        clearAuthAndRedirect();
        return Promise.reject(refreshError);
      }
    }

    // Handle other authentication errors that should redirect to login
    if (error.response?.status === 403 && error.response?.data?.code === 'ACCESS_DENIED') {
      clearAuthAndRedirect();
      return Promise.reject(error);
    }

    return Promise.reject(error);
  }
);

// Mock API responses when ENABLE_MOCK_API is true
const mockResponses = {
  // Auth endpoints
  'POST /auth/login': {
    data: {
      token: 'mock-jwt-token',
      refreshToken: 'mock-refresh-token',
      user: {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'admin',
        permissions: ['read', 'write', 'delete'],
      },
    },
  },
  'POST /auth/refresh': {
    data: {
      token: 'new-mock-jwt-token',
    },
  },
  'POST /auth/logout': {
    data: { message: 'Logged out successfully' },
  },

  // Dashboard endpoints
  'GET /dashboard/stats': {
    data: {
      totalCustomers: 1234,
      activeServices: 89,
      monthlyRevenue: 245000,
      pendingTasks: 23,
    },
  },

  // Customer endpoints
  'GET /customers': {
    data: {
      customers: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        pages: 0,
      },
    },
  },
  'POST /customers': {
    data: {
      success: true,
      message: 'Customer created successfully',
      data: {
        customer: {
          id: 'mock-customer-id',
          company_name: 'Mock Company',
          customer_code: 'MOCK001',
          created_at: new Date().toISOString(),
        }
      }
    },
  },
};

// Mock API function
const mockApiCall = (method, url) => {
  const key = `${method.toUpperCase()} ${url}`;
  const mockResponse = mockResponses[key];

  if (mockResponse) {
    return Promise.resolve({ data: mockResponse.data });
  }

  return Promise.reject(new Error(`Mock API: No mock response found for ${key}`));
};

// API service functions
export const apiService = {
  // Generic HTTP methods
  get: (url, config = {}) => {
    if (FEATURES.ENABLE_MOCK_API) {
      return mockApiCall('GET', url);
    }
    return api.get(url, config);
  },

  post: (url, data = {}, config = {}) => {
    console.log(`apiService.post called: ${url}`, data);
    if (FEATURES.ENABLE_MOCK_API) {
      console.log('Using mock API for POST request');
      return mockApiCall('POST', url);
    }
    console.log('Making real API POST request');
    return api.post(url, data, config);
  },

  put: (url, data = {}, config = {}) => {
    if (FEATURES.ENABLE_MOCK_API) {
      return mockApiCall('PUT', url);
    }
    return api.put(url, data, config);
  },

  patch: (url, data = {}, config = {}) => {
    if (FEATURES.ENABLE_MOCK_API) {
      return mockApiCall('PATCH', url);
    }
    return api.patch(url, data, config);
  },

  delete: (url, config = {}) => {
    if (FEATURES.ENABLE_MOCK_API) {
      return mockApiCall('DELETE', url);
    }
    return api.delete(url, config);
  },

  // File upload
  upload: (url, formData, config = {}) => {
    const uploadConfig = {
      ...config,
      headers: {
        ...config.headers,
        'Content-Type': 'multipart/form-data',
      },
    };

    if (FEATURES.ENABLE_MOCK_API) {
      return Promise.resolve({ data: { message: 'File uploaded successfully' } });
    }

    return api.post(url, formData, uploadConfig);
  },
};

// Auth API
export const authAPI = {
  login: (credentials) => apiService.post('/auth/login', credentials),
  logout: () => apiService.post('/auth/logout'),
  refresh: (refreshToken) => apiService.post('/auth/refresh', { refreshToken }),
  register: (userData) => apiService.post('/auth/register', userData),
  forgotPassword: (email) => apiService.post('/auth/forgot-password', { email }),
  resetPassword: (token, password) => apiService.post('/auth/reset-password', { token, password }),
  verifyEmail: (token) => apiService.post('/auth/verify-email', { token }),
};

// Dashboard API
export const dashboardAPI = {
  getStats: () => apiService.get('/dashboard/stats'),
  getRecentActivities: () => apiService.get('/dashboard/activities'),
  getChartData: (type) => apiService.get(`/dashboard/charts/${type}`),
};

// Master Data API
export const masterDataAPI = {
  // Industries
  getIndustries: (params = {}) => apiService.get('/master-data/industries', { params }),
  getIndustryById: (id) => apiService.get(`/master-data/industries/${id}`),
  createIndustry: (data) => apiService.post('/master-data/industries', data),
  updateIndustry: (id, data) => apiService.put(`/master-data/industries/${id}`, data),
  deleteIndustry: (id) => apiService.delete(`/master-data/industries/${id}`),

  // Areas
  getAreas: (params = {}) => apiService.get('/master-data/areas', { params }),
  getAreaById: (id) => apiService.get(`/master-data/areas/${id}`),
  createArea: (data) => apiService.post('/master-data/areas', data),
  updateArea: (id, data) => apiService.put(`/master-data/areas/${id}`, data),
  deleteArea: (id) => apiService.delete(`/master-data/areas/${id}`),

  // License Editions
  getLicenseEditions: (params = {}) => apiService.get('/master-data/license-editions', { params }),
  getLicenseEditionById: (id) => apiService.get(`/master-data/license-editions/${id}`),
  createLicenseEdition: (data) => apiService.post('/master-data/license-editions', data),
  updateLicenseEdition: (id, data) => apiService.put(`/master-data/license-editions/${id}`, data),
  deleteLicenseEdition: (id) => apiService.delete(`/master-data/license-editions/${id}`),

  // Tally Products
  getTallyProducts: (params = {}) => apiService.get('/master-data/tally-products', { params }),
  getTallyProductById: (id) => apiService.get(`/master-data/tally-products/${id}`),
  createTallyProduct: (data) => apiService.post('/master-data/tally-products', data),
  updateTallyProduct: (id, data) => apiService.put(`/master-data/tally-products/${id}`, data),
  deleteTallyProduct: (id) => apiService.delete(`/master-data/tally-products/${id}`),

  // Executives
  getExecutives: (params = {}) => apiService.get('/executives', { params }),
  getExecutiveById: (id) => apiService.get(`/executives/${id}`),
  createExecutive: (data) => apiService.post('/executives', data),
  updateExecutive: (id, data) => apiService.put(`/executives/${id}`, data),
  deleteExecutive: (id) => apiService.delete(`/executives/${id}`),

  // Additional Services
  getAdditionalServices: (params = {}) => apiService.get('/master-data/additional-services', { params }),
  getAdditionalServiceById: (id) => apiService.get(`/master-data/additional-services/${id}`),
  createAdditionalService: (data) => apiService.post('/master-data/additional-services', data),
  updateAdditionalService: (id, data) => apiService.put(`/master-data/additional-services/${id}`, data),
  deleteAdditionalService: (id) => apiService.delete(`/master-data/additional-services/${id}`),

  // Search across all master data
  searchMasterData: (query, type = null) => {
    const params = { search: query };
    if (type) params.type = type;
    return apiService.get('/master-data/search', { params });
  }
};

// Customer API
export const customerAPI = {
  // Get all customers with advanced filtering
  getAll: (params = {}) => apiService.get('/customers', { params }),

  // Get customer by ID with optional relations and stats
  getById: (id, options = {}) => {
    const { includeRelations = true, includeStats = false } = options;
    return apiService.get(`/customers/${id}`, {
      params: { includeRelations, includeStats }
    });
  },

  // Create new customer with comprehensive validation
  create: (data) => {
    console.log('customerAPI.create called with data:', data);
    // Frontend validation before sending to backend
    const validatedData = customerAPI.validateCustomerData(data);
    console.log('Validated data:', validatedData);
    console.log('Making POST request to /customers');
    return apiService.post('/customers', validatedData);
  },

  // Update existing customer
  update: (id, data) => {
    const validatedData = customerAPI.validateCustomerData(data, false);
    return apiService.put(`/customers/${id}`, validatedData);
  },

  // Delete customer with options
  delete: (id, options = {}) => {
    const { force = false, reason } = options;
    const params = {};
    if (force) params.force = force;
    if (reason) params.reason = reason;

    return apiService.delete(`/customers/${id}`, { params });
  },

  // Get customer statistics
  getStats: (filters = {}) => apiService.get('/customers/stats', { params: filters }),

  // Search customers
  search: (query, filters = {}) => {
    return apiService.get('/customers', {
      params: { search: query, ...filters }
    });
  },

  // Validate customer data on frontend
  validateCustomerData: (data, isCreate = true) => {
    const validatedData = { ...data };

    // Clean and format data
    if (validatedData.company_name) {
      validatedData.company_name = validatedData.company_name.trim();
    }

    if (validatedData.customer_code) {
      validatedData.customer_code = validatedData.customer_code.toUpperCase().trim();
    }

    if (validatedData.email) {
      validatedData.email = validatedData.email.toLowerCase().trim();
    }

    if (validatedData.gst_number) {
      validatedData.gst_number = validatedData.gst_number.toUpperCase().replace(/\s/g, '');
    }

    if (validatedData.pan_number) {
      validatedData.pan_number = validatedData.pan_number.toUpperCase().replace(/\s/g, '');
    }

    // Remove empty strings and convert to null
    Object.keys(validatedData).forEach(key => {
      if (validatedData[key] === '') {
        validatedData[key] = null;
      }
    });

    return validatedData;
  },

  // Frontend validation rules
  validationRules: {
    company_name: {
      required: true,
      minLength: 2,
      maxLength: 200,
      pattern: /^[a-zA-Z0-9\s\-\.\&\(\)]+$/,
      message: 'Company name must be 2-200 characters and contain only letters, numbers, spaces, and basic punctuation'
    },
    customer_code: {
      required: false,
      minLength: 2,
      maxLength: 20,
      pattern: /^[A-Z0-9]+$/,
      message: 'Customer code must be 2-20 characters and contain only uppercase letters and numbers'
    },
    contact_person: {
      required: false,
      minLength: 2,
      maxLength: 100,
      pattern: /^[a-zA-Z\s\.]+$/,
      message: 'Contact person must be 2-100 characters and contain only letters, spaces, and periods'
    },
    email: {
      required: false,
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: 'Please provide a valid email address'
    },
    phone: {
      required: false,
      pattern: /^[\+]?[1-9][\d]{0,15}$/,
      message: 'Please provide a valid phone number'
    },
    gst_number: {
      required: false,
      length: 15,
      pattern: /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/,
      message: 'GST number must be exactly 15 characters in valid format'
    },
    pan_number: {
      required: false,
      length: 10,
      pattern: /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,
      message: 'PAN number must be exactly 10 characters in valid format'
    }
  },

  // Validate single field
  validateField: (fieldName, value, isRequired = false) => {
    const rule = customerAPI.validationRules[fieldName];
    if (!rule) return { isValid: true };

    // Check if required
    if (isRequired && (!value || value.trim() === '')) {
      return { isValid: false, message: `${fieldName} is required` };
    }

    // Skip validation if field is empty and not required
    if (!value || value.trim() === '') {
      return { isValid: true };
    }

    // Check length constraints
    if (rule.minLength && value.length < rule.minLength) {
      return { isValid: false, message: rule.message };
    }

    if (rule.maxLength && value.length > rule.maxLength) {
      return { isValid: false, message: rule.message };
    }

    if (rule.length && value.length !== rule.length) {
      return { isValid: false, message: rule.message };
    }

    // Check pattern
    if (rule.pattern && !rule.pattern.test(value)) {
      return { isValid: false, message: rule.message };
    }

    return { isValid: true };
  },

  // Validate entire form
  validateForm: (data, requiredFields = []) => {
    const errors = {};

    // Check required fields
    requiredFields.forEach(field => {
      if (!data[field] || data[field].trim() === '') {
        errors[field] = `${field.replace('_', ' ')} is required`;
      }
    });

    // Validate each field
    Object.keys(data).forEach(field => {
      if (customerAPI.validationRules[field]) {
        const validation = customerAPI.validateField(
          field,
          data[field],
          requiredFields.includes(field)
        );

        if (!validation.isValid) {
          errors[field] = validation.message;
        }
      }
    });

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }
};

export default api;
