#!/usr/bin/env node

/**
 * Fresh Database Setup Script
 * Complete setup for a new/empty database
 */

import { logger } from './src/utils/logger.js';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

const STEPS = [
  {
    name: 'Test Database Connection',
    command: 'node test-pg-connection.js',
    description: 'Verify database connectivity'
  },
  {
    name: 'Run Database Migrations',
    command: 'npm run migrate',
    description: 'Create all database tables and schema'
  },
  {
    name: 'Seed Admin User',
    command: 'npm run seed:admin',
    description: 'Create <EMAIL> user'
  },
  {
    name: 'Seed Subscription Plans',
    command: 'npm run seed:plans',
    description: 'Create SaaS subscription plans'
  },
  {
    name: 'Seed Master Data',
    command: 'npm run seed:master',
    description: 'Create license editions, products, industries, etc.'
  }
];

const runCommand = async (command, description) => {
  try {
    logger.info(`🔄 ${description}...`);
    logger.debug(`Running: ${command}`);
    
    const { stdout, stderr } = await execAsync(command);
    
    if (stderr && !stderr.includes('info:') && !stderr.includes('warn:')) {
      logger.warn('Command stderr:', stderr);
    }
    
    if (stdout) {
      logger.debug('Command output:', stdout);
    }
    
    logger.info(`✅ ${description} completed`);
    return true;
  } catch (error) {
    logger.error(`❌ ${description} failed:`, error.message);
    if (error.stdout) logger.debug('stdout:', error.stdout);
    if (error.stderr) logger.debug('stderr:', error.stderr);
    throw error;
  }
};

const setupFreshDatabase = async () => {
  try {
    logger.info('🚀 Starting fresh database setup...');
    logger.info('📋 This will set up a complete TallyCRM database from scratch');
    
    // Show database configuration
    logger.info('🔧 Database Configuration:');
    logger.info(`   Host: ${process.env.DB_HOST || 'localhost'}`);
    logger.info(`   Port: ${process.env.DB_PORT || '5432'}`);
    logger.info(`   Database: ${process.env.DB_NAME || 'tallyerp'}`);
    logger.info(`   Username: ${process.env.DB_USERNAME || 'postgres'}`);
    
    // Execute each step
    for (let i = 0; i < STEPS.length; i++) {
      const step = STEPS[i];
      logger.info(`\n📋 Step ${i + 1}/${STEPS.length}: ${step.name}`);
      
      try {
        await runCommand(step.command, step.description);
      } catch (error) {
        logger.error(`❌ Step ${i + 1} failed. Stopping setup.`);
        throw error;
      }
    }
    
    // Optional: Seed sample data
    logger.info('\n📋 Optional: Seeding sample data...');
    try {
      await runCommand('npm run seed:sample', 'Create sample customers and data');
      logger.info('✅ Sample data seeded successfully');
    } catch (error) {
      logger.warn('⚠️ Sample data seeding skipped (this is normal if no demo tenant exists)');
    }
    
    // Success summary
    logger.info('\n🎉 Fresh database setup completed successfully!');
    logger.info('📊 What was created:');
    logger.info('   ✅ Database schema (all tables)');
    logger.info('   ✅ Admin user: <EMAIL> / Admin@123');
    logger.info('   ✅ 4 Subscription plans');
    logger.info('   ✅ 4 License editions');
    logger.info('   ✅ 6 Tally products');
    logger.info('   ✅ 10 Industries');
    logger.info('   ✅ 20 Areas');
    logger.info('   ✅ 9 Designations');
    logger.info('   ✅ 6 Staff roles');
    logger.info('   ✅ 7 Nature of issues');
    logger.info('   ✅ 6 Call statuses');
    logger.info('   ✅ 6 Additional services');
    logger.info('   ✅ Sample data (if applicable)');
    
    logger.info('\n🚀 Your TallyCRM database is ready for production!');
    logger.info('🔐 Login with: <EMAIL> / Admin@123');
    
  } catch (error) {
    logger.error('\n❌ Fresh database setup failed:', error.message);
    logger.error('💡 Troubleshooting tips:');
    logger.error('   1. Check database connection: node test-pg-connection.js');
    logger.error('   2. Verify .env file has correct database credentials');
    logger.error('   3. Ensure database exists and is accessible');
    logger.error('   4. Check logs above for specific error details');
    logger.error('   5. Try running steps manually: npm run migrate, npm run seed:all');
    
    process.exit(1);
  }
};

// Show help if requested
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
TallyCRM Fresh Database Setup

Usage:
  node setup-fresh-database.js

This script will:
1. Test database connection
2. Run all migrations (create tables)
3. Seed admin user (<EMAIL>)
4. Seed subscription plans
5. Seed master data (products, industries, etc.)
6. Optionally seed sample data

Prerequisites:
- PostgreSQL database created and accessible
- Correct database credentials in .env file
- Node.js and npm dependencies installed

Environment Variables Required:
- DB_HOST (database host)
- DB_PORT (database port)
- DB_NAME (database name)
- DB_USERNAME (database username)
- DB_PASSWORD (database password)

For troubleshooting, see PRODUCTION_SETUP.md
`);
  process.exit(0);
}

// Run the setup
setupFreshDatabase();
