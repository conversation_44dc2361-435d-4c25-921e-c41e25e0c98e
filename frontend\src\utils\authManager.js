/**
 * Authentication Manager
 * Singleton to manage authentication state and prevent multiple checks
 */

import { STORAGE_KEYS } from './constants';

class AuthManager {
  constructor() {
    this.isInitialized = false;
    this.authState = {
      isAuthenticated: false,
      user: null,
      isLoading: true
    };
    this.listeners = new Set();
  }

  // Subscribe to auth state changes
  subscribe(callback) {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  }

  // Notify all listeners of state changes
  notifyListeners() {
    this.listeners.forEach(callback => callback(this.authState));
  }

  // Initialize authentication state (only once)
  async initialize() {
    if (this.isInitialized) {
      return this.authState;
    }

    this.isInitialized = true;
    console.log('AuthManager: Initializing authentication state...');

    try {
      const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
      const userData = localStorage.getItem(STORAGE_KEYS.USER);

      console.log('AuthManager: Token exists:', !!token, 'User data exists:', !!userData);

      if (token && userData) {
        try {
          const parsedUser = JSON.parse(userData);
          this.authState = {
            isAuthenticated: true,
            user: parsedUser,
            isLoading: false
          };
          console.log('AuthManager: User authenticated from storage:', parsedUser.email || parsedUser.name);
        } catch (parseError) {
          console.error('AuthManager: Failed to parse stored user data:', parseError);
          this.clearAuthData();
          this.authState = {
            isAuthenticated: false,
            user: null,
            isLoading: false
          };
        }
      } else {
        console.log('AuthManager: No authentication data found');
        this.authState = {
          isAuthenticated: false,
          user: null,
          isLoading: false
        };
      }
    } catch (error) {
      console.error('AuthManager: Auth check failed:', error);
      this.clearAuthData();
      this.authState = {
        isAuthenticated: false,
        user: null,
        isLoading: false
      };
    }

    this.notifyListeners();
    return this.authState;
  }

  // Update authentication state
  setAuthState(newState) {
    this.authState = { ...this.authState, ...newState };
    this.notifyListeners();
  }

  // Clear authentication data
  clearAuthData() {
    localStorage.removeItem(STORAGE_KEYS.TOKEN);
    localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
    localStorage.removeItem(STORAGE_KEYS.USER);
  }

  // Get current auth state
  getAuthState() {
    return this.authState;
  }

  // Reset the manager (for testing/cleanup)
  reset() {
    this.isInitialized = false;
    this.authState = {
      isAuthenticated: false,
      user: null,
      isLoading: true
    };
    this.listeners.clear();
  }
}

// Create singleton instance
export const authManager = new AuthManager();
