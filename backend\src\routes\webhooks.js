import express from 'express';
import Stripe from 'stripe';
import models from '../models/index.js';
import { logger } from '../utils/logger.js';

const router = express.Router();
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

/**
 * Stripe webhook handler
 */
router.post('/stripe', express.raw({ type: 'application/json' }), async (req, res) => {
  const sig = req.headers['stripe-signature'];
  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, process.env.STRIPE_WEBHOOK_SECRET);
  } catch (err) {
    logger.error('Webhook signature verification failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  logger.info('Stripe webhook received:', { type: event.type, id: event.id });

  try {
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object);
        break;

      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object);
        break;

      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object);
        break;

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object);
        break;

      case 'invoice.created':
        await handleInvoiceCreated(event.data.object);
        break;

      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object);
        break;

      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(event.data.object);
        break;

      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(event.data.object);
        break;

      case 'payment_intent.payment_failed':
        await handlePaymentIntentFailed(event.data.object);
        break;

      default:
        logger.info('Unhandled webhook event type:', event.type);
    }

    res.json({ received: true });
  } catch (error) {
    logger.error('Webhook handler error:', error);
    res.status(500).json({ error: 'Webhook handler failed' });
  }
});

/**
 * Handle checkout session completed
 */
async function handleCheckoutSessionCompleted(session) {
  const { tenant_id, plan_id, interval } = session.metadata;

  if (!tenant_id || !plan_id) {
    logger.error('Missing metadata in checkout session:', session.id);
    return;
  }

  // Get the subscription from Stripe
  const stripeSubscription = await stripe.subscriptions.retrieve(session.subscription);
  
  // Get the plan
  const plan = await models.SubscriptionPlan.findByPk(plan_id);
  if (!plan) {
    logger.error('Plan not found:', plan_id);
    return;
  }

  // Create or update subscription
  const [subscription] = await models.Subscription.upsert({
    tenant_id,
    plan_id,
    stripe_subscription_id: stripeSubscription.id,
    stripe_customer_id: stripeSubscription.customer,
    status: stripeSubscription.status,
    current_period_start: new Date(stripeSubscription.current_period_start * 1000),
    current_period_end: new Date(stripeSubscription.current_period_end * 1000),
    trial_start: stripeSubscription.trial_start ? new Date(stripeSubscription.trial_start * 1000) : null,
    trial_end: stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000) : null,
    cancel_at_period_end: stripeSubscription.cancel_at_period_end,
    canceled_at: stripeSubscription.canceled_at ? new Date(stripeSubscription.canceled_at * 1000) : null,
    ended_at: stripeSubscription.ended_at ? new Date(stripeSubscription.ended_at * 1000) : null,
    quantity: stripeSubscription.quantity,
    amount: stripeSubscription.items.data[0].price.unit_amount / 100,
    currency: stripeSubscription.currency.toUpperCase(),
    interval: interval || 'month',
    interval_count: stripeSubscription.items.data[0].price.recurring.interval_count,
  });

  // Update tenant subscription status
  await models.Tenant.update(
    {
      subscription_plan: plan.slug,
      subscription_status: 'active',
      subscription_expires_at: new Date(stripeSubscription.current_period_end * 1000),
    },
    { where: { id: tenant_id } }
  );

  logger.info('Subscription created from checkout:', {
    tenantId: tenant_id,
    subscriptionId: subscription.id,
    stripeSubscriptionId: stripeSubscription.id,
  });
}

/**
 * Handle subscription created
 */
async function handleSubscriptionCreated(stripeSubscription) {
  // This is usually handled by checkout.session.completed
  logger.info('Subscription created:', stripeSubscription.id);
}

/**
 * Handle subscription updated
 */
async function handleSubscriptionUpdated(stripeSubscription) {
  const subscription = await models.Subscription.findOne({
    where: { stripe_subscription_id: stripeSubscription.id },
  });

  if (!subscription) {
    logger.error('Subscription not found for update:', stripeSubscription.id);
    return;
  }

  await subscription.update({
    status: stripeSubscription.status,
    current_period_start: new Date(stripeSubscription.current_period_start * 1000),
    current_period_end: new Date(stripeSubscription.current_period_end * 1000),
    cancel_at_period_end: stripeSubscription.cancel_at_period_end,
    canceled_at: stripeSubscription.canceled_at ? new Date(stripeSubscription.canceled_at * 1000) : null,
    ended_at: stripeSubscription.ended_at ? new Date(stripeSubscription.ended_at * 1000) : null,
  });

  // Update tenant status
  await models.Tenant.update(
    {
      subscription_status: stripeSubscription.status,
      subscription_expires_at: new Date(stripeSubscription.current_period_end * 1000),
    },
    { where: { id: subscription.tenant_id } }
  );

  logger.info('Subscription updated:', {
    subscriptionId: subscription.id,
    status: stripeSubscription.status,
  });
}

/**
 * Handle subscription deleted
 */
async function handleSubscriptionDeleted(stripeSubscription) {
  const subscription = await models.Subscription.findOne({
    where: { stripe_subscription_id: stripeSubscription.id },
  });

  if (!subscription) {
    logger.error('Subscription not found for deletion:', stripeSubscription.id);
    return;
  }

  await subscription.update({
    status: 'canceled',
    ended_at: new Date(),
  });

  // Update tenant status
  await models.Tenant.update(
    {
      subscription_status: 'inactive',
    },
    { where: { id: subscription.tenant_id } }
  );

  logger.info('Subscription deleted:', {
    subscriptionId: subscription.id,
    tenantId: subscription.tenant_id,
  });
}

/**
 * Handle invoice created
 */
async function handleInvoiceCreated(stripeInvoice) {
  const subscription = await models.Subscription.findOne({
    where: { stripe_subscription_id: stripeInvoice.subscription },
  });

  if (!subscription) {
    logger.error('Subscription not found for invoice:', stripeInvoice.subscription);
    return;
  }

  const invoiceNumber = await models.Invoice.generateInvoiceNumber();

  await models.Invoice.create({
    tenant_id: subscription.tenant_id,
    subscription_id: subscription.id,
    stripe_invoice_id: stripeInvoice.id,
    invoice_number: invoiceNumber,
    status: stripeInvoice.status,
    amount_due: stripeInvoice.amount_due / 100,
    amount_paid: stripeInvoice.amount_paid / 100,
    amount_remaining: stripeInvoice.amount_remaining / 100,
    subtotal: stripeInvoice.subtotal / 100,
    tax_amount: stripeInvoice.tax / 100,
    total: stripeInvoice.total / 100,
    currency: stripeInvoice.currency.toUpperCase(),
    period_start: new Date(stripeInvoice.period_start * 1000),
    period_end: new Date(stripeInvoice.period_end * 1000),
    due_date: stripeInvoice.due_date ? new Date(stripeInvoice.due_date * 1000) : null,
    billing_reason: stripeInvoice.billing_reason,
    description: stripeInvoice.description,
    line_items: stripeInvoice.lines.data,
  });

  logger.info('Invoice created:', {
    invoiceNumber,
    stripeInvoiceId: stripeInvoice.id,
    tenantId: subscription.tenant_id,
  });
}

/**
 * Handle invoice payment succeeded
 */
async function handleInvoicePaymentSucceeded(stripeInvoice) {
  const invoice = await models.Invoice.findOne({
    where: { stripe_invoice_id: stripeInvoice.id },
  });

  if (!invoice) {
    logger.error('Invoice not found for payment:', stripeInvoice.id);
    return;
  }

  await invoice.update({
    status: 'paid',
    amount_paid: stripeInvoice.amount_paid / 100,
    amount_remaining: stripeInvoice.amount_remaining / 100,
    paid_at: new Date(),
  });

  logger.info('Invoice payment succeeded:', {
    invoiceId: invoice.id,
    amount: stripeInvoice.amount_paid / 100,
  });
}

/**
 * Handle invoice payment failed
 */
async function handleInvoicePaymentFailed(stripeInvoice) {
  const invoice = await models.Invoice.findOne({
    where: { stripe_invoice_id: stripeInvoice.id },
  });

  if (!invoice) {
    logger.error('Invoice not found for failed payment:', stripeInvoice.id);
    return;
  }

  await invoice.update({
    attempt_count: stripeInvoice.attempt_count,
    next_payment_attempt: stripeInvoice.next_payment_attempt 
      ? new Date(stripeInvoice.next_payment_attempt * 1000) 
      : null,
  });

  logger.info('Invoice payment failed:', {
    invoiceId: invoice.id,
    attemptCount: stripeInvoice.attempt_count,
  });
}

/**
 * Handle payment intent succeeded
 */
async function handlePaymentIntentSucceeded(paymentIntent) {
  // Find related invoice
  const invoice = await models.Invoice.findOne({
    where: { stripe_invoice_id: paymentIntent.invoice },
  });

  if (!invoice) {
    logger.error('Invoice not found for payment intent:', paymentIntent.invoice);
    return;
  }

  // Create payment record
  await models.Payment.create({
    tenant_id: invoice.tenant_id,
    invoice_id: invoice.id,
    stripe_payment_intent_id: paymentIntent.id,
    stripe_charge_id: paymentIntent.charges.data[0]?.id,
    payment_method: 'card', // Default for Stripe
    status: 'succeeded',
    amount: paymentIntent.amount / 100,
    currency: paymentIntent.currency.toUpperCase(),
    description: paymentIntent.description,
    receipt_email: paymentIntent.receipt_email,
    processed_at: new Date(),
  });

  logger.info('Payment intent succeeded:', {
    paymentIntentId: paymentIntent.id,
    invoiceId: invoice.id,
    amount: paymentIntent.amount / 100,
  });
}

/**
 * Handle payment intent failed
 */
async function handlePaymentIntentFailed(paymentIntent) {
  // Find related invoice
  const invoice = await models.Invoice.findOne({
    where: { stripe_invoice_id: paymentIntent.invoice },
  });

  if (!invoice) {
    logger.error('Invoice not found for failed payment intent:', paymentIntent.invoice);
    return;
  }

  // Create payment record
  await models.Payment.create({
    tenant_id: invoice.tenant_id,
    invoice_id: invoice.id,
    stripe_payment_intent_id: paymentIntent.id,
    payment_method: 'card',
    status: 'failed',
    amount: paymentIntent.amount / 100,
    currency: paymentIntent.currency.toUpperCase(),
    description: paymentIntent.description,
    failure_reason: paymentIntent.last_payment_error?.message,
    failure_code: paymentIntent.last_payment_error?.code,
    failed_at: new Date(),
  });

  logger.info('Payment intent failed:', {
    paymentIntentId: paymentIntent.id,
    invoiceId: invoice.id,
    reason: paymentIntent.last_payment_error?.message,
  });
}

export default router;
