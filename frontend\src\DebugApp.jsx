import React, { useState, useEffect } from 'react';
import { useAuth } from './hooks/useAuth';
import { API_CONFIG } from './utils/constants';

const DebugApp = () => {
  const [debugInfo, setDebugInfo] = useState({});
  const { isAuthenticated, isLoading, user } = useAuth();

  useEffect(() => {
    console.log('DebugApp mounted');
    
    // Collect debug information
    const info = {
      timestamp: new Date().toISOString(),
      environment: import.meta.env.MODE,
      apiBaseUrl: API_CONFIG.BASE_URL,
      currentUrl: window.location.href,
      userAgent: navigator.userAgent,
      localStorage: {
        token: !!localStorage.getItem('tallycrm_token'),
        user: !!localStorage.getItem('tallycrm_user'),
      },
      auth: {
        isAuthenticated,
        isLoading,
        user: user ? { id: user.id, email: user.email, name: user.name } : null
      },
      envVars: {
        VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL,
        VITE_DEV_PORT: import.meta.env.VITE_DEV_PORT,
        VITE_ENABLE_MOCK_API: import.meta.env.VITE_ENABLE_MOCK_API,
      }
    };
    
    setDebugInfo(info);
    console.log('Debug Info:', info);
  }, [isAuthenticated, isLoading, user]);

  const testApiConnection = async () => {
    try {
      console.log('Testing API connection to:', API_CONFIG.BASE_URL);
      const response = await fetch(`${API_CONFIG.BASE_URL}/health`);
      const data = await response.json();
      console.log('API Health Check:', data);
      alert(`API Connection: ${response.ok ? 'SUCCESS' : 'FAILED'}\nStatus: ${response.status}\nData: ${JSON.stringify(data)}`);
    } catch (error) {
      console.error('API Test Failed:', error);
      alert(`API Connection FAILED: ${error.message}`);
    }
  };

  const clearStorage = () => {
    localStorage.clear();
    sessionStorage.clear();
    window.location.reload();
  };

  if (isLoading) {
    return (
      <div style={{ 
        minHeight: '100vh', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        fontFamily: 'Arial, sans-serif',
        backgroundColor: '#f3f4f6'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ 
            width: '50px', 
            height: '50px', 
            border: '4px solid #e5e7eb',
            borderTop: '4px solid #7c3aed',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 1rem'
          }}></div>
          <p>Loading authentication...</p>
        </div>
      </div>
    );
  }

  return (
    <div style={{ 
      minHeight: '100vh', 
      backgroundColor: '#f3f4f6', 
      padding: '2rem',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{ 
        maxWidth: '800px', 
        margin: '0 auto', 
        backgroundColor: 'white', 
        padding: '2rem', 
        borderRadius: '8px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
      }}>
        <h1 style={{ color: '#7c3aed', marginBottom: '2rem' }}>🔍 TallyCRM Debug Panel</h1>
        
        <div style={{ marginBottom: '2rem' }}>
          <h2>Authentication Status</h2>
          <div style={{ 
            padding: '1rem', 
            backgroundColor: isAuthenticated ? '#dcfce7' : '#fef2f2',
            border: `1px solid ${isAuthenticated ? '#16a34a' : '#dc2626'}`,
            borderRadius: '4px',
            marginBottom: '1rem'
          }}>
            <p><strong>Authenticated:</strong> {isAuthenticated ? '✅ Yes' : '❌ No'}</p>
            <p><strong>Loading:</strong> {isLoading ? '⏳ Yes' : '✅ No'}</p>
            <p><strong>User:</strong> {user ? `${user.name} (${user.email})` : 'None'}</p>
          </div>
        </div>

        <div style={{ marginBottom: '2rem' }}>
          <h2>Environment Information</h2>
          <pre style={{ 
            backgroundColor: '#f8f9fa', 
            padding: '1rem', 
            borderRadius: '4px',
            overflow: 'auto',
            fontSize: '0.875rem'
          }}>
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </div>

        <div style={{ marginBottom: '2rem' }}>
          <h2>Actions</h2>
          <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
            <button 
              onClick={testApiConnection}
              style={{ 
                backgroundColor: '#7c3aed', 
                color: 'white', 
                padding: '0.5rem 1rem', 
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Test API Connection
            </button>
            <button 
              onClick={clearStorage}
              style={{ 
                backgroundColor: '#dc2626', 
                color: 'white', 
                padding: '0.5rem 1rem', 
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Clear Storage & Reload
            </button>
            <button 
              onClick={() => window.location.href = '/auth/login'}
              style={{ 
                backgroundColor: '#16a34a', 
                color: 'white', 
                padding: '0.5rem 1rem', 
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Go to Login
            </button>
          </div>
        </div>

        <div>
          <h2>Console Logs</h2>
          <p>Check the browser console (F12) for detailed logs and any error messages.</p>
        </div>
      </div>

      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default DebugApp;
