import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const UsageRecord = sequelize.define('UsageRecord', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
    },
    subscription_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'subscriptions',
        key: 'id',
      },
    },
    metric_name: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'e.g., users, customers, service_calls, storage_gb, api_calls',
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    timestamp: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    period_start: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    period_end: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    action: {
      type: DataTypes.ENUM('increment', 'decrement', 'set'),
      defaultValue: 'set',
    },
    metadata: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'usage_records',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['subscription_id'],
      },
      {
        fields: ['metric_name'],
      },
      {
        fields: ['timestamp'],
      },
      {
        fields: ['period_start', 'period_end'],
      },
      {
        fields: ['tenant_id', 'metric_name', 'period_start'],
      },
    ],
  });

  // Static methods
  UsageRecord.recordUsage = async function(tenantId, subscriptionId, metricName, quantity, action = 'set') {
    const now = new Date();
    const periodStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const periodEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);

    return await this.create({
      tenant_id: tenantId,
      subscription_id: subscriptionId,
      metric_name: metricName,
      quantity,
      action,
      timestamp: now,
      period_start: periodStart,
      period_end: periodEnd,
    });
  };

  UsageRecord.getCurrentUsage = async function(tenantId, metricName, periodStart = null, periodEnd = null) {
    if (!periodStart) {
      const now = new Date();
      periodStart = new Date(now.getFullYear(), now.getMonth(), 1);
      periodEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
    }

    const latestRecord = await this.findOne({
      where: {
        tenant_id: tenantId,
        metric_name: metricName,
        period_start: periodStart,
        period_end: periodEnd,
      },
      order: [['timestamp', 'DESC']],
    });

    return latestRecord ? latestRecord.quantity : 0;
  };

  UsageRecord.getUsageHistory = async function(tenantId, metricName, months = 12) {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - months);

    return await this.findAll({
      where: {
        tenant_id: tenantId,
        metric_name: metricName,
        timestamp: {
          [sequelize.Sequelize.Op.between]: [startDate, endDate],
        },
      },
      order: [['timestamp', 'ASC']],
    });
  };

  UsageRecord.getUsageSummary = async function(tenantId, periodStart = null, periodEnd = null) {
    if (!periodStart) {
      const now = new Date();
      periodStart = new Date(now.getFullYear(), now.getMonth(), 1);
      periodEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
    }

    const records = await this.findAll({
      where: {
        tenant_id: tenantId,
        period_start: periodStart,
        period_end: periodEnd,
      },
      attributes: [
        'metric_name',
        [sequelize.fn('MAX', sequelize.col('quantity')), 'max_quantity'],
        [sequelize.fn('AVG', sequelize.col('quantity')), 'avg_quantity'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'record_count'],
      ],
      group: ['metric_name'],
      order: [['metric_name', 'ASC']],
    });

    const summary = {};
    records.forEach(record => {
      summary[record.metric_name] = {
        current: parseInt(record.getDataValue('max_quantity')),
        average: parseFloat(record.getDataValue('avg_quantity')),
        recordCount: parseInt(record.getDataValue('record_count')),
      };
    });

    return summary;
  };

  UsageRecord.checkLimits = async function(tenantId, limits) {
    const currentUsage = {};
    
    for (const metricName of Object.keys(limits)) {
      currentUsage[metricName] = await this.getCurrentUsage(tenantId, metricName);
    }

    const violations = {};
    for (const [metricName, limit] of Object.entries(limits)) {
      const current = currentUsage[metricName];
      if (current >= limit) {
        violations[metricName] = {
          current,
          limit,
          exceeded: current - limit,
        };
      }
    }

    return {
      currentUsage,
      violations,
      hasViolations: Object.keys(violations).length > 0,
    };
  };

  // Associations
  UsageRecord.associate = function(models) {
    UsageRecord.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });

    UsageRecord.belongsTo(models.Subscription, {
      foreignKey: 'subscription_id',
      as: 'subscription',
    });
  };

  return UsageRecord;
}
