import express from 'express';
import { body, query, param } from 'express-validator';
import { validateRequest as validate } from '../middleware/validation.js';
import { authenticateToken, requirePermission } from '../middleware/auth.js';
import {
  licenseEdition<PERSON><PERSON>roller,
  designation<PERSON><PERSON>roller,
  tallyProductController,
  staffRoleController,
  industryController,
  areaController,
  natureOfIssueController,
  additionalServiceController,
  callStatusController,
} from '../controllers/masterDataController.js';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Common validation rules
const commonQueryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('search')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters'),
  query('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  query('sortBy')
    .optional()
    .isIn(['name', 'code', 'sort_order', 'created_at'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['ASC', 'DESC'])
    .withMessage('Sort order must be ASC or DESC'),
];

const commonBodyValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('code')
    .trim()
    .isLength({ min: 2, max: 20 })
    .withMessage('Code must be between 2 and 20 characters')
    .matches(/^[A-Z0-9_]+$/)
    .withMessage('Code must contain only uppercase letters, numbers, and underscores'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters'),
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  body('sort_order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Sort order must be a non-negative integer'),
];

const idValidation = [
  param('id')
    .isUUID()
    .withMessage('ID must be a valid UUID'),
];

// Helper function to create CRUD routes for a master data entity
const createMasterDataRoutes = (path, controller, permission, additionalValidation = []) => {
  // GET /api/v1/master-data/{entity}
  router.get(`/${path}`, [
    requirePermission(`${permission}.read`),
    ...commonQueryValidation,
    validate,
  ], controller.getAll);

  // GET /api/v1/master-data/{entity}/:id
  router.get(`/${path}/:id`, [
    requirePermission(`${permission}.read`),
    ...idValidation,
    validate,
  ], controller.getById);

  // POST /api/v1/master-data/{entity}
  router.post(`/${path}`, [
    requirePermission(`${permission}.create`),
    ...commonBodyValidation,
    ...additionalValidation,
    validate,
  ], controller.create);

  // PUT /api/v1/master-data/{entity}/:id
  router.put(`/${path}/:id`, [
    requirePermission(`${permission}.update`),
    ...idValidation,
    ...commonBodyValidation.map(rule => rule.optional()),
    ...additionalValidation.map(rule => rule.optional()),
    validate,
  ], controller.update);

  // DELETE /api/v1/master-data/{entity}/:id
  router.delete(`/${path}/:id`, [
    requirePermission(`${permission}.delete`),
    ...idValidation,
    validate,
  ], controller.delete);

  // PATCH /api/v1/master-data/{entity}/:id/toggle-active
  router.patch(`/${path}/:id/toggle-active`, [
    requirePermission(`${permission}.update`),
    ...idValidation,
    validate,
  ], controller.toggleActive);
};

// License Editions
createMasterDataRoutes('license-editions', licenseEditionController, 'master_data', [
  body('version')
    .optional()
    .trim()
    .isLength({ min: 1, max: 20 })
    .withMessage('Version must be between 1 and 20 characters'),
  body('price')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Price must be a valid decimal number'),
  body('annual_maintenance_charge')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Annual maintenance charge must be a valid decimal number'),
  body('max_companies')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Max companies must be a positive integer'),
  body('max_users')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Max users must be a positive integer'),
]);

// Designations
createMasterDataRoutes('designations', designationController, 'master_data', [
  body('level')
    .optional()
    .isInt({ min: 1, max: 10 })
    .withMessage('Level must be between 1 and 10'),
  body('department')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Department must be between 2 and 50 characters'),
]);

// Tally Products
createMasterDataRoutes('tally-products', tallyProductController, 'master_data', [
  body('category')
    .optional()
    .isIn(['software', 'hardware', 'service', 'addon', 'training'])
    .withMessage('Invalid category'),
  body('version')
    .optional()
    .trim()
    .isLength({ min: 1, max: 20 })
    .withMessage('Version must be between 1 and 20 characters'),
  body('price')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Price must be a valid decimal number'),
  body('cost_price')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Cost price must be a valid decimal number'),
  body('hsn_code')
    .optional()
    .trim()
    .isLength({ min: 4, max: 8 })
    .withMessage('HSN code must be between 4 and 8 characters'),
  body('gst_rate')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('GST rate must be a valid decimal number'),
  body('unit')
    .optional()
    .trim()
    .isLength({ min: 1, max: 10 })
    .withMessage('Unit must be between 1 and 10 characters'),
  body('is_service')
    .optional()
    .isBoolean()
    .withMessage('isService must be a boolean'),
]);

// Staff Roles
createMasterDataRoutes('staff-roles', staffRoleController, 'master_data', [
  body('department')
    .optional()
    .isIn(['sales', 'technical', 'support', 'management', 'accounts', 'hr', 'marketing'])
    .withMessage('Invalid department'),
  body('level')
    .optional()
    .isInt({ min: 1, max: 10 })
    .withMessage('Level must be between 1 and 10'),
]);

// Industries
createMasterDataRoutes('industries', industryController, 'master_data', [
  body('category')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Category must be between 2 and 50 characters'),
]);

// Areas
createMasterDataRoutes('areas', areaController, 'master_data', [
  body('city')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('City must be between 2 and 50 characters'),
  body('state')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('State must be between 2 and 50 characters'),
  body('country')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Country must be between 2 and 50 characters'),
]);

// Nature of Issues
createMasterDataRoutes('nature-of-issues', natureOfIssueController, 'master_data', [
  body('category')
    .optional()
    .isIn(['technical', 'functional', 'data', 'installation', 'training', 'other'])
    .withMessage('Invalid category'),
  body('severity')
    .optional()
    .isIn(['low', 'medium', 'high', 'critical'])
    .withMessage('Invalid severity'),
  body('estimated_resolution_time')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Estimated resolution time must be a positive integer'),
  body('requires_onsite')
    .optional()
    .isBoolean()
    .withMessage('requiresOnsite must be a boolean'),
]);

// Additional Services
createMasterDataRoutes('additional-services', additionalServiceController, 'master_data', [
  body('category')
    .optional()
    .isIn(['training', 'customization', 'integration', 'support', 'consulting', 'other'])
    .withMessage('Invalid category'),
  body('service_type')
    .optional()
    .isIn(['onetime', 'recurring', 'hourly', 'project'])
    .withMessage('Invalid service type'),
  body('price')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Price must be a valid decimal number'),
  body('cost_price')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Cost price must be a valid decimal number'),
  body('duration_hours')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Duration hours must be a positive integer'),
  body('gst_rate')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('GST rate must be a valid decimal number'),
  body('requires_approval')
    .optional()
    .isBoolean()
    .withMessage('requiresApproval must be a boolean'),
  body('is_billable')
    .optional()
    .isBoolean()
    .withMessage('isBillable must be a boolean'),
]);

// Call Statuses
createMasterDataRoutes('call-statuses', callStatusController, 'master_data', [
  body('category')
    .optional()
    .isIn(['open', 'in_progress', 'resolved', 'closed', 'cancelled'])
    .withMessage('Invalid category'),
  body('color')
    .optional()
    .matches(/^#[0-9A-Fa-f]{6}$/)
    .withMessage('Color must be a valid hex color code'),
  body('is_final')
    .optional()
    .isBoolean()
    .withMessage('isFinal must be a boolean'),
  body('requires_approval')
    .optional()
    .isBoolean()
    .withMessage('requiresApproval must be a boolean'),
  body('auto_close_after_days')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Auto close after days must be a positive integer'),
  body('is_billable')
    .optional()
    .isBoolean()
    .withMessage('isBillable must be a boolean'),
]);

export default router;
