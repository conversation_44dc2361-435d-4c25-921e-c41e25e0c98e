import express from 'express';
import { body, query, param } from 'express-validator';
import { validateRequest as validate } from '../middleware/validation.js';
import { authenticateToken, requirePermission, requireTenantAccess } from '../middleware/auth.js';
import {
  getExecutives,
  getExecutiveById,
  createExecutive,
  updateExecutive,
  deleteExecutive,
  getExecutiveStats,
} from '../controllers/executiveController.js';

const router = express.Router();

// Apply authentication and tenant access to all routes
router.use(authenticateToken);
router.use(requireTenantAccess);

/**
 * @route   GET /api/executives
 * @desc    Get all executives with pagination and filters
 * @access  Private (requires executives.read permission)
 */
router.get('/', [
  requirePermission('executives.read'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('search')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters'),
  query('department')
    .optional()
    .isIn(['sales', 'technical', 'support', 'management', 'accounts', 'hr', 'marketing'])
    .withMessage('Invalid department'),
  query('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  query('sortBy')
    .optional()
    .isIn(['created_at', 'updated_at', 'first_name', 'last_name', 'employee_code'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['ASC', 'DESC'])
    .withMessage('Sort order must be ASC or DESC'),
  validate,
], getExecutives);

/**
 * @route   GET /api/executives/stats
 * @desc    Get executive statistics
 * @access  Private (requires executives.read permission)
 */
router.get('/stats', [
  requirePermission('executives.read'),
], getExecutiveStats);

/**
 * @route   GET /api/executives/:id
 * @desc    Get executive by ID
 * @access  Private (requires executives.read permission)
 */
router.get('/:id', [
  requirePermission('executives.read'),
  param('id')
    .isUUID()
    .withMessage('Executive ID must be a valid UUID'),
  validate,
], getExecutiveById);

/**
 * @route   POST /api/executives
 * @desc    Create new executive
 * @access  Private (requires executives.create permission)
 */
router.post('/', [
  requirePermission('executives.create'),
  body('first_name')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters'),
  body('last_name')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters'),
  body('employee_code')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ min: 2, max: 20 })
    .withMessage('Employee code must be between 2 and 20 characters'),
  body('email')
    .notEmpty()
    .isEmail()
    .normalizeEmail()
    .withMessage('Email is required and must be valid'),
  body('phone')
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),
  body('alternate_phone')
    .optional({ nullable: true, checkFalsy: true })
    .isMobilePhone()
    .withMessage('Please provide a valid alternate phone number'),
  body('designation_id')
    .notEmpty()
    .isUUID()
    .withMessage('Designation is required and must be a valid UUID'),
  body('staff_role_id')
    .optional()
    .isUUID()
    .withMessage('Staff role ID must be a valid UUID'),
  body('department')
    .isIn(['sales', 'technical', 'support', 'management', 'accounts', 'hr', 'marketing'])
    .withMessage('Invalid department'),
  body('date_of_joining')
    .optional({ nullable: true, checkFalsy: true })
    .isISO8601()
    .withMessage('Date of joining must be a valid date'),
  body('date_of_birth')
    .optional({ nullable: true, checkFalsy: true })
    .isISO8601()
    .withMessage('Date of birth must be a valid date'),
  body('address')
    .notEmpty()
    .trim()
    .isLength({ min: 1, max: 500 })
    .withMessage('Address is required and must be between 1 and 500 characters'),
  body('city')
    .notEmpty()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('City is required and must be between 1 and 100 characters'),
  body('state')
    .notEmpty()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('State is required and must be between 1 and 100 characters'),
  body('postal_code')
    .notEmpty()
    .trim()
    .isLength({ min: 1, max: 20 })
    .withMessage('Postal code is required and must be between 1 and 20 characters'),
  body('salary')
    .notEmpty()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Salary is required and must be a valid decimal number'),
  body('commission_rate')
    .notEmpty()
    .isDecimal({ decimal_digits: '0,2' })
    .custom((value) => {
      const num = parseFloat(value);
      if (num < 0 || num > 100) {
        throw new Error('Commission rate must be between 0 and 100');
      }
      return true;
    })
    .withMessage('Commission rate is required and must be a valid percentage (0-100)'),
  body('target_amount')
    .notEmpty()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Target amount is required and must be a valid decimal number'),
  body('skills')
    .optional()
    .isArray()
    .withMessage('Skills must be an array'),
  body('areas_covered')
    .optional()
    .isArray()
    .withMessage('Areas covered must be an array'),
  validate,
], createExecutive);

/**
 * @route   PUT /api/executives/:id
 * @desc    Update executive
 * @access  Private (requires executives.update permission)
 */
router.put('/:id', [
  requirePermission('executives.update'),
  param('id')
    .isUUID()
    .withMessage('Executive ID must be a valid UUID'),
  body('first_name')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters'),
  body('last_name')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters'),
  body('employee_code')
    .optional()
    .trim()
    .isLength({ min: 2, max: 20 })
    .withMessage('Employee code must be between 2 and 20 characters'),
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('phone')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),
  body('alternate_phone')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid alternate phone number'),
  body('designation_id')
    .optional()
    .isUUID()
    .withMessage('Designation ID must be a valid UUID'),
  body('staff_role_id')
    .optional()
    .isUUID()
    .withMessage('Staff role ID must be a valid UUID'),
  body('department')
    .optional()
    .isIn(['sales', 'technical', 'support', 'management', 'accounts', 'hr', 'marketing'])
    .withMessage('Invalid department'),
  body('date_of_joining')
    .optional()
    .isISO8601()
    .withMessage('Date of joining must be a valid date'),
  body('date_of_birth')
    .optional()
    .isISO8601()
    .withMessage('Date of birth must be a valid date'),
  body('salary')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Salary must be a valid decimal number'),
  body('commission_rate')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Commission rate must be a valid decimal number'),
  body('target_amount')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Target amount must be a valid decimal number'),
  body('skills')
    .optional()
    .isArray()
    .withMessage('Skills must be an array'),
  body('areas_covered')
    .optional()
    .isArray()
    .withMessage('Areas covered must be an array'),
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  validate,
], updateExecutive);

/**
 * @route   DELETE /api/executives/:id
 * @desc    Delete executive
 * @access  Private (requires executives.delete permission)
 */
router.delete('/:id', [
  requirePermission('executives.delete'),
  param('id')
    .isUUID()
    .withMessage('Executive ID must be a valid UUID'),
  validate,
], deleteExecutive);

export default router;
