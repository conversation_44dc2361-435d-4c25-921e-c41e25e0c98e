# 💳 Stripe Testing Guide for TallyCRM SaaS

This guide will help you test all Stripe integration features in your TallyCRM SaaS application.

## 🔧 **Setup Stripe Test Environment**

### 1. **Stripe Dashboard Setup**

1. **Create Stripe Account:**
   - Go to [Stripe Dashboard](https://dashboard.stripe.com)
   - Create account or sign in
   - Switch to **Test Mode** (toggle in top-right)

2. **Get Test API Keys:**
   - Navigate to **Developers > API keys**
   - Copy **Publishable key** (starts with `pk_test_`)
   - Copy **Secret key** (starts with `sk_test_`)

3. **Create Products and Prices:**
   ```bash
   # You can create these via Stripe CLI or Dashboard
   # The application will create local subscription plans via seeder
   ```

### 2. **Environment Configuration**

Update your `.env` file:

```env
# Stripe Test Configuration
STRIPE_SECRET_KEY=sk_test_your_test_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_test_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_test_webhook_secret
```

Update frontend `.env`:

```env
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_test_your_test_publishable_key
```

## 🔄 **Webhook Setup for Testing**

### 1. **Install Stripe CLI**

```bash
# macOS
brew install stripe/stripe-cli/stripe

# Linux
wget https://github.com/stripe/stripe-cli/releases/latest/download/stripe_1.19.1_linux_x86_64.tar.gz
tar -xvf stripe_1.19.1_linux_x86_64.tar.gz
sudo mv stripe /usr/local/bin

# Windows
# Download from https://github.com/stripe/stripe-cli/releases
```

### 2. **Login and Setup Webhooks**

```bash
# Login to Stripe
stripe login

# Forward webhooks to local development
stripe listen --forward-to localhost:3001/webhooks/stripe

# This will output a webhook secret like: whsec_1234...
# Copy this to your .env file as STRIPE_WEBHOOK_SECRET
```

### 3. **Test Webhook Events**

```bash
# Trigger test events
stripe trigger checkout.session.completed
stripe trigger customer.subscription.created
stripe trigger invoice.payment_succeeded
stripe trigger payment_intent.succeeded
```

## 🧪 **Test Card Numbers**

### **Successful Payments**
```
****************  # Visa
****************  # Visa (debit)
****************  # Mastercard
2223003122003222  # Mastercard (2-series)
****************  # Visa (3D Secure)
```

### **Failed Payments**
```
****************  # Generic decline
****************  # Insufficient funds
****************  # Lost card
****************  # Stolen card
****************  # Expired card
****************  # Incorrect CVC
****************  # Processing error
```

### **Special Test Cases**
```
****************  # Requires authentication (3D Secure)
****************  # Requires authentication (3D Secure 2)
****************  # Requires authentication (3D Secure - frictionless)
****************  # Requires authentication (3D Secure - challenge)
```

### **Test Card Details**
- **Expiry:** Any future date (e.g., 12/34)
- **CVC:** Any 3-digit number (e.g., 123)
- **ZIP:** Any valid postal code (e.g., 12345)

## 🔄 **Testing Subscription Flows**

### 1. **Test Plan Selection and Checkout**

```bash
# Start your application
cd backend && npm run dev
cd frontend && npm start

# Navigate to: http://localhost:3000/billing/plans
```

**Test Steps:**
1. Register a new tenant account
2. Navigate to billing plans page
3. Select a subscription plan
4. Click "Upgrade Now"
5. Complete Stripe Checkout with test card
6. Verify redirect to success page
7. Check subscription status in billing dashboard

### 2. **Test Webhook Processing**

**Monitor webhook events:**
```bash
# In terminal with Stripe CLI running
stripe listen --forward-to localhost:3001/webhooks/stripe

# Watch for these events during checkout:
# ✅ checkout.session.completed
# ✅ customer.subscription.created
# ✅ invoice.created
# ✅ invoice.payment_succeeded
# ✅ payment_intent.succeeded
```

**Verify in application:**
1. Check subscription status updated in database
2. Verify invoice created
3. Confirm payment recorded
4. Check usage limits applied

### 3. **Test Subscription Management**

**Cancel Subscription:**
1. Go to billing dashboard
2. Click "Cancel Subscription"
3. Verify webhook: `customer.subscription.updated`
4. Check subscription marked for cancellation

**Reactivate Subscription:**
1. Click "Reactivate Subscription"
2. Verify webhook: `customer.subscription.updated`
3. Check subscription reactivated

## 📊 **Testing Usage Tracking**

### 1. **Test Usage Limits**

```bash
# Create customers up to plan limit
# Try to create one more - should get 429 error

curl -X POST http://localhost:3001/api/v1/customers \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"company_name": "Test Company"}'

# Expected response when limit exceeded:
{
  "success": false,
  "message": "Usage limit exceeded for customers",
  "error": "USAGE_LIMIT_EXCEEDED",
  "data": {
    "current": 51,
    "limit": 50,
    "exceeded": 1,
    "upgradeRequired": true
  }
}
```

### 2. **Test Usage Dashboard**

1. Navigate to `/billing/dashboard`
2. Verify usage bars show correct percentages
3. Create resources and refresh to see updates
4. Test limit warnings when approaching limits

## 🔄 **Testing Payment Scenarios**

### 1. **Successful Payment Flow**

```javascript
// Test with successful card: ****************
// Expected flow:
// 1. checkout.session.completed
// 2. customer.subscription.created
// 3. invoice.created
// 4. invoice.payment_succeeded
// 5. payment_intent.succeeded
```

### 2. **Failed Payment Flow**

```javascript
// Test with declined card: ****************
// Expected flow:
// 1. checkout.session.completed (may not fire)
// 2. payment_intent.payment_failed
// 3. invoice.payment_failed
```

### 3. **3D Secure Authentication**

```javascript
// Test with 3D Secure card: ****************
// Expected flow:
// 1. Additional authentication step
// 2. User completes authentication
// 3. Normal success flow continues
```

## 🧪 **Advanced Testing Scenarios**

### 1. **Test Subscription Updates**

```bash
# Simulate plan upgrade via Stripe Dashboard
# 1. Go to Stripe Dashboard > Customers
# 2. Find test customer
# 3. Update subscription to different price
# 4. Verify webhook: customer.subscription.updated
```

### 2. **Test Invoice Payment Retry**

```bash
# Simulate failed payment retry
stripe trigger invoice.payment_failed
stripe trigger invoice.payment_succeeded
```

### 3. **Test Subscription Cancellation**

```bash
# Test immediate cancellation
stripe trigger customer.subscription.deleted

# Test end-of-period cancellation
# Update subscription in Stripe Dashboard:
# Set "Cancel at period end" = true
```

## 📝 **Testing Checklist**

### **Basic Functionality**
- [ ] Subscription plan display
- [ ] Checkout session creation
- [ ] Payment processing
- [ ] Webhook event handling
- [ ] Subscription status updates
- [ ] Invoice generation
- [ ] Payment recording

### **Error Handling**
- [ ] Declined card handling
- [ ] Network error handling
- [ ] Webhook failure recovery
- [ ] Invalid plan selection
- [ ] Expired session handling

### **Usage Tracking**
- [ ] Usage limit enforcement
- [ ] Usage dashboard accuracy
- [ ] Limit warning display
- [ ] Upgrade prompts
- [ ] Usage reset on plan change

### **Security**
- [ ] Webhook signature verification
- [ ] API key security
- [ ] Tenant data isolation
- [ ] Payment data protection

## 🔍 **Debugging Tools**

### 1. **Stripe Dashboard**
- **Logs:** View all API requests and responses
- **Events:** Monitor webhook events
- **Customers:** View customer and subscription details

### 2. **Application Logs**
```bash
# Backend logs
tail -f backend/logs/app.log

# PM2 logs (if using PM2)
pm2 logs tallycrm-backend
```

### 3. **Database Queries**
```sql
-- Check subscription status
SELECT * FROM subscriptions WHERE tenant_id = 'your-tenant-id';

-- Check usage records
SELECT * FROM usage_records WHERE tenant_id = 'your-tenant-id' ORDER BY created_at DESC;

-- Check invoices
SELECT * FROM invoices WHERE tenant_id = 'your-tenant-id' ORDER BY created_at DESC;

-- Check payments
SELECT * FROM payments WHERE tenant_id = 'your-tenant-id' ORDER BY created_at DESC;
```

## 🚨 **Common Issues and Solutions**

### 1. **Webhook Not Receiving Events**
```bash
# Check webhook endpoint is accessible
curl -X POST http://localhost:3001/webhooks/stripe

# Verify webhook secret in .env
echo $STRIPE_WEBHOOK_SECRET

# Check Stripe CLI is forwarding
stripe listen --forward-to localhost:3001/webhooks/stripe
```

### 2. **Payment Not Processing**
- Verify API keys are correct
- Check test mode is enabled
- Ensure webhook signature verification
- Check network connectivity

### 3. **Usage Limits Not Working**
- Verify middleware is applied to routes
- Check tenant context in requests
- Ensure usage tracking service is running
- Verify database connections

### 4. **Subscription Status Not Updating**
- Check webhook events are being received
- Verify webhook handler logic
- Check database transaction handling
- Ensure proper error handling

## 📊 **Test Data Cleanup**

```bash
# Clean test data from Stripe Dashboard
# 1. Go to Stripe Dashboard
# 2. Navigate to Customers, Subscriptions, Invoices
# 3. Delete test records

# Clean local database
npm run db:reset  # This will reset and reseed database
```

## ✅ **Production Testing Checklist**

Before going live:
- [ ] Test with live Stripe keys in staging
- [ ] Verify webhook endpoints are accessible
- [ ] Test real payment methods
- [ ] Verify SSL certificate
- [ ] Test from different networks
- [ ] Verify email notifications
- [ ] Test subscription lifecycle
- [ ] Verify usage tracking accuracy
- [ ] Test error scenarios
- [ ] Verify security measures

Your Stripe integration is now thoroughly tested and ready for production! 🎉
