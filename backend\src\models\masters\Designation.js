import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const Designation = sequelize.define('Designation', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 100],
      },
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        len: [2, 20],
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    level: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
      comment: 'Hierarchy level (1=highest)',
    },
    department: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Department this designation belongs to',
    },
    is_mandatory: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'If true, at least one contact with this designation must be entered in Customer Address Book',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'designations',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['code'],
        unique: true,
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['level'],
      },
      {
        fields: ['department'],
      },
      {
        fields: ['sort_order'],
      },
    ],
  });

  // Class methods
  Designation.getDefaultDesignations = function() {
    return [
      {
        name: 'Managing Director',
        code: 'MD',
        description: 'Chief Executive Officer',
        level: 1,
        department: 'Management',
        sort_order: 1,
      },
      {
        name: 'General Manager',
        code: 'GM',
        description: 'General Manager',
        level: 2,
        department: 'Management',
        sort_order: 2,
      },
      {
        name: 'Sales Manager',
        code: 'SM',
        description: 'Sales Manager',
        level: 3,
        department: 'Sales',
        sort_order: 3,
      },
      {
        name: 'Technical Manager',
        code: 'TM',
        description: 'Technical Manager',
        level: 3,
        department: 'Technical',
        sort_order: 4,
      },
      {
        name: 'Sales Executive',
        code: 'SE',
        description: 'Sales Executive',
        level: 4,
        department: 'Sales',
        sort_order: 5,
      },
      {
        name: 'Technical Executive',
        code: 'TE',
        description: 'Technical Executive',
        level: 4,
        department: 'Technical',
        sort_order: 6,
      },
      {
        name: 'Customer Support Executive',
        code: 'CSE',
        description: 'Customer Support Executive',
        level: 4,
        department: 'Support',
        sort_order: 7,
      },
      {
        name: 'Accountant',
        code: 'ACC',
        description: 'Accountant',
        level: 4,
        department: 'Accounts',
        sort_order: 8,
      },
      {
        name: 'Office Assistant',
        code: 'OA',
        description: 'Office Assistant',
        level: 5,
        department: 'Administration',
        sort_order: 9,
      },
    ];
  };

  // Associations
  Designation.associate = function(models) {
    Designation.hasMany(models.CustomerContact, {
      foreignKey: 'designation_id',
      as: 'customerContacts',
    });

    Designation.hasMany(models.Executive, {
      foreignKey: 'designation_id',
      as: 'executives',
    });
  };

  return Designation;
}
