import React from 'react';
import { cn } from '../../utils/helpers';

const ResponsiveTable = ({
  columns,
  data,
  onRowClick,
  className,
  mobileCardRender,
  loading = false,
  emptyMessage = "No data available"
}) => {
  if (loading) {
    return (
      <div className="bg-white shadow-xl rounded-2xl overflow-hidden border border-purple-100">
        <div className="p-8 text-center">
          <div className="relative">
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-purple-200 mx-auto"></div>
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-purple-600 border-t-transparent absolute top-0 left-1/2 transform -translate-x-1/2"></div>
          </div>
          <p className="mt-4 text-purple-600 font-medium">Loading data...</p>
        </div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="bg-white shadow-xl rounded-2xl overflow-hidden border border-purple-100">
        <div className="p-8 text-center">
          <div className="mx-auto w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-4">
            <i className="bi bi-inbox text-purple-600 text-2xl"></i>
          </div>
          <h6 className="text-gray-900 font-medium mb-1">No Data Found</h6>
          <p className="text-gray-500 text-sm">{emptyMessage}</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Desktop Table View */}
      <div className="hidden lg:block bg-white shadow-xl rounded-2xl overflow-hidden border border-purple-100">
        <div className="overflow-x-auto">
          <table className={cn("min-w-full divide-y divide-purple-200", className)}>
            <thead className="bg-gradient-to-r from-purple-600 to-indigo-600">
              <tr>
                {columns.map((column, index) => (
                  <th
                    key={index}
                    className={cn(
                      "px-4 py-4 text-left text-xs font-bold text-white uppercase tracking-wider",
                      column.className
                    )}
                  >
                    {column.header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-purple-100">
              {data.map((row, rowIndex) => (
                <tr
                  key={rowIndex}
                  className={cn(
                    "hover:bg-gradient-to-r hover:from-purple-50 hover:to-blue-50 transition-all duration-200",
                    onRowClick && "cursor-pointer",
                    rowIndex % 2 === 0 ? 'bg-white' : 'bg-purple-25'
                  )}
                  onClick={() => onRowClick && onRowClick(row)}
                >
                  {columns.map((column, colIndex) => (
                    <td
                      key={colIndex}
                      className={cn(
                        "px-4 py-4 text-sm",
                        column.cellClassName
                      )}
                    >
                      {column.render ? column.render(row, rowIndex) : row[column.key]}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Mobile Card View */}
      <div className="lg:hidden space-y-4">
        {data.map((row, index) => (
          <div
            key={index}
            className={cn(
              "bg-white shadow-xl rounded-2xl p-5 border border-purple-100 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1",
              onRowClick && "cursor-pointer active:scale-95"
            )}
            onClick={() => onRowClick && onRowClick(row)}
          >
            {mobileCardRender ? mobileCardRender(row, index) : (
              <div className="space-y-4">
                {columns.slice(0, 4).map((column, colIndex) => (
                  <div key={colIndex} className="flex justify-between items-start">
                    <span className="text-sm font-bold text-purple-700 uppercase tracking-wide">
                      {column.header}
                    </span>
                    <span className="text-sm text-gray-900 text-right ml-3 font-medium">
                      {column.render ? column.render(row, index) : row[column.key]}
                    </span>
                  </div>
                ))}
                {columns.length > 4 && (
                  <div className="pt-3 border-t border-purple-100">
                    <button className="text-purple-600 text-sm font-bold hover:text-purple-800 transition-colors">
                      View Details →
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>
    </>
  );
};

export default ResponsiveTable;
