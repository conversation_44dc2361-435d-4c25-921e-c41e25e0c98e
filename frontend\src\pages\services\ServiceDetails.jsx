import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import apiService from '../../services/api';
import { 
  FaEdit, 
  FaTrash, 
  FaUser, 
  FaCalendar,
  FaClock,
  FaRupeeSign,
  FaTools,
  FaMapMarkerAlt,
  FaPhone,
  FaEnvelope,
  FaCheckCircle,
  FaTimesCircle,
  FaPlayCircle,
  FaPauseCircle
} from 'react-icons/fa';

const ServiceDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [service, setService] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('details');

  // Fetch service data from API
  useEffect(() => {
    fetchServiceDetails();
  }, [id]);

  const fetchServiceDetails = async () => {
    try {
      setLoading(true);
      const response = await apiService.get(`/service-calls/${id}`);

      if (response.data?.success) {
        const serviceData = response.data.data.serviceCall;
        setService({
          id: serviceData.id,
          serviceNumber: serviceData.service_number || `SRV-${serviceData.id}`,
          customer: serviceData.customer_name || 'Unknown Customer',
          customerId: serviceData.customer_id,
          contactPerson: serviceData.contact_person || '',
          contactPhone: serviceData.contact_phone || '',
          contactEmail: serviceData.contact_email || '',
          type: serviceData.service_type || 'General',
          category: serviceData.category || 'Support',
          description: serviceData.description || '',
          priority: serviceData.priority || 'medium',
          status: serviceData.status || 'pending',
          assignedTo: serviceData.assigned_to || 'Unassigned',
          technicianId: serviceData.technician_id,
          technicianPhone: serviceData.technician_phone || '',
          technicianEmail: serviceData.technician_email || '',

          // Scheduling
          createdDate: serviceData.created_at ? serviceData.created_at.split('T')[0] : '',
          scheduledDate: serviceData.scheduled_date ? serviceData.scheduled_date.split('T')[0] : '',
          scheduledTime: serviceData.scheduled_time || '',
          startedDate: serviceData.started_at ? serviceData.started_at.split('T')[0] : null,
          completedDate: serviceData.completed_at ? serviceData.completed_at.split('T')[0] : null,
          estimatedHours: serviceData.estimated_hours || 0,
          actualHours: serviceData.actual_hours || 0,

          // Location
          serviceLocation: serviceData.service_location || 'customer-site',
          address: serviceData.address || '',
          city: serviceData.city || '',
          state: serviceData.state || '',

          // Financial
          amount: serviceData.amount || 0,
          currency: serviceData.currency || 'INR',
          paymentStatus: serviceData.payment_status || 'pending',

          // Additional
          requirements: serviceData.requirements || '',
          notes: serviceData.notes || '',

          // Progress tracking - will be fetched separately if needed
          progress: serviceData.progress || [],

          // Files/attachments - will be fetched separately if needed
          attachments: serviceData.attachments || []
        });
      } else {
        toast.error('Service call not found');
        navigate('/services');
      }
    } catch (error) {
      console.error('Error fetching service details:', error);
      toast.error('Failed to load service details');
      navigate('/services');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this service request? This action cannot be undone.')) {
      toast.success('Service request deleted successfully');
      navigate('/services');
    }
  };

  const handleStatusUpdate = (newStatus) => {
    setService(prev => ({ ...prev, status: newStatus }));
    toast.success(`Service status updated to ${newStatus}`);
  };

  const getStatusBadge = (status) => {
    const badgeClass = status === 'completed' ? 'bg-success' : 
                      status === 'in-progress' ? 'bg-info' : 
                      status === 'scheduled' ? 'bg-warning' : 
                      status === 'pending' ? 'bg-secondary' : 'bg-danger';
    return <span className={`badge ${badgeClass}`}>{status.toUpperCase().replace('-', ' ')}</span>;
  };

  const getPriorityBadge = (priority) => {
    const badgeClass = priority === 'high' ? 'bg-danger' : 
                      priority === 'medium' ? 'bg-warning' : 'bg-success';
    return <span className={`badge ${badgeClass}`}>{priority.toUpperCase()}</span>;
  };

  const getPaymentStatusBadge = (status) => {
    const badgeClass = status === 'paid' ? 'bg-success' : 
                      status === 'partial' ? 'bg-info' :
                      status === 'overdue' ? 'bg-danger' : 'bg-warning';
    return <span className={`badge ${badgeClass}`}>{status.toUpperCase()}</span>;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center" style={{ height: '400px' }}>
        <div className="animate-spin rounded-full border-2 border-gray-300 border-t-current text-primary-600" role="status">
          <span className="sr-only">Loading...</span>
        </div>
      </div>
    );
  }

  if (!service) {
    return (
      <div className="w-full px-4">
        <div className="p-4 rounded-md border bg-red-50 border-red-200 text-red-800" role="alert">
          Service request not found.
        </div>
      </div>
    );
  }

  return (
    <div className="w-full px-4">
      {/* Header */}
      <div className="grid grid-cols-12 gap-4 mb-6">
        <div className="col-span-12">
          <div className="flex justify-between items-start">
            <div>
              <h2 className="mb-1">{service.serviceNumber}</h2>
              <p className="text-gray-600 mb-2">{service.customer} • {service.type}</p>
              <div className="flex items-center gap-3">
                {getStatusBadge(service.status)}
                {getPriorityBadge(service.priority)}
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyan-600">{service.category}</span>
                <small className="text-gray-600">
                  <FaCalendar className="mr-1" />
                  Created {new Date(service.createdDate).toLocaleDateString()}
                </small>
              </div>
            </div>
            <div className="flex gap-2">
              {/* Status Action Buttons */}
              {service.status === 'pending' && (
                <button 
                  className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 bg-green-600 text-white border-green-600 hover:bg-green-700 focus:ring-green-500 px-3 py-1 text-sm"
                  onClick={() => handleStatusUpdate('in-progress')}
                >
                  <FaPlayCircle className="mr-1" />
                  Start Service
                </button>
              )}
              {service.status === 'in-progress' && (
                <>
                  <button 
                    className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 bg-yellow-600 text-white border-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500 px-3 py-1 text-sm"
                    onClick={() => handleStatusUpdate('scheduled')}
                  >
                    <FaPauseCircle className="mr-1" />
                    Pause
                  </button>
                  <button 
                    className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 bg-green-600 text-white border-green-600 hover:bg-green-700 focus:ring-green-500 px-3 py-1 text-sm"
                    onClick={() => handleStatusUpdate('completed')}
                  >
                    <FaCheckCircle className="mr-1" />
                    Complete
                  </button>
                </>
              )}
              
              <Link to={`/services/${service.id}/edit`} className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500 px-3 py-1 text-sm">
                <FaEdit className="mr-1" />
                Edit
              </Link>
              <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 border border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500 px-3 py-1 text-sm" onClick={handleDelete}>
                <FaTrash className="mr-1" />
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-12 gap-4 mb-6">
        <div className="md:col-span-3">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 bg-primary-600 text-white">
            <div className="p-4">
              <div className="flex justify-between">
                <div>
                  <h4 className="mb-0">{service.actualHours}h</h4>
                  <p className="mb-0">Hours Spent</p>
                </div>
                <FaClock size={24} />
              </div>
            </div>
          </div>
        </div>
        <div className="md:col-span-3">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 bg-cyan-600 text-white">
            <div className="p-4">
              <div className="flex justify-between">
                <div>
                  <h4 className="mb-0">{service.estimatedHours}h</h4>
                  <p className="mb-0">Estimated</p>
                </div>
                <FaCalendar size={24} />
              </div>
            </div>
          </div>
        </div>
        <div className="md:col-span-3">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 bg-green-600 text-white">
            <div className="p-4">
              <div className="flex justify-between">
                <div>
                  <h4 className="mb-0">₹{service.amount.toLocaleString()}</h4>
                  <p className="mb-0">Service Amount</p>
                </div>
                <FaRupeeSign size={24} />
              </div>
            </div>
          </div>
        </div>
        <div className="md:col-span-3">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 bg-yellow-600 text-white">
            <div className="p-4">
              <div className="flex justify-between">
                <div>
                  <h4 className="mb-0">{Math.round((service.actualHours / service.estimatedHours) * 100)}%</h4>
                  <p className="mb-0">Progress</p>
                </div>
                <FaTools size={24} />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="grid grid-cols-12 gap-4">
        <div className="col-span-12">
          <ul className="flex space-x-4 flex border-b border-gray-200 mb-6">
            <li className="nav-item">
              <button 
                className={`nav-link ${activeTab === 'details' ? 'active' : ''}`}
                onClick={() => setActiveTab('details')}
              >
                Service Details
              </button>
            </li>
            <li className="nav-item">
              <button 
                className={`nav-link ${activeTab === 'progress' ? 'active' : ''}`}
                onClick={() => setActiveTab('progress')}
              >
                Progress ({service.progress.length})
              </button>
            </li>
            <li className="nav-item">
              <button 
                className={`nav-link ${activeTab === 'attachments' ? 'active' : ''}`}
                onClick={() => setActiveTab('attachments')}
              >
                Attachments ({service.attachments.length})
              </button>
            </li>
          </ul>

          {/* Tab Content */}
          {activeTab === 'details' && (
            <div className="grid grid-cols-12 gap-4">
              {/* Service Information */}
              <div className="lg:col-span-6 mb-6">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full">
                  <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
                    <h5 className="text-lg font-semibold text-gray-900 mb-0">
                      <FaTools className="mr-2" />
                      Service Information
                    </h5>
                  </div>
                  <div className="p-4">
                    <div className="grid grid-cols-12 gap-4">
                      <div className="sm:col-span-6 mb-3">
                        <strong>Service Type:</strong>
                        <p className="mb-0">{service.type}</p>
                      </div>
                      <div className="sm:col-span-6 mb-3">
                        <strong>Category:</strong>
                        <p className="mb-0">{service.category}</p>
                      </div>
                      <div className="col-span-12 mb-3">
                        <strong>Description:</strong>
                        <p className="mb-0">{service.description}</p>
                      </div>
                      <div className="sm:col-span-6 mb-3">
                        <strong>Scheduled Date:</strong>
                        <p className="mb-0">{new Date(service.scheduledDate).toLocaleDateString()}</p>
                      </div>
                      <div className="sm:col-span-6 mb-3">
                        <strong>Scheduled Time:</strong>
                        <p className="mb-0">{service.scheduledTime}</p>
                      </div>
                      <div className="col-span-12 mb-3">
                        <strong>Requirements:</strong>
                        <p className="mb-0">{service.requirements}</p>
                      </div>
                      <div className="col-span-12 mb-3">
                        <strong>Notes:</strong>
                        <p className="mb-0">{service.notes}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Customer & Contact Information */}
              <div className="lg:col-span-6 mb-6">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full">
                  <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
                    <h5 className="text-lg font-semibold text-gray-900 mb-0">
                      <FaUser className="mr-2" />
                      Customer & Contact
                    </h5>
                  </div>
                  <div className="p-4">
                    <div className="mb-3">
                      <strong>Customer:</strong>
                      <p className="mb-0">
                        <Link to={`/customers/${service.customerId}`} className="no-underline">
                          {service.customer}
                        </Link>
                      </p>
                    </div>
                    
                    <div className="mb-3">
                      <strong>Contact Person:</strong>
                      <p className="mb-0">{service.contactPerson}</p>
                    </div>
                    
                    <div className="mb-3">
                      <div className="flex items-center mb-2">
                        <FaPhone className="text-gray-600 mr-2" />
                        <strong>Phone:</strong>
                      </div>
                      <p className="mb-0 ml-6">{service.contactPhone}</p>
                    </div>
                    
                    <div className="mb-3">
                      <div className="flex items-center mb-2">
                        <FaEnvelope className="text-gray-600 mr-2" />
                        <strong>Email:</strong>
                      </div>
                      <p className="mb-0 ml-6">{service.contactEmail}</p>
                    </div>
                    
                    {service.serviceLocation === 'customer-site' && (
                      <div className="mb-3">
                        <div className="flex items-center mb-2">
                          <FaMapMarkerAlt className="text-gray-600 mr-2" />
                          <strong>Service Address:</strong>
                        </div>
                        <p className="mb-0 ml-6">
                          {service.address}<br />
                          {service.city}, {service.state}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Technician Information */}
              <div className="lg:col-span-6 mb-6">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full">
                  <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
                    <h5 className="text-lg font-semibold text-gray-900 mb-0">
                      <FaUser className="mr-2" />
                      Assigned Technician
                    </h5>
                  </div>
                  <div className="p-4">
                    <div className="mb-3">
                      <strong>Technician:</strong>
                      <p className="mb-0">{service.assignedTo}</p>
                    </div>
                    
                    <div className="mb-3">
                      <div className="flex items-center mb-2">
                        <FaPhone className="text-gray-600 mr-2" />
                        <strong>Phone:</strong>
                      </div>
                      <p className="mb-0 ml-6">{service.technicianPhone}</p>
                    </div>
                    
                    <div className="mb-3">
                      <div className="flex items-center mb-2">
                        <FaEnvelope className="text-gray-600 mr-2" />
                        <strong>Email:</strong>
                      </div>
                      <p className="mb-0 ml-6">{service.technicianEmail}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Financial Information */}
              <div className="lg:col-span-6 mb-6">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full">
                  <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
                    <h5 className="text-lg font-semibold text-gray-900 mb-0">
                      <FaRupeeSign className="mr-2" />
                      Financial Information
                    </h5>
                  </div>
                  <div className="p-4">
                    <div className="grid grid-cols-12 gap-4">
                      <div className="sm:col-span-6 mb-3">
                        <strong>Service Amount:</strong>
                        <p className="mb-0">₹{service.amount.toLocaleString()}</p>
                      </div>
                      <div className="sm:col-span-6 mb-3">
                        <strong>Currency:</strong>
                        <p className="mb-0">{service.currency}</p>
                      </div>
                      <div className="col-span-12 mb-3">
                        <strong>Payment Status:</strong>
                        <div className="mt-1">
                          {getPaymentStatusBadge(service.paymentStatus)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'progress' && (
            <div className="grid grid-cols-12 gap-4">
              <div className="col-span-12">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
                    <h5 className="text-lg font-semibold text-gray-900 mb-0">Service Progress Timeline</h5>
                  </div>
                  <div className="p-4">
                    <div className="timeline">
                      {service.progress.map((item, index) => (
                        <div key={item.id} className="timeline-item">
                          <div className="timeline-marker bg-primary-600"></div>
                          <div className="timeline-content">
                            <div className="flex justify-between items-start">
                              <div>
                                <h6 className="mb-1">{item.action}</h6>
                                <p className="mb-1">{item.description}</p>
                                <small className="text-gray-600">by {item.user}</small>
                              </div>
                              <small className="text-gray-600">{new Date(item.timestamp).toLocaleString()}</small>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'attachments' && (
            <div className="grid grid-cols-12 gap-4">
              <div className="col-span-12">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <div className="px-4 py-3 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
                    <h5 className="text-lg font-semibold text-gray-900 mb-0">Service Attachments</h5>
                    <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary-600 text-white border-primary-600 hover:bg-primary-700 focus:ring-primary-500 px-3 py-1 text-sm">
                      Upload File
                    </button>
                  </div>
                  <div className="p-4">
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200 hover:bg-gray-50">
                        <thead className="bg-gray-50">
                          <tr>
                            <th>File Name</th>
                            <th>Size</th>
                            <th>Uploaded By</th>
                            <th>Upload Date</th>
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {service.attachments.map(file => (
                            <tr key={file.id}>
                              <td>{file.name}</td>
                              <td>{file.size}</td>
                              <td>{file.uploadedBy}</td>
                              <td>{new Date(file.uploadedDate).toLocaleDateString()}</td>
                              <td>
                                <div className="inline-flex rounded-md shadow-sm" role="group">
                                  <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 px-3 py-1 text-sm border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500">
                                    Download
                                  </button>
                                  <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 px-3 py-1 text-sm border border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500">
                                    Delete
                                  </button>
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ServiceDetails;
