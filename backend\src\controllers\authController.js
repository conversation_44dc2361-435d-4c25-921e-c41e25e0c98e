import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import { sendEmail } from '../utils/email.js';
import { generateTokenPair } from '../utils/jwt.js';

/**
 * Register a new user
 */
export const register = async (req, res) => {
  try {
    const {
      email,
      password,
      firstName,
      lastName,
      phone,
      tenantName,
      tenantSlug,
    } = req.body;

    // Validate required fields
    if (!email || !password || !firstName || !lastName || !tenantName) {
      return res.status(400).json({
        success: false,
        message: 'All required fields must be provided',
        errors: {
          email: !email ? 'Email is required' : null,
          password: !password ? 'Password is required' : null,
          firstName: !firstName ? 'First name is required' : null,
          lastName: !lastName ? 'Last name is required' : null,
          tenantName: !tenantName ? 'Company name is required' : null,
        },
      });
    }

    // Check if user already exists
    const existingUser = await models.User.findOne({ where: { email } });
    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: 'User with this email already exists',
      });
    }

    // Generate tenant slug if not provided
    const slug = tenantSlug || tenantName.toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');

    // Check if tenant slug already exists
    const existingTenant = await models.Tenant.findOne({ where: { slug } });
    if (existingTenant) {
      return res.status(409).json({
        success: false,
        message: 'Company with this name already exists',
      });
    }

    // Start transaction
    const transaction = await models.sequelize.transaction();

    try {
      // Create tenant
      const tenant = await models.Tenant.create({
        name: tenantName,
        slug,
        subscription_plan: 'trial',
        subscription_status: 'trial',
        subscription_expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        is_active: true,
      }, { transaction });

      // Generate email verification token
      const emailVerificationToken = uuidv4();
      const emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      // Create user
      const user = await models.User.create({
        tenant_id: tenant.id,
        email,
        password, // Will be hashed by the model hook
        first_name: firstName,
        last_name: lastName,
        phone,
        is_active: true,
        is_verified: false,
        email_verification_token: emailVerificationToken,
        email_verification_expires: emailVerificationExpires,
      }, { transaction });

      // Assign admin role to the first user
      const adminRole = await models.Role.findOne({ 
        where: { slug: 'admin' } 
      });

      if (adminRole) {
        await models.UserRole.create({
          user_id: user.id,
          role_id: adminRole.id,
          assigned_at: new Date(),
          is_active: true,
        }, { transaction });
      }

      await transaction.commit();

      // Send verification email
      try {
        await sendEmail({
          to: email,
          subject: 'Verify your email address',
          template: 'email-verification',
          data: {
            firstName,
            verificationLink: `${process.env.FRONTEND_URL}/verify-email?token=${emailVerificationToken}`,
            companyName: tenantName,
          },
        });
      } catch (emailError) {
        logger.error('Failed to send verification email:', emailError);
        // Don't fail registration if email fails
      }

      logger.info('User registered successfully:', {
        userId: user.id,
        email,
        tenantId: tenant.id,
        tenantName,
      });

      res.status(201).json({
        success: true,
        message: 'Registration successful. Please check your email to verify your account.',
        data: {
          user: {
            id: user.id,
            email: user.email,
            firstName: user.first_name,
            lastName: user.last_name,
            isVerified: user.is_verified,
          },
          tenant: {
            id: tenant.id,
            name: tenant.name,
            slug: tenant.slug,
            subscriptionPlan: tenant.subscription_plan,
            subscriptionStatus: tenant.subscription_status,
          },
        },
      });

    } catch (error) {
      await transaction.rollback();
      throw error;
    }

  } catch (error) {
    logger.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Registration failed',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Login user
 */
export const login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validate input
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email and password are required',
      });
    }

    // Find user with tenant and roles
    const user = await models.User.findOne({
      where: { email },
      include: [
        {
          model: models.Tenant,
          as: 'tenant',
        },
        {
          model: models.Role,
          as: 'roles',
          through: {
            where: { is_active: true },
          },
          include: [
            {
              model: models.Permission,
              as: 'permissions',
            },
          ],
        },
      ],
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password',
      });
    }

    // Check password
    const isValidPassword = await user.validatePassword(password);
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password',
      });
    }

    // Check if user is active
    if (!user.is_active) {
      return res.status(401).json({
        success: false,
        message: 'Account is deactivated',
      });
    }

    // Check if tenant is active
    if (!user.tenant || !user.tenant.is_active) {
      return res.status(401).json({
        success: false,
        message: 'Company account is deactivated',
      });
    }

    // Check subscription status
    if (!user.tenant.isSubscriptionActive()) {
      return res.status(403).json({
        success: false,
        message: 'Company subscription has expired',
      });
    }

    // Flatten permissions
    const permissions = [];
    user.roles.forEach(role => {
      role.permissions.forEach(permission => {
        if (!permissions.includes(permission.slug)) {
          permissions.push(permission.slug);
        }
      });
    });

    // Generate JWT token with minimal payload to avoid header size limits
    const tokenUser = {
      id: user.id,
      email: user.email,
      tenant_id: user.tenant.id,
      // Store only role slugs instead of full role objects to reduce token size
      roles: user.roles.map(role => role.slug)
    };

    const { accessToken } = generateTokenPair(tokenUser);

    // Update last login
    await user.update({ last_login_at: new Date() });

    logger.info('User logged in successfully:', {
      userId: user.id,
      email: user.email,
      tenantId: user.tenant.id,
    });

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        token: accessToken,
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          phone: user.phone,
          isActive: user.is_active,
          isVerified: user.is_verified,
          lastLoginAt: user.last_login_at,
          profileImage: user.profile_image,
        },
        tenant: {
          id: user.tenant.id,
          name: user.tenant.name,
          slug: user.tenant.slug,
          subscriptionPlan: user.tenant.subscription_plan,
          subscriptionStatus: user.tenant.subscription_status,
          subscriptionExpiresAt: user.tenant.subscription_expires_at,
        },
        roles: user.roles.map(role => ({
          id: role.id,
          name: role.name,
          slug: role.slug,
          level: role.level,
        })),
        permissions,
      },
    });

  } catch (error) {
    logger.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Login failed',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Verify email address
 */
export const verifyEmail = async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Verification token is required',
      });
    }

    // Find user with verification token
    const user = await models.User.findOne({
      where: {
        email_verification_token: token,
        email_verification_expires: {
          [models.Sequelize.Op.gt]: new Date(),
        },
      },
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired verification token',
      });
    }

    // Update user as verified
    await user.update({
      is_verified: true,
      email_verification_token: null,
      email_verification_expires: null,
    });

    logger.info('Email verified successfully:', {
      userId: user.id,
      email: user.email,
    });

    res.json({
      success: true,
      message: 'Email verified successfully',
    });

  } catch (error) {
    logger.error('Email verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Email verification failed',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get current user profile
 */
export const getProfile = async (req, res) => {
  try {
    const user = await models.User.findByPk(req.user.id, {
      include: [
        {
          model: models.Tenant,
          as: 'tenant',
        },
        {
          model: models.Role,
          as: 'roles',
          through: {
            where: { is_active: true },
          },
          include: [
            {
              model: models.Permission,
              as: 'permissions',
            },
          ],
        },
      ],
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    // Flatten permissions
    const permissions = [];
    user.roles.forEach(role => {
      role.permissions.forEach(permission => {
        if (!permissions.includes(permission.slug)) {
          permissions.push(permission.slug);
        }
      });
    });

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          phone: user.phone,
          isActive: user.is_active,
          isVerified: user.is_verified,
          lastLoginAt: user.last_login_at,
          profileImage: user.profile_image,
          preferences: user.preferences,
        },
        tenant: {
          id: user.tenant.id,
          name: user.tenant.name,
          slug: user.tenant.slug,
          subscriptionPlan: user.tenant.subscription_plan,
          subscriptionStatus: user.tenant.subscription_status,
          subscriptionExpiresAt: user.tenant.subscription_expires_at,
        },
        roles: user.roles.map(role => ({
          id: role.id,
          name: role.name,
          slug: role.slug,
          level: role.level,
        })),
        permissions,
      },
    });

  } catch (error) {
    logger.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get profile',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};
