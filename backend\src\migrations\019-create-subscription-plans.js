import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  await queryInterface.createTable('subscription_plans', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    slug: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    price_monthly: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    price_yearly: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
    },
    currency: {
      type: DataTypes.STRING(3),
      defaultValue: 'INR',
    },
    stripe_price_id_monthly: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    stripe_price_id_yearly: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    stripe_product_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    trial_days: {
      type: DataTypes.INTEGER,
      defaultValue: 14,
    },
    max_users: {
      type: DataTypes.INTEGER,
      defaultValue: 5,
    },
    max_customers: {
      type: DataTypes.INTEGER,
      defaultValue: 100,
    },
    max_service_calls: {
      type: DataTypes.INTEGER,
      defaultValue: 50,
    },
    max_storage_gb: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
    },
    features: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    is_popular: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    metadata: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Create indexes
  await queryInterface.addIndex('subscription_plans', ['slug'], {
    unique: true,
    name: 'subscription_plans_slug_unique',
  });

  await queryInterface.addIndex('subscription_plans', ['is_active'], {
    name: 'subscription_plans_is_active_idx',
  });

  await queryInterface.addIndex('subscription_plans', ['sort_order'], {
    name: 'subscription_plans_sort_order_idx',
  });
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('subscription_plans');
};
