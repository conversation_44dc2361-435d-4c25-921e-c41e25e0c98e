import React from 'react';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';

const Register = () => {
  return (
    <>
      <Helmet>
        <title>Register - TallyCRM</title>
      </Helmet>

      <div className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Create Account</h2>
          <p className="text-gray-600">Join TallyCRM to manage your business efficiently</p>
        </div>

        {/* Coming Soon Card */}
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-8 text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full mb-4">
            <i className="bi bi-person-plus text-white text-2xl"></i>
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-3">Registration Coming Soon!</h3>
          <p className="text-gray-600 mb-6">
            We're working hard to bring you the registration feature.
            In the meantime, please contact your administrator for account access.
          </p>

          {/* Features Preview */}
          <div className="grid grid-cols-1 gap-3 mb-6">
            <div className="flex items-center text-sm text-gray-600">
              <i className="bi bi-check-circle text-green-500 mr-2"></i>
              <span>Secure account creation</span>
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <i className="bi bi-check-circle text-green-500 mr-2"></i>
              <span>Role-based access control</span>
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <i className="bi bi-check-circle text-green-500 mr-2"></i>
              <span>Email verification</span>
            </div>
          </div>
        </div>

        {/* Divider */}
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-gray-500">Already have an account?</span>
          </div>
        </div>

        {/* Sign In Link */}
        <div className="text-center">
          <Link
            to="/auth/login"
            className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium no-underline transition-colors duration-200 hover:underline"
          >
            <i className="bi bi-box-arrow-in-right mr-2"></i>
            Sign in to your account
          </Link>
        </div>
      </div>
    </>
  );
};

export default Register;
