# Test script to verify theme color API functionality
$API_BASE = "http://localhost:3002/api"

Write-Host "🔐 Logging in..." -ForegroundColor Yellow

# Test credentials
$testCredentials = @{
    email = "<EMAIL>"
    password = "Admin@123"
} | ConvertTo-Json

# Login to get token
$loginResponse = Invoke-RestMethod -Uri "$API_BASE/auth/login" -Method POST -Body $testCredentials -ContentType "application/json"

if (-not $loginResponse.success) {
    Write-Host "❌ Login failed: $($loginResponse.message)" -ForegroundColor Red
    exit 1
}

$token = $loginResponse.data.token
Write-Host "✅ Login successful" -ForegroundColor Green

# Set headers for authenticated requests
$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

# Get current user preferences
Write-Host "`n📖 Getting current user preferences..." -ForegroundColor Yellow
$userData = Invoke-RestMethod -Uri "$API_BASE/settings/user" -Method GET -Headers $headers
Write-Host "Current primary_color: $($userData.data.user.primary_color)" -ForegroundColor Cyan

# Update theme color
Write-Host "`n🎨 Updating theme color to #ff6b6b..." -ForegroundColor Yellow
$updateBody = @{
    primary_color = "#ff6b6b"
} | ConvertTo-Json

$updateResponse = Invoke-RestMethod -Uri "$API_BASE/settings/user" -Method PUT -Body $updateBody -Headers $headers
Write-Host "Update successful: $($updateResponse.success)" -ForegroundColor Green

# Get updated user preferences
Write-Host "`n📖 Getting updated user preferences..." -ForegroundColor Yellow
$updatedUserData = Invoke-RestMethod -Uri "$API_BASE/settings/user" -Method GET -Headers $headers
$savedColor = $updatedUserData.data.user.primary_color
Write-Host "Updated primary_color: $savedColor" -ForegroundColor Cyan

# Verify the color was saved
if ($savedColor -eq "#ff6b6b") {
    Write-Host "`n✅ Theme color successfully saved and retrieved!" -ForegroundColor Green
} else {
    Write-Host "`n❌ Theme color was not saved correctly. Expected: #ff6b6b, Got: $savedColor" -ForegroundColor Red
}
