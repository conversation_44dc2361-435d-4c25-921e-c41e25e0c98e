import React from 'react';
import { cn } from '../../../utils/helpers';

const Label = React.forwardRef(({
  children,
  className,
  required = false,
  ...props
}, ref) => {
  return (
    <label
      ref={ref}
      className={cn('block text-sm font-medium text-gray-700 mb-1', className)}
      {...props}
    >
      {children}
      {required && <span className="text-danger-500 ml-1">*</span>}
    </label>
  );
});

Label.displayName = 'Label';

export default Label;
