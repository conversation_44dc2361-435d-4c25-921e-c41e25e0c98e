import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const SaleItem = sequelize.define('SaleItem', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    sale_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'sales',
        key: 'id',
      },
    },
    item_type: {
      type: DataTypes.ENUM('product', 'license', 'service', 'additional_service'),
      allowNull: false,
      defaultValue: 'product',
    },
    product_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'tally_products',
        key: 'id',
      },
    },
    license_edition_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'license_editions',
        key: 'id',
      },
    },
    additional_service_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'additional_services',
        key: 'id',
      },
    },
    description: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 200],
      },
    },
    hsn_code: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    quantity: {
      type: DataTypes.DECIMAL(10, 3),
      allowNull: false,
      defaultValue: 1.000,
    },
    unit: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'Nos',
    },
    rate: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    discount_percentage: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    discount_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    taxable_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    gst_rate: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false,
      defaultValue: 18.00,
    },
    cgst_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    sgst_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    igst_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    total_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    delivery_status: {
      type: DataTypes.ENUM('pending', 'delivered', 'installed', 'completed'),
      allowNull: false,
      defaultValue: 'pending',
    },
    delivery_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    installation_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    warranty_period: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Warranty period in months',
    },
    warranty_start_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    warranty_end_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    serial_numbers: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Array of serial numbers for delivered items',
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'sale_items',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['sale_id'],
      },
      {
        fields: ['item_type'],
      },
      {
        fields: ['product_id'],
      },
      {
        fields: ['license_edition_id'],
      },
      {
        fields: ['additional_service_id'],
      },
      {
        fields: ['delivery_status'],
      },
    ],
  });

  // Instance methods
  SaleItem.prototype.calculateAmount = function() {
    return parseFloat(this.quantity) * parseFloat(this.rate);
  };

  SaleItem.prototype.calculateDiscountAmount = function() {
    const amount = this.calculateAmount();
    if (this.discount_percentage > 0) {
      return (amount * parseFloat(this.discount_percentage)) / 100;
    }
    return parseFloat(this.discount_amount) || 0;
  };

  SaleItem.prototype.calculateTaxableAmount = function() {
    const amount = this.calculateAmount();
    const discountAmount = this.calculateDiscountAmount();
    return amount - discountAmount;
  };

  SaleItem.prototype.calculateGSTAmount = function() {
    const taxableAmount = this.calculateTaxableAmount();
    return (taxableAmount * parseFloat(this.gst_rate)) / 100;
  };

  SaleItem.prototype.calculateTotalAmount = function() {
    const taxableAmount = this.calculateTaxableAmount();
    const gstAmount = this.calculateGSTAmount();
    return taxableAmount + gstAmount;
  };

  SaleItem.prototype.updateCalculatedFields = function() {
    this.amount = this.calculateAmount();
    this.discount_amount = this.calculateDiscountAmount();
    this.taxable_amount = this.calculateTaxableAmount();
    
    const gstAmount = this.calculateGSTAmount();
    // For intra-state transactions (CGST + SGST)
    this.cgst_amount = gstAmount / 2;
    this.sgst_amount = gstAmount / 2;
    this.igst_amount = 0;
    
    this.total_amount = this.calculateTotalAmount();
  };

  SaleItem.prototype.isWarrantyValid = function() {
    if (!this.warranty_end_date) return false;
    return new Date(this.warranty_end_date) >= new Date();
  };

  SaleItem.prototype.getWarrantyDaysRemaining = function() {
    if (!this.warranty_end_date) return null;
    const endDate = new Date(this.warranty_end_date);
    const today = new Date();
    const diffTime = endDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  // Associations
  SaleItem.associate = function(models) {
    SaleItem.belongsTo(models.Sale, {
      foreignKey: 'sale_id',
      as: 'sale',
    });

    SaleItem.belongsTo(models.TallyProduct, {
      foreignKey: 'product_id',
      as: 'product',
    });

    SaleItem.belongsTo(models.LicenseEdition, {
      foreignKey: 'license_edition_id',
      as: 'licenseEdition',
    });

    SaleItem.belongsTo(models.AdditionalService, {
      foreignKey: 'additional_service_id',
      as: 'additionalService',
    });
  };

  return SaleItem;
}
