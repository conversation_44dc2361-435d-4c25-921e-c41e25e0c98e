#!/bin/bash

# TallyCRM Production Deployment Script
# This script deploys TallyCRM using Docker in production

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.prod.yml"
ENV_FILE=".env.prod"
BACKUP_DIR="./backups"
LOG_FILE="./logs/deployment.log"

# Functions
log() {
    echo -e "${CYAN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check if environment file exists
    if [ ! -f "$ENV_FILE" ]; then
        error "Environment file $ENV_FILE not found. Please create it from .env.docker template."
    fi
    
    # Check if compose file exists
    if [ ! -f "$COMPOSE_FILE" ]; then
        error "Docker Compose file $COMPOSE_FILE not found."
    fi
    
    success "Prerequisites check passed"
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    
    mkdir -p logs
    mkdir -p uploads
    mkdir -p "$BACKUP_DIR"
    mkdir -p nginx/ssl
    
    success "Directories created"
}

# Backup existing data
backup_data() {
    log "Creating backup of existing data..."
    
    BACKUP_TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_PATH="$BACKUP_DIR/backup_$BACKUP_TIMESTAMP"
    
    mkdir -p "$BACKUP_PATH"
    
    # Backup database if container exists
    if docker-compose -f "$COMPOSE_FILE" ps postgres | grep -q "Up"; then
        info "Backing up PostgreSQL database..."
        docker-compose -f "$COMPOSE_FILE" exec -T postgres pg_dump -U postgres tallycrm_prod > "$BACKUP_PATH/database.sql" || warning "Database backup failed"
    fi
    
    # Backup uploads directory
    if [ -d "uploads" ]; then
        info "Backing up uploads directory..."
        cp -r uploads "$BACKUP_PATH/" || warning "Uploads backup failed"
    fi
    
    success "Backup created at $BACKUP_PATH"
}

# Pull latest images
pull_images() {
    log "Pulling latest Docker images..."
    
    docker-compose -f "$COMPOSE_FILE" pull || error "Failed to pull Docker images"
    
    success "Docker images pulled successfully"
}

# Build and deploy
deploy() {
    log "Starting deployment..."
    
    # Stop existing containers
    info "Stopping existing containers..."
    docker-compose -f "$COMPOSE_FILE" down || warning "Failed to stop some containers"
    
    # Build and start new containers
    info "Building and starting new containers..."
    docker-compose -f "$COMPOSE_FILE" up -d --build || error "Failed to start containers"
    
    success "Containers started successfully"
}

# Wait for services to be ready
wait_for_services() {
    log "Waiting for services to be ready..."
    
    # Wait for database
    info "Waiting for PostgreSQL..."
    timeout 60 bash -c 'until docker-compose -f '"$COMPOSE_FILE"' exec postgres pg_isready -U postgres; do sleep 2; done' || error "PostgreSQL failed to start"
    
    # Wait for Redis
    info "Waiting for Redis..."
    timeout 60 bash -c 'until docker-compose -f '"$COMPOSE_FILE"' exec redis redis-cli ping; do sleep 2; done' || error "Redis failed to start"
    
    # Wait for application
    info "Waiting for application..."
    timeout 120 bash -c 'until curl -f http://localhost:8080/health; do sleep 5; done' || error "Application failed to start"
    
    success "All services are ready"
}

# Run database migrations
run_migrations() {
    log "Running database migrations..."
    
    # Run migrations inside the app container
    docker-compose -f "$COMPOSE_FILE" exec app npm run migrate || error "Database migrations failed"
    
    success "Database migrations completed"
}

# Health check
health_check() {
    log "Performing health check..."
    
    # Check container status
    info "Checking container status..."
    docker-compose -f "$COMPOSE_FILE" ps
    
    # Check application health
    info "Checking application health..."
    HEALTH_RESPONSE=$(curl -s http://localhost:8080/health || echo "FAILED")
    
    if [[ "$HEALTH_RESPONSE" == *"healthy"* ]] || [[ "$HEALTH_RESPONSE" == *"ok"* ]]; then
        success "Application health check passed"
    else
        error "Application health check failed: $HEALTH_RESPONSE"
    fi
}

# Cleanup old images
cleanup() {
    log "Cleaning up old Docker images..."
    
    # Remove dangling images
    docker image prune -f || warning "Failed to clean up some images"
    
    success "Cleanup completed"
}

# Main deployment process
main() {
    log "🚀 Starting TallyCRM production deployment"
    
    check_prerequisites
    create_directories
    backup_data
    pull_images
    deploy
    wait_for_services
    run_migrations
    health_check
    cleanup
    
    success "🎉 Deployment completed successfully!"
    info "Application is available at: http://localhost:8080"
    info "To view logs: docker-compose -f $COMPOSE_FILE logs -f"
    info "To stop: docker-compose -f $COMPOSE_FILE down"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "backup")
        backup_data
        ;;
    "health")
        health_check
        ;;
    "logs")
        docker-compose -f "$COMPOSE_FILE" logs -f
        ;;
    "stop")
        docker-compose -f "$COMPOSE_FILE" down
        ;;
    "restart")
        docker-compose -f "$COMPOSE_FILE" restart
        ;;
    *)
        echo "Usage: $0 {deploy|backup|health|logs|stop|restart}"
        echo "  deploy  - Full deployment (default)"
        echo "  backup  - Create backup only"
        echo "  health  - Health check only"
        echo "  logs    - View logs"
        echo "  stop    - Stop services"
        echo "  restart - Restart services"
        exit 1
        ;;
esac
