docker build --no-cache -t cloudstier/tally-crm:v1 .
docker push cloudstier/tally-crm-prod
# Fixed Docker run command with proper port mapping and environment
docker run -d -p 5372:8080 --name tallycrmv1 --env-file .env.prod cloudstier/tally-crm:v1

# Alternative: Run with individual environment variables
docker run -d -p 5372:8080 --name tallycrmv1 \
  -e NODE_ENV=production \
  -e PORT=8080 \
  -e DB_HOST=your_db_host \
  -e DB_PORT=5432 \
  -e DB_NAME=tallycrm_prod \
  -e DB_USERNAME=postgres \
  -e DB_PASSWORD=your_db_password \
  -e JWT_SECRET=your_jwt_secret \
  cloudstier/tally-crm:v1