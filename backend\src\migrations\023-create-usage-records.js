import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  await queryInterface.createTable('usage_records', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    subscription_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'subscriptions',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    metric_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    timestamp: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    period_start: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    period_end: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    action: {
      type: DataTypes.ENUM('increment', 'decrement', 'set'),
      defaultValue: 'set',
    },
    metadata: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Create indexes
  await queryInterface.addIndex('usage_records', ['tenant_id'], {
    name: 'usage_records_tenant_id_idx',
  });

  await queryInterface.addIndex('usage_records', ['subscription_id'], {
    name: 'usage_records_subscription_id_idx',
  });

  await queryInterface.addIndex('usage_records', ['metric_name'], {
    name: 'usage_records_metric_name_idx',
  });

  await queryInterface.addIndex('usage_records', ['timestamp'], {
    name: 'usage_records_timestamp_idx',
  });

  await queryInterface.addIndex('usage_records', ['period_start', 'period_end'], {
    name: 'usage_records_period_idx',
  });

  await queryInterface.addIndex('usage_records', ['tenant_id', 'metric_name', 'period_start'], {
    name: 'usage_records_tenant_metric_period_idx',
  });
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('usage_records');
};
