import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const config = {
  // Application Configuration
  app: {
    name: process.env.APP_NAME || 'TallyCRM Backend',
    version: process.env.APP_VERSION || '1.0.0',
    port: parseInt(process.env.PORT) || 3001,
    env: process.env.NODE_ENV || 'development',
    url: process.env.APP_URL || 'http://localhost:3001'
  },

  // Frontend Configuration
  frontend: {
    url: process.env.FRONTEND_URL || 'http://localhost:3000',
    corsOrigins: process.env.CORS_ORIGINS ?
      process.env.CORS_ORIGINS.split(',') :
      ['http://localhost:3000', 'http://localhost:5173', 'http://localhost:3003'],
    enableCors: process.env.ENABLE_CORS !== 'false' // Default to true, set to false to disable
  },

  // JWT Configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'your_super_secret_jwt_key_here',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    refreshSecret: process.env.JWT_REFRESH_SECRET || 'your_super_secret_refresh_key_here',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d'
  },

  // Session Configuration
  session: {
    secret: process.env.SESSION_SECRET || 'your_super_secret_session_key_here',
    name: process.env.SESSION_NAME || 'tallycrm_session',
    cookieMaxAge: parseInt(process.env.SESSION_COOKIE_MAX_AGE) || 86400000 // 24 hours
  },

  // Security Configuration
  security: {
    bcryptSaltRounds: parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12,
    enableRateLimiting: process.env.ENABLE_RATE_LIMITING === 'true',
    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000, // 15 minutes
    rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100
  },

  // Email Configuration
  email: {
    smtp: {
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT) || 587,
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    },
    from: process.env.EMAIL_FROM || '<EMAIL>'
  },

  // File Upload Configuration
  upload: {
    path: process.env.UPLOAD_PATH || 'uploads/',
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 5242880, // 5MB
    allowedFileTypes: process.env.ALLOWED_FILE_TYPES ?
      process.env.ALLOWED_FILE_TYPES.split(',') :
      ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx', 'xls', 'xlsx']
  },

  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    filePath: process.env.LOG_FILE_PATH || 'logs/',
    maxSize: process.env.LOG_MAX_SIZE || '20m',
    maxFiles: process.env.LOG_MAX_FILES || '14d'
  },

  // API Configuration
  api: {
    prefix: process.env.API_PREFIX || '/api/v1',
    rateLimit: parseInt(process.env.API_RATE_LIMIT) || 1000
  },

  // External Services
  external: {
    googleMapsApiKey: process.env.GOOGLE_MAPS_API_KEY
  },

  // Cache Configuration (Redis)
  cache: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD || '',
    db: parseInt(process.env.REDIS_DB) || 0
  },

  // Monitoring Configuration
  monitoring: {
    enabled: process.env.ENABLE_MONITORING === 'true',
    healthCheckEndpoint: process.env.HEALTH_CHECK_ENDPOINT || '/health'
  },

  // Development Configuration
  development: {
    debug: process.env.DEBUG || 'tallycrm:*',
    enableSwagger: process.env.ENABLE_SWAGGER === 'true',
    swaggerPath: process.env.SWAGGER_PATH || '/api-docs'
  }
};

export default config;
