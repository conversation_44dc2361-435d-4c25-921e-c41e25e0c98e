# TallyCRM Docker Setup Guide

This guide explains how to set up and run TallyCRM using Docker for both development and production environments.

## 📋 Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- Git

## 🏗️ Docker Architecture

The TallyCRM Docker setup includes:

- **Multi-stage Dockerfile** for optimized production builds
- **Development Docker Compose** with hot-reload
- **Production Docker Compose** with PostgreSQL, Redis, and Nginx
- **Health checks** for all services
- **Volume persistence** for data and logs

## 🚀 Quick Start

### Development Environment

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd tallycrm
   ```

2. **Start development environment**
   ```bash
   # Start all services (PostgreSQL, Redis, Backend, Frontend)
   npm run docker:dev

   # Or with rebuild
   npm run docker:dev:build
   ```

3. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:3001
   - PostgreSQL: localhost:5432
   - Redis: localhost:6379

4. **View logs**
   ```bash
   npm run docker:dev:logs
   ```

5. **Stop services**
   ```bash
   npm run docker:dev:down
   ```

### Production Environment

1. **Prepare environment files (Automated)**
   ```bash
   # Automatically set up all environment files with secure defaults
   npm run docker:setup-env
   ```

   Or manually:
   ```bash
   cp .env.docker .env.prod
   # Edit .env.prod with your production values
   ```

2. **Configure environment variables**
   The Docker setup automatically handles environment files:
   - `.env.docker` - Template file (always copied to container)
   - `.env.prod` - Production environment file (used by docker-compose)
   - `backend/.env` - Backend-specific environment (if exists)
   - `frontend/.env` - Frontend-specific environment (if exists)

3. **Start production environment**
   ```bash
   npm run docker:prod:build
   ```

4. **Access the application**
   - Application: http://localhost:8080 (or your configured port)
   - With Nginx: http://localhost (HTTP) / https://localhost (HTTPS)

## 📁 Docker Files Overview

### Core Files
- `Dockerfile` - Multi-stage production build
- `Dockerfile.dev` - Development build with hot-reload
- `docker-compose.yml` - Development environment
- `docker-compose.prod.yml` - Production environment
- `.dockerignore` - Files to exclude from Docker context

### Configuration Files
- `.env.docker` - Template for Docker environment variables
- `nginx/nginx.conf` - Nginx reverse proxy configuration

## 🔧 Configuration

### Environment File Handling

The Docker setup automatically handles environment files with the following priority:

1. **Container Build Time:**
   - `.env.docker` is always copied as a fallback
   - `backend/.env.example` and `frontend/.env.example` are used as defaults
   - If `backend/.env` or `frontend/.env` exist, they override the examples

2. **Container Runtime:**
   - Production: Uses `.env.prod` file via docker-compose
   - Development: Uses environment variables defined in docker-compose.yml

### Environment Variables

#### Development (.env)
The development setup uses default values suitable for local development.

#### Production (.env.prod)
Copy `.env.docker` to `.env.prod` and configure:

```env
# Database
DB_PASSWORD=your_secure_postgres_password
REDIS_PASSWORD=your_secure_redis_password

# JWT Secrets (generate with: openssl rand -base64 32)
JWT_SECRET=your_jwt_secret_32_chars_minimum
JWT_REFRESH_SECRET=your_jwt_refresh_secret_32_chars_minimum

# Stripe
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Email
EMAIL_HOST=smtp.gmail.com
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# Application
APP_URL=https://yourdomain.com
```

### SSL Configuration (Production)

For HTTPS in production:

1. **Place SSL certificates**
   ```bash
   mkdir -p nginx/ssl
   # Copy your SSL certificate files:
   # nginx/ssl/cert.pem
   # nginx/ssl/key.pem
   ```

2. **Update nginx configuration** if needed in `nginx/nginx.conf`

## 🛠️ Available Commands

### Development
```bash
npm run docker:dev          # Start development environment
npm run docker:dev:build    # Start with rebuild
npm run docker:dev:down     # Stop development environment
npm run docker:dev:logs     # View logs
```

### Production
```bash
npm run docker:prod         # Start production environment
npm run docker:prod:build   # Start with rebuild
npm run docker:prod:down    # Stop production environment
```

### Manual Docker Commands
```bash
npm run docker:build        # Build production image
npm run docker:run          # Run production container
npm run docker:test         # Test Docker setup
npm run docker:deploy       # Deploy to production (with backup)
npm run docker:setup-env    # Set up environment files automatically
```

## 🧪 Testing and Automation

### Automated Testing
```bash
# Test the Docker setup automatically
npm run docker:test
```

This script will:
- Check Docker and Docker Compose installation
- Build and start all services
- Perform health checks on all endpoints
- Display service status and logs
- Provide troubleshooting information

### Production Deployment Script
```bash
# Deploy to production with automated backup
npm run docker:deploy

# Or use the script directly with options
bash scripts/deploy-production.sh deploy    # Full deployment
bash scripts/deploy-production.sh backup    # Backup only
bash scripts/deploy-production.sh health    # Health check
bash scripts/deploy-production.sh logs      # View logs
bash scripts/deploy-production.sh stop      # Stop services
bash scripts/deploy-production.sh restart   # Restart services
```

The deployment script includes:
- Prerequisites checking
- Automatic backup of database and files
- Zero-downtime deployment
- Database migrations
- Health checks
- Cleanup of old images

## 🔍 Troubleshooting

### Common Issues

1. **Port conflicts**
   ```bash
   # Check what's using the ports
   netstat -tulpn | grep :3000
   netstat -tulpn | grep :3001
   netstat -tulpn | grep :5432
   ```

2. **Permission issues**
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER logs uploads
   ```

3. **Database connection issues**
   ```bash
   # Check PostgreSQL logs
   docker-compose logs postgres
   
   # Connect to database manually
   docker exec -it tallycrm-postgres psql -U postgres -d tallycrm_dev
   ```

4. **Frontend not updating**
   ```bash
   # Rebuild frontend container
   docker-compose up -d --build frontend
   ```

### Health Checks

All services include health checks. Check status:
```bash
docker-compose ps
```

### Logs

View logs for specific services:
```bash
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f postgres
docker-compose logs -f redis
```

## 📊 Monitoring

### Container Stats
```bash
docker stats
```

### Database Monitoring
```bash
# Connect to PostgreSQL
docker exec -it tallycrm-postgres psql -U postgres -d tallycrm_dev

# Check database size
SELECT pg_size_pretty(pg_database_size('tallycrm_dev'));

# Check active connections
SELECT count(*) FROM pg_stat_activity;
```

### Redis Monitoring
```bash
# Connect to Redis
docker exec -it tallycrm-redis redis-cli

# Check memory usage
INFO memory

# Check connected clients
INFO clients
```

## 🔒 Security Considerations

1. **Change default passwords** in production
2. **Use strong JWT secrets** (minimum 32 characters)
3. **Configure SSL certificates** for HTTPS
4. **Set up firewall rules** for production
5. **Regular security updates** for base images
6. **Monitor logs** for suspicious activity

## 🚀 Deployment

### CI/CD Integration

The existing `.github/workflows/deploy.yml` is configured for Docker deployment. Ensure your CI/CD environment has:

- Docker Hub credentials
- Server SSH access
- Environment variables configured

### Manual Deployment

1. **Build and push image**
   ```bash
   docker build -t your-registry/tallycrm:latest .
   docker push your-registry/tallycrm:latest
   ```

2. **Deploy on server**
   ```bash
   docker pull your-registry/tallycrm:latest
   docker run -d --name tallycrm -p 8080:8080 --env-file .env.prod your-registry/tallycrm:latest
   ```

## 📚 Additional Resources

- [Docker Documentation](https://docs.docker.com/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [PostgreSQL Docker Image](https://hub.docker.com/_/postgres)
- [Redis Docker Image](https://hub.docker.com/_/redis)
- [Nginx Docker Image](https://hub.docker.com/_/nginx)
