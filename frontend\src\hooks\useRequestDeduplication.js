import { useRef, useCallback } from 'react';

/**
 * Custom hook to prevent duplicate API requests
 * @param {Function} requestFunction - The async function to execute
 * @param {number} debounceMs - Debounce time in milliseconds (default: 0)
 * @returns {Function} - Deduplicated request function
 */
export const useRequestDeduplication = (requestFunction, debounceMs = 0) => {
  const pendingRequests = useRef(new Set());
  const timeoutRef = useRef(null);

  const debouncedRequest = useCallback(
    (...args) => {
      // Create a unique key for this request
      const requestKey = JSON.stringify(args);

      // Clear existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // If debounce is enabled, delay the request
      if (debounceMs > 0) {
        timeoutRef.current = setTimeout(() => {
          executeRequest(requestKey, args);
        }, debounceMs);
      } else {
        executeRequest(requestKey, args);
      }
    },
    [requestFunction, debounceMs]
  );

  const executeRequest = useCallback(
    async (requestKey, args) => {
      // Check if this request is already pending
      if (pendingRequests.current.has(requestKey)) {
        console.log('Duplicate request prevented:', requestKey);
        return;
      }

      // Mark request as pending
      pendingRequests.current.add(requestKey);

      try {
        const result = await requestFunction(...args);
        return result;
      } catch (error) {
        throw error;
      } finally {
        // Remove from pending requests
        pendingRequests.current.delete(requestKey);
      }
    },
    [requestFunction]
  );

  return debouncedRequest;
};

/**
 * Custom hook for preventing button click spam
 * @param {Function} clickHandler - The click handler function
 * @param {number} cooldownMs - Cooldown time in milliseconds (default: 1000)
 * @returns {Function} - Debounced click handler
 */
export const useClickDeduplication = (clickHandler, cooldownMs = 1000) => {
  const lastClickTime = useRef(0);

  return useCallback(
    (...args) => {
      const now = Date.now();
      if (now - lastClickTime.current < cooldownMs) {
        console.log('Click spam prevented');
        return;
      }

      lastClickTime.current = now;
      return clickHandler(...args);
    },
    [clickHandler, cooldownMs]
  );
};

/**
 * Custom hook for preventing form submission spam
 * @param {Function} submitHandler - The form submit handler
 * @param {number} cooldownMs - Cooldown time in milliseconds (default: 2000)
 * @returns {Function} - Debounced submit handler
 */
export const useSubmitDeduplication = (submitHandler, cooldownMs = 2000) => {
  const isSubmitting = useRef(false);

  return useCallback(
    async (...args) => {
      if (isSubmitting.current) {
        console.log('Form submission spam prevented');
        return;
      }

      isSubmitting.current = true;

      try {
        const result = await submitHandler(...args);
        return result;
      } catch (error) {
        throw error;
      } finally {
        // Reset after cooldown period
        setTimeout(() => {
          isSubmitting.current = false;
        }, cooldownMs);
      }
    },
    [submitHandler, cooldownMs]
  );
};
