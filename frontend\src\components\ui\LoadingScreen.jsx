import React from 'react';
import { cn } from '../../utils/helpers';

const LoadingScreen = ({ 
  title = "Loading Dashboard...", 
  subtitle = "Fetching your business insights",
  className = "",
  variant = "dashboard" // dashboard, page, modal
}) => {
  const variants = {
    dashboard: {
      container: "min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100",
      spinner: "h-20 w-20",
      title: "text-xl font-bold",
      subtitle: "text-base"
    },
    page: {
      container: "min-h-[60vh] bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100",
      spinner: "h-16 w-16", 
      title: "text-lg font-semibold",
      subtitle: "text-sm"
    },
    modal: {
      container: "min-h-[200px] bg-white",
      spinner: "h-12 w-12",
      title: "text-base font-medium",
      subtitle: "text-sm"
    }
  };

  const config = variants[variant];

  return (
    <div className={cn(config.container, "flex justify-center items-center", className)}>
      <div className="text-center">
        <div className="relative mb-8">
          {/* Outer ring - light theme color */}
          <div 
            className={cn("animate-spin rounded-full border-4", config.spinner)}
            style={{ 
              borderColor: 'rgba(var(--primary-rgb), 0.2)',
              borderTopColor: 'transparent'
            }}
          ></div>
          
          {/* Inner ring - main theme color */}
          <div 
            className={cn("animate-spin rounded-full border-4 border-t-transparent absolute top-0 left-0", config.spinner)}
            style={{ 
              borderColor: 'var(--primary-color)',
              borderTopColor: 'transparent'
            }}
          ></div>
          
          {/* Ping effect - accent color */}
          <div 
            className={cn("animate-ping rounded-full border-4 border-opacity-30 absolute", 
              variant === 'dashboard' ? 'h-16 w-16 top-2 left-2' : 
              variant === 'page' ? 'h-12 w-12 top-2 left-2' :
              'h-8 w-8 top-2 left-2'
            )}
            style={{ 
              borderColor: 'rgba(var(--primary-rgb), 0.4)'
            }}
          ></div>
          
          {/* Center icon - optional */}
          {variant === 'dashboard' && (
            <div 
              className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 rounded-full flex items-center justify-center"
              style={{ backgroundColor: 'var(--primary-color)' }}
            >
              <svg 
                className="w-4 h-4" 
                style={{ color: 'var(--primary-text)' }}
                fill="currentColor" 
                viewBox="0 0 20 20"
              >
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
              </svg>
            </div>
          )}
        </div>

        <div className="space-y-3">
          <h3 
            className={cn(config.title)}
            style={{ color: 'var(--primary-color)' }}
          >
            {title}
          </h3>
          <p 
            className={cn(config.subtitle)}
            style={{ color: 'rgba(var(--primary-rgb), 0.8)' }}
          >
            {subtitle}
          </p>

          {/* Loading progress dots */}
          <div className="flex justify-center space-x-2 mt-4">
            <div 
              className="w-2 h-2 rounded-full animate-bounce"
              style={{ backgroundColor: 'rgba(var(--primary-rgb), 0.6)' }}
            ></div>
            <div 
              className="w-2 h-2 rounded-full animate-bounce"
              style={{ 
                backgroundColor: 'rgba(var(--primary-rgb), 0.8)',
                animationDelay: '0.1s' 
              }}
            ></div>
            <div 
              className="w-2 h-2 rounded-full animate-bounce"
              style={{ 
                backgroundColor: 'var(--primary-color)',
                animationDelay: '0.2s' 
              }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;
