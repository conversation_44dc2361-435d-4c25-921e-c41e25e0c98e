import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const AdditionalService = sequelize.define('AdditionalService', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 100],
      },
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        len: [2, 20],
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    category: {
      type: DataTypes.ENUM('training', 'customization', 'integration', 'support', 'consulting', 'other'),
      allowNull: false,
      defaultValue: 'support',
    },
    service_type: {
      type: DataTypes.ENUM('onetime', 'recurring', 'hourly', 'project'),
      allowNull: false,
      defaultValue: 'onetime',
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    cost_price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    unit: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'Service',
    },
    duration_hours: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Estimated duration in hours',
    },
    hsn_code: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: '998399',
      comment: 'HSN/SAC code for GST',
    },
    gst_rate: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false,
      defaultValue: 18.00,
      comment: 'GST rate percentage',
    },
    requires_approval: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Whether this service requires management approval',
    },
    is_billable: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: 'Whether this service is billable to customer',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    prerequisites: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Array of prerequisites for this service',
    },
    deliverables: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Array of deliverables for this service',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'additional_services',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['code'],
        unique: true,
      },
      {
        fields: ['category'],
      },
      {
        fields: ['service_type'],
      },
      {
        fields: ['is_billable'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['sort_order'],
      },
    ],
  });

  // Class methods
  AdditionalService.getDefaultServices = function() {
    return [
      {
        name: 'Basic Tally Training',
        code: 'BASIC_TRAINING',
        description: 'Basic Tally software training for new users',
        category: 'training',
        service_type: 'onetime',
        price: 5000.00,
        cost_price: 3000.00,
        unit: 'Session',
        duration_hours: 8,
        hsn_code: '998399',
        gst_rate: 18.00,
        requires_approval: false,
        is_billable: true,
        prerequisites: ['Tally software installed', 'Basic computer knowledge'],
        deliverables: ['Training manual', 'Practice exercises', 'Certificate'],
        sort_order: 1,
      },
      {
        name: 'Advanced Tally Training',
        code: 'ADV_TRAINING',
        description: 'Advanced Tally features and customization training',
        category: 'training',
        service_type: 'onetime',
        price: 8000.00,
        cost_price: 5000.00,
        unit: 'Session',
        duration_hours: 16,
        hsn_code: '998399',
        gst_rate: 18.00,
        requires_approval: false,
        is_billable: true,
        prerequisites: ['Basic Tally knowledge', 'Completed basic training'],
        deliverables: ['Advanced manual', 'Custom reports', 'Certificate'],
        sort_order: 2,
      },
      {
        name: 'Data Migration Service',
        code: 'DATA_MIGRATION',
        description: 'Data migration from other accounting software',
        category: 'support',
        service_type: 'project',
        price: 10000.00,
        cost_price: 6000.00,
        unit: 'Project',
        duration_hours: 20,
        hsn_code: '998399',
        gst_rate: 18.00,
        requires_approval: true,
        is_billable: true,
        prerequisites: ['Source data backup', 'Data format specification'],
        deliverables: ['Migrated data', 'Data validation report', 'Documentation'],
        sort_order: 3,
      },
      {
        name: 'Custom Report Development',
        code: 'CUSTOM_REPORT',
        description: 'Development of custom reports and formats',
        category: 'customization',
        service_type: 'project',
        price: 3000.00,
        cost_price: 2000.00,
        unit: 'Report',
        duration_hours: 4,
        hsn_code: '998399',
        gst_rate: 18.00,
        requires_approval: false,
        is_billable: true,
        prerequisites: ['Report requirements', 'Sample format'],
        deliverables: ['Custom report', 'User guide', 'Testing'],
        sort_order: 4,
      },
      {
        name: 'Annual Maintenance Visit',
        code: 'AMC_VISIT',
        description: 'Annual maintenance and health check visit',
        category: 'support',
        service_type: 'recurring',
        price: 2000.00,
        cost_price: 1200.00,
        unit: 'Visit',
        duration_hours: 2,
        hsn_code: '998399',
        gst_rate: 18.00,
        requires_approval: false,
        is_billable: true,
        prerequisites: ['Valid AMC contract'],
        deliverables: ['Health check report', 'Recommendations', 'Minor fixes'],
        sort_order: 5,
      },
      {
        name: 'Remote Support Session',
        code: 'REMOTE_SUPPORT',
        description: 'Remote support session for issue resolution',
        category: 'support',
        service_type: 'hourly',
        price: 500.00,
        cost_price: 300.00,
        unit: 'Hour',
        duration_hours: 1,
        hsn_code: '998399',
        gst_rate: 18.00,
        requires_approval: false,
        is_billable: true,
        prerequisites: ['Internet connection', 'Remote access setup'],
        deliverables: ['Issue resolution', 'Session summary'],
        sort_order: 6,
      },
      {
        name: 'GST Compliance Setup',
        code: 'GST_SETUP',
        description: 'GST compliance setup and configuration',
        category: 'consulting',
        service_type: 'onetime',
        price: 5000.00,
        cost_price: 3000.00,
        unit: 'Service',
        duration_hours: 6,
        hsn_code: '998399',
        gst_rate: 18.00,
        requires_approval: false,
        is_billable: true,
        prerequisites: ['GSTIN number', 'Business details'],
        deliverables: ['GST configuration', 'Sample returns', 'Documentation'],
        sort_order: 7,
      },
      {
        name: 'Backup & Recovery Setup',
        code: 'BACKUP_SETUP',
        description: 'Automated backup and recovery system setup',
        category: 'support',
        service_type: 'onetime',
        price: 3000.00,
        cost_price: 2000.00,
        unit: 'Service',
        duration_hours: 3,
        hsn_code: '998399',
        gst_rate: 18.00,
        requires_approval: false,
        is_billable: true,
        prerequisites: ['Backup storage location', 'Access permissions'],
        deliverables: ['Backup system', 'Recovery procedure', 'Testing'],
        sort_order: 8,
      },
    ];
  };

  // Instance methods
  AdditionalService.prototype.getMarginPercentage = function() {
    if (this.cost_price > 0) {
      return ((this.price - this.cost_price) / this.cost_price) * 100;
    }
    return 0;
  };

  AdditionalService.prototype.getGSTAmount = function(quantity = 1) {
    const baseAmount = this.price * quantity;
    return (baseAmount * this.gst_rate) / 100;
  };

  AdditionalService.prototype.getTotalAmount = function(quantity = 1) {
    const baseAmount = this.price * quantity;
    const gstAmount = this.getGSTAmount(quantity);
    return baseAmount + gstAmount;
  };

  // Associations
  AdditionalService.associate = function(models) {
    AdditionalService.hasMany(models.ServiceCallItem, {
      foreignKey: 'additional_service_id',
      as: 'serviceCallItems',
    });

    AdditionalService.hasMany(models.SaleItem, {
      foreignKey: 'additional_service_id',
      as: 'saleItems',
    });
  };

  return AdditionalService;
}
