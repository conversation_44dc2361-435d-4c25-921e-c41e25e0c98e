// Simple test using Node.js built-in modules
const http = require('http');

function makeRequest(options, data) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(body));
        } catch (e) {
          resolve(body);
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testAPI() {
  try {
    console.log('🔐 Testing login...');
    
    // Login
    const loginOptions = {
      hostname: 'localhost',
      port: 3002,
      path: '/api/v1/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const loginData = {
      email: '<EMAIL>',
      password: 'password123'
    };
    
    const loginResponse = await makeRequest(loginOptions, loginData);
    
    if (!loginResponse.success) {
      console.log('❌ Login failed:', loginResponse.message);
      return;
    }
    
    const token = loginResponse.data.token;
    console.log('✅ Login successful');
    
    // Get user preferences
    console.log('\n📖 Getting user preferences...');
    const getUserOptions = {
      hostname: 'localhost',
      port: 3002,
      path: '/api/v1/settings/user',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };
    
    const userData = await makeRequest(getUserOptions);
    console.log('Current primary_color:', userData.data?.user?.primary_color);
    
    // Update theme color
    console.log('\n🎨 Updating theme color...');
    const updateOptions = {
      hostname: 'localhost',
      port: 3002,
      path: '/api/v1/settings/user',
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };
    
    const updateData = {
      primary_color: '#ff6b6b'
    };
    
    const updateResponse = await makeRequest(updateOptions, updateData);
    console.log('Update response:', updateResponse.success ? 'Success' : 'Failed');
    
    // Get updated preferences
    console.log('\n📖 Getting updated preferences...');
    const updatedUserData = await makeRequest(getUserOptions);
    const savedColor = updatedUserData.data?.user?.primary_color;
    console.log('Updated primary_color:', savedColor);
    
    if (savedColor === '#ff6b6b') {
      console.log('\n✅ Theme color successfully saved!');
    } else {
      console.log('\n❌ Theme color not saved correctly');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testAPI();
