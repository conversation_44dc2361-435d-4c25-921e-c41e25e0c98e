import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { customerAPI, apiService } from '../../services/api';
import { SearchableSelect } from '../../components/ui';
import {
  FaSave,
  FaTimes,
  FaUser,
  FaBuilding,
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt,
  FaPlus,
  FaMinus,
  FaMap,
  FaCog,
  FaCalendar,
  FaShieldAlt,
  FaTools,
  FaClipboardList
} from 'react-icons/fa';

const CustomerFormValidated = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = Boolean(id);

  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    // Basic Info (matching todo.md requirements)
    customerName: '',
    tallySerialNo: '',
    product: null,
    licenceEdition: null,
    location: null,
    profileStatus: 'FOLLOW UP',
    followUpExecutive: null,
    mapLocation: '',
    latitude: '',
    longitude: '',
    gstNo: '',
    remarks: '',

    // Customer Address Book (Multiple Entries)
    addressBook: [
      {
        type: '',
        contactPerson: '',
        mobileNumbers: [''],
        phone: '',
        email: '',
        isMandatory: false
      }
    ],

    // Tally Software Service (TSS)
    tssStatus: 'NO',
    tssExpiryDate: '',
    adminEmail: '',

    // AMC (Annual Maintenance Contract)
    amcStatus: 'NO',
    amcFromDate: '',
    amcToDate: '',
    renewalDate: '',
    noOfVisits: '',
    currentAmcAmount: '',
    lastYearAmcAmount: '',

    // Additional Services
    additionalServices: [],

    // Additional Features (New section) - with expiry dates like TSS
    tdlAddons: false,
    tdlAddonsExpiryDate: '',
    whatsappTelegramGroup: false,
    whatsappTelegramGroupExpiryDate: '',
    autoBackup: false,
    autoBackupExpiryDate: '',
    cloudUser: false,
    cloudUserExpiryDate: '',
    mobileApp: false,
    mobileAppExpiryDate: ''
  });

  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});

  // Master data states
  const [tallyProducts, setTallyProducts] = useState([]);
  const [licenseEditions, setLicenseEditions] = useState([]);
  const [areas, setAreas] = useState([]);
  const [executives, setExecutives] = useState([]);

  // Loading states
  const [loadingTallyProducts, setLoadingTallyProducts] = useState(false);
  const [loadingLicenseEditions, setLoadingLicenseEditions] = useState(false);
  const [loadingAreas, setLoadingAreas] = useState(false);
  const [loadingExecutives, setLoadingExecutives] = useState(false);

  // Required fields for validation
  const requiredFields = ['customerName', 'tallySerialNo'];

  const designationOptions = [
    { value: 'owner', label: '👑 Owner', isMandatory: true },
    { value: 'manager', label: '👨‍💼 Manager', isMandatory: false },
    { value: 'accountant', label: '📊 Accountant', isMandatory: false },
    { value: 'auditor-CA', label: '🎓 Auditor-CA', isMandatory: false },
    { value: 'auditor-STP', label: '📋 Auditor-STP', isMandatory: false }
  ];

  const additionalServicesOptions = [
    { id: 1, name: 'Data Migration', icon: '📊', color: 'blue' },
    { id: 2, name: 'Training', icon: '🎓', color: 'green' },
    { id: 3, name: 'Customization', icon: '⚙️', color: 'purple' },
    { id: 4, name: 'Support', icon: '🛠️', color: 'orange' },
    { id: 5, name: 'Backup Setup', icon: '💾', color: 'teal' },
    { id: 6, name: 'Network Setup', icon: '🌐', color: 'indigo' }
  ];

  useEffect(() => {
    fetchDropdownData();
    if (isEdit) {
      fetchCustomer();
    }
  }, [isEdit, id]);

  // Fetch dropdown data for form
  const fetchDropdownData = async () => {
    try {
      // Fetch tally products
      try {
        setLoadingTallyProducts(true);
        const tallyProductsResponse = await apiService.get('/master-data/tally-products');
        if (tallyProductsResponse.data?.success && Array.isArray(tallyProductsResponse.data.data?.tallyproduct)) {
          setTallyProducts(tallyProductsResponse.data.data.tallyproduct);
        } else {
          console.warn('Tally Products API returned invalid data:', tallyProductsResponse.data);
          setTallyProducts([]);
        }
      } catch (error) {
        console.error('Error fetching tally products:', error);
        setTallyProducts([]);
      } finally {
        setLoadingTallyProducts(false);
      }

      // Fetch license editions
      try {
        setLoadingLicenseEditions(true);
        const licenseEditionsResponse = await apiService.get('/master-data/license-editions');
        if (licenseEditionsResponse.data?.success && Array.isArray(licenseEditionsResponse.data.data?.licenseedition)) {
          setLicenseEditions(licenseEditionsResponse.data.data.licenseedition);
        } else {
          console.warn('License Editions API returned invalid data:', licenseEditionsResponse.data);
          setLicenseEditions([]);
        }
      } catch (error) {
        console.error('Error fetching license editions:', error);
        setLicenseEditions([]);
      } finally {
        setLoadingLicenseEditions(false);
      }

      // Fetch areas
      try {
        setLoadingAreas(true);
        const areasResponse = await apiService.get('/master-data/areas');
        if (areasResponse.data?.success && Array.isArray(areasResponse.data.data?.area)) {
          setAreas(areasResponse.data.data.area);
        } else {
          console.warn('Areas API returned invalid data:', areasResponse.data);
          setAreas([]);
        }
      } catch (areasError) {
        console.error('Error fetching areas:', areasError);
        setAreas([]);
      } finally {
        setLoadingAreas(false);
      }

      // Fetch executives
      try {
        setLoadingExecutives(true);
        const executivesResponse = await apiService.get('/executives');
        if (executivesResponse.data?.success && Array.isArray(executivesResponse.data.data?.executives)) {
          setExecutives(executivesResponse.data.data.executives);
        } else {
          console.warn('Executives API returned invalid data:', executivesResponse.data);
          setExecutives([]);
        }
      } catch (executivesError) {
        console.error('Error fetching executives:', executivesError);
        setExecutives([]);
      } finally {
        setLoadingExecutives(false);
      }
    } catch (error) {
      console.error('Error fetching dropdown data:', error);
      // Set empty arrays as fallback
      setTallyProducts([]);
      setLicenseEditions([]);
      setAreas([]);
      setExecutives([]);
    }
  };

  const fetchCustomer = async () => {
    try {
      setLoading(true);
      const response = await customerAPI.getById(id, { includeRelations: true });

      if (response.data?.success && response.data?.data?.customer) {
        const customer = response.data.data.customer;
        const customFields = customer.custom_fields || {};

        setFormData({
          customerName: customer.company_name || '',
          tallySerialNo: customer.customer_code || customer.tally_serial_number || '',
          product: customFields.product_id || null,
          licenceEdition: customFields.license_edition_id || null,
          location: customer.area_id || null,
          profileStatus: customFields.profile_status || 'FOLLOW UP',
          followUpExecutive: customer.assigned_executive_id || customFields.follow_up_executive_id || null,
          mapLocation: customer.address_line_1 || customFields.map_location || '',
          latitude: customer.latitude || '',
          longitude: customer.longitude || '',
          gstNo: customer.gst_number || '',
          remarks: customer.notes || '',
          addressBook: customFields.address_book && customFields.address_book.length > 0
            ? customFields.address_book.map(entry => ({
                type: entry.type || '',
                contactPerson: entry.contact_person || '',
                mobileNumbers: entry.mobile_numbers || [''],
                phone: entry.phone || '',
                email: entry.email || '',
                isMandatory: entry.is_mandatory || false
              }))
            : [{
                type: '',
                contactPerson: customer.contact_person || '',
                mobileNumbers: [customer.phone || ''],
                phone: customer.phone || '',
                email: customer.email || '',
                isMandatory: false
              }],
          tssStatus: customFields.tss_status || 'NO',
          tssExpiryDate: customFields.tss_expiry_date || '',
          adminEmail: customFields.admin_email || '',
          amcStatus: customFields.amc_status || 'NO',
          amcFromDate: customFields.amc_from_date || '',
          amcToDate: customFields.amc_to_date || '',
          renewalDate: customFields.renewal_date || '',
          noOfVisits: customFields.no_of_visits || '',
          currentAmcAmount: customFields.current_amc_amount || '',
          lastYearAmcAmount: customFields.last_year_amc_amount || '',
          additionalServices: customFields.additional_services || [],
          tdlAddons: customFields.tdl_addons || false,
          tdlAddonsExpiryDate: customFields.tdl_addons_expiry_date || '',
          whatsappTelegramGroup: customFields.whatsapp_telegram_group || false,
          whatsappTelegramGroupExpiryDate: customFields.whatsapp_telegram_group_expiry_date || '',
          autoBackup: customFields.auto_backup || false,
          autoBackupExpiryDate: customFields.auto_backup_expiry_date || '',
          cloudUser: customFields.cloud_user || false,
          cloudUserExpiryDate: customFields.cloud_user_expiry_date || '',
          mobileApp: customFields.mobile_app || false,
          mobileAppExpiryDate: customFields.mobile_app_expiry_date || ''
        });
      }
    } catch (error) {
      console.error('Error fetching customer:', error);
      toast.error('Failed to load customer data');
      navigate('/customers');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    let processedValue = value;

    // Handle checkbox inputs
    if (type === 'checkbox') {
      processedValue = checked;
    }

    // Convert Tally Serial No to uppercase and allow only alphanumeric characters
    if (name === 'tallySerialNo') {
      processedValue = value.toUpperCase().replace(/[^A-Z0-9]/g, '');
    }

    setFormData(prev => ({
      ...prev,
      [name]: processedValue
    }));

    // Mark field as touched
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));

    // Real-time validation
    if (touched[name] || processedValue !== '') {
      validateField(name, processedValue);
    }
  };

  // Handle SearchableSelect changes
  const handleSearchableSelectChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Mark field as touched
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));

    // Real-time validation
    if (touched[name] || value !== null) {
      validateField(name, value);
    }
  };

  // Helper functions for address book management
  const addAddressBookEntry = () => {
    setFormData(prev => ({
      ...prev,
      addressBook: [
        ...prev.addressBook,
        {
          type: '',
          contactPerson: '',
          mobileNumbers: [''],
          phone: '',
          email: '',
          isMandatory: false
        }
      ]
    }));
  };

  const removeAddressBookEntry = (index) => {
    setFormData(prev => ({
      ...prev,
      addressBook: prev.addressBook.filter((_, i) => i !== index)
    }));
  };

  const handleAddressBookChange = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      addressBook: prev.addressBook.map((entry, i) =>
        i === index ? { ...entry, [field]: value } : entry
      )
    }));
  };

  const addMobileNumber = (addressIndex) => {
    setFormData(prev => ({
      ...prev,
      addressBook: prev.addressBook.map((entry, index) =>
        index === addressIndex
          ? { ...entry, mobileNumbers: [...entry.mobileNumbers, ''] }
          : entry
      )
    }));
  };

  const removeMobileNumber = (addressIndex, mobileIndex) => {
    setFormData(prev => ({
      ...prev,
      addressBook: prev.addressBook.map((entry, index) =>
        index === addressIndex
          ? { ...entry, mobileNumbers: entry.mobileNumbers.filter((_, i) => i !== mobileIndex) }
          : entry
      )
    }));
  };

  const handleMobileNumberChange = (addressIndex, mobileIndex, value) => {
    setFormData(prev => ({
      ...prev,
      addressBook: prev.addressBook.map((entry, index) =>
        index === addressIndex
          ? {
              ...entry,
              mobileNumbers: entry.mobileNumbers.map((num, i) =>
                i === mobileIndex ? value : num
              )
            }
          : entry
      )
    }));
  };

  const handleAdditionalServiceToggle = (serviceId) => {
    setFormData(prev => ({
      ...prev,
      additionalServices: prev.additionalServices.includes(serviceId)
        ? prev.additionalServices.filter(id => id !== serviceId)
        : [...prev.additionalServices, serviceId]
    }));
  };



  const validateField = (fieldName, value) => {
    // Basic validation for new fields
    if (requiredFields.includes(fieldName) && (!value || value.toString().trim() === '')) {
      return { isValid: false, message: `${fieldName} is required` };
    }

    // Email validation
    if (fieldName.includes('email') && value && !/\S+@\S+\.\S+/.test(value)) {
      return { isValid: false, message: 'Please enter a valid email' };
    }

    return { isValid: true, message: '' };
  };

  const validateForm = () => {
    const newErrors = {};

    // Required fields validation
    if (!formData.customerName.trim()) {
      newErrors.customerName = 'Customer name is required';
    }

    if (!formData.tallySerialNo.trim()) {
      newErrors.tallySerialNo = 'Tally Serial No is required';
    }

    // Validate mandatory address book entries
    const mandatoryDesignations = designationOptions.filter(d => d.isMandatory).map(d => d.value);
    const addressBookTypes = formData.addressBook.map(entry => entry.type);

    mandatoryDesignations.forEach(designation => {
      if (!addressBookTypes.includes(designation)) {
        newErrors.addressBook = `${designation} contact is mandatory`;
      }
    });

    // Validate address book entries
    formData.addressBook.forEach((entry, index) => {
      if (entry.type && !entry.contactPerson.trim()) {
        newErrors[`addressBook_${index}_contactPerson`] = 'Contact person is required';
      }
      if (entry.type && entry.mobileNumbers.every(num => !num.trim())) {
        newErrors[`addressBook_${index}_mobile`] = 'At least one mobile number is required';
      }
      if (entry.type && !entry.email.trim()) {
        newErrors[`addressBook_${index}_email`] = 'Email is required';
      } else if (entry.email && !/\S+@\S+\.\S+/.test(entry.email)) {
        newErrors[`addressBook_${index}_email`] = 'Please enter a valid email';
      }
    });

    // GST validation
    if (formData.gstNo && formData.gstNo.length !== 15) {
      newErrors.gstNo = 'GST number must be exactly 15 characters';
    }

    // TSS validation
    if (formData.tssStatus === 'YES') {
      if (!formData.tssExpiryDate) {
        newErrors.tssExpiryDate = 'TSS Expiry Date is required when TSS is active';
      }
      if (!formData.adminEmail) {
        newErrors.adminEmail = 'Admin Email is required when TSS is active';
      } else if (!/\S+@\S+\.\S+/.test(formData.adminEmail)) {
        newErrors.adminEmail = 'Please enter a valid admin email';
      }
    }

    // AMC validation
    if (formData.amcStatus === 'YES') {
      if (!formData.amcFromDate) newErrors.amcFromDate = 'AMC From Date is required';
      if (!formData.amcToDate) newErrors.amcToDate = 'AMC To Date is required';
      if (!formData.renewalDate) newErrors.renewalDate = 'Renewal Date is required';
      if (!formData.noOfVisits) newErrors.noOfVisits = 'Number of visits is required';
      if (!formData.currentAmcAmount) newErrors.currentAmcAmount = 'Current AMC amount is required';
    }

    // Additional Features validation - like TSS
    if (formData.tdlAddons && !formData.tdlAddonsExpiryDate) {
      newErrors.tdlAddonsExpiryDate = 'Expiry Date is required when TDL & Addons is selected';
    }
    if (formData.whatsappTelegramGroup && !formData.whatsappTelegramGroupExpiryDate) {
      newErrors.whatsappTelegramGroupExpiryDate = 'Expiry Date is required when WhatsApp/Telegram Group is selected';
    }
    if (formData.autoBackup && !formData.autoBackupExpiryDate) {
      newErrors.autoBackupExpiryDate = 'Expiry Date is required when Auto Backup is selected';
    }
    if (formData.cloudUser && !formData.cloudUserExpiryDate) {
      newErrors.cloudUserExpiryDate = 'Expiry Date is required when Cloud User is selected';
    }
    if (formData.mobileApp && !formData.mobileAppExpiryDate) {
      newErrors.mobileAppExpiryDate = 'Expiry Date is required when Mobile App is selected';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    console.log('=== CUSTOMER FORM SUBMISSION STARTED ===');
    console.log('Form data:', formData);
    console.log('Is edit mode:', isEdit);
    console.log('Required fields:', requiredFields);

    // Mark all fields as touched
    const allTouched = {};
    Object.keys(formData).forEach(key => {
      allTouched[key] = true;
    });
    setTouched(allTouched);

    if (!validateForm()) {
      console.log('Form validation failed');
      toast.error('Please fix the errors in the form');
      return;
    }

    console.log('Form validation passed, making API call');
    setLoading(true);

    try {
      // Prepare data for API according to backend Customer model structure
      const customerData = {
        // Required fields
        company_name: formData.customerName,
        customer_code: formData.tallySerialNo, // Using tally serial as customer code

        // Optional basic fields
        display_name: formData.customerName,
        customer_type: 'customer', // Default type
        business_type: 'private_limited', // Default business type

        // Contact information from address book
        email: formData.addressBook.find(entry => entry.email)?.email || null,
        phone: formData.addressBook.find(entry => entry.mobileNumbers?.length > 0)?.mobileNumbers[0] || null,

        // Address fields
        address_line_1: formData.mapLocation || null,
        city: formData.location || null,
        state: null,
        country: 'India',
        postal_code: null,

        // Business details
        gst_number: formData.gstNo || null,
        pan_number: null,
        area_id: formData.location || null,
        assigned_executive_id: formData.followUpExecutive || null,

        // Tally specific fields - store IDs in custom_fields for now since backend expects names
        tally_version: null, // Will be stored in custom_fields
        tally_serial_number: formData.tallySerialNo,
        license_type: null, // Will be stored in custom_fields

        // Location
        latitude: formData.latitude ? parseFloat(formData.latitude) : null,
        longitude: formData.longitude ? parseFloat(formData.longitude) : null,

        // Additional fields
        notes: formData.remarks || null,

        // Custom fields for additional data that doesn't map to standard fields
        custom_fields: {
          profile_status: formData.profileStatus,
          follow_up_executive_id: formData.followUpExecutive,
          product_id: formData.product,
          license_edition_id: formData.licenceEdition,
          address_book: formData.addressBook.map(entry => ({
            type: entry.type,
            contact_person: entry.contactPerson,
            mobile_numbers: entry.mobileNumbers.filter(num => num.trim() !== ''),
            phone: entry.phone,
            email: entry.email,
            is_mandatory: entry.isMandatory
          })),
          tss_status: formData.tssStatus,
          tss_expiry_date: formData.tssExpiryDate || null,
          admin_email: formData.adminEmail,
          amc_status: formData.amcStatus,
          amc_from_date: formData.amcFromDate || null,
          amc_to_date: formData.amcToDate || null,
          renewal_date: formData.renewalDate || null,
          no_of_visits: formData.noOfVisits ? parseInt(formData.noOfVisits) : null,
          current_amc_amount: formData.currentAmcAmount ? parseFloat(formData.currentAmcAmount) : null,
          last_year_amc_amount: formData.lastYearAmcAmount ? parseFloat(formData.lastYearAmcAmount) : null,
          additional_services: formData.additionalServices,
          tdl_addons: formData.tdlAddons,
          tdl_addons_expiry_date: formData.tdlAddonsExpiryDate || null,
          whatsapp_telegram_group: formData.whatsappTelegramGroup,
          whatsapp_telegram_group_expiry_date: formData.whatsappTelegramGroupExpiryDate || null,
          auto_backup: formData.autoBackup,
          auto_backup_expiry_date: formData.autoBackupExpiryDate || null,
          cloud_user: formData.cloudUser,
          cloud_user_expiry_date: formData.cloudUserExpiryDate || null,
          mobile_app: formData.mobileApp,
          mobile_app_expiry_date: formData.mobileAppExpiryDate || null
        },

        // Status
        is_active: true
      };

      let response;
      if (isEdit) {
        console.log('Updating customer with ID:', id);
        response = await customerAPI.update(id, customerData);
      } else {
        console.log('Creating new customer');
        response = await customerAPI.create(customerData);
      }

      console.log('API response:', response);

      if (response.data?.success) {
        toast.success(isEdit ? 'Customer updated successfully' : 'Customer created successfully');
        navigate('/customers');
      } else {
        toast.error(response.data?.message || 'Failed to save customer');

        // Handle backend validation errors
        if (response.data?.errors) {
          setErrors(response.data.errors);
        }
      }
    } catch (error) {
      console.error('Error saving customer:', error);

      // Handle backend validation errors
      if (error.response?.data?.errors) {
        setErrors(error.response.data.errors);
        toast.error('Please fix the validation errors');
      } else {
        toast.error(error.response?.data?.message || 'Something went wrong. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/customers');
  };

  // Helper functions for service styling (to avoid dynamic Tailwind classes)
  const getServiceSelectedClasses = (color) => {
    const colorMap = {
      'blue': 'border-blue-400 bg-gradient-to-br from-blue-50 to-blue-100 shadow-lg',
      'green': 'border-green-400 bg-gradient-to-br from-green-50 to-green-100 shadow-lg',
      'purple': 'border-purple-400 bg-gradient-to-br from-purple-50 to-purple-100 shadow-lg',
      'orange': 'border-orange-400 bg-gradient-to-br from-orange-50 to-orange-100 shadow-lg',
      'teal': 'border-teal-400 bg-gradient-to-br from-teal-50 to-teal-100 shadow-lg',
      'indigo': 'border-indigo-400 bg-gradient-to-br from-indigo-50 to-indigo-100 shadow-lg'
    };
    return colorMap[color] || 'border-gray-400 bg-gradient-to-br from-gray-50 to-gray-100 shadow-lg';
  };

  const getServiceIconClasses = (color) => {
    const colorMap = {
      'blue': 'bg-blue-500 text-white',
      'green': 'bg-green-500 text-white',
      'purple': 'bg-purple-500 text-white',
      'orange': 'bg-orange-500 text-white',
      'teal': 'bg-teal-500 text-white',
      'indigo': 'bg-indigo-500 text-white'
    };
    return colorMap[color] || 'bg-gray-500 text-white';
  };

  const getServiceTextClasses = (color) => {
    const colorMap = {
      'blue': 'text-blue-800',
      'green': 'text-green-800',
      'purple': 'text-purple-800',
      'orange': 'text-orange-800',
      'teal': 'text-teal-800',
      'indigo': 'text-indigo-800'
    };
    return colorMap[color] || 'text-gray-800';
  };

  const getServiceCheckClasses = (color) => {
    const colorMap = {
      'blue': 'bg-blue-500',
      'green': 'bg-green-500',
      'purple': 'bg-purple-500',
      'orange': 'bg-orange-500',
      'teal': 'bg-teal-500',
      'indigo': 'bg-indigo-500'
    };
    return colorMap[color] || 'bg-gray-500';
  };

  if (loading && isEdit) {
    return (
      <div className="flex justify-center items-center" style={{ height: '400px' }}>
        <div className="animate-spin rounded-full border-2 border-gray-300 border-t-current text-primary-600" role="status">
          <span className="sr-only">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100">
      <div className="w-full">
        {/* Header */}
        <div className="mb-8">
          <div className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 rounded-2xl shadow-xl p-6 text-white">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-3xl font-bold mb-2 flex items-center">
                  <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-4">
                    <FaBuilding className="text-white text-xl" />
                  </div>
                  {isEdit ? 'Edit Customer' : 'New Customer'}
                </h2>
                <p className="text-blue-100 text-lg">
                  {isEdit ? 'Update customer information and details' : 'Add a new customer to your CRM system'}
                </p>
              </div>
              <div className="flex gap-3">
                <button
                  type="button"
                  className="inline-flex items-center px-6 py-3 border-2 border-white border-opacity-30 text-sm font-medium rounded-xl text-white bg-white bg-opacity-10 hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-all duration-200 backdrop-blur-sm"
                  onClick={handleCancel}
                >
                  <FaTimes className="mr-2 h-4 w-4" />
                  Cancel
                </button>
                <button
                  type="submit"
                  form="customerForm"
                  className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-xl text-blue-600 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-all duration-200 shadow-lg"
                  disabled={loading}
                  onClick={() => console.log('Submit button clicked!')}
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2"></div>
                      {isEdit ? 'Updating...' : 'Creating...'}
                    </>
                  ) : (
                    <>
                      <FaSave className="mr-2 h-4 w-4" />
                      {isEdit ? 'Update Customer' : 'Create Customer'}
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Form */}
        <form id="customerForm" onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Information */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden border border-purple-100">
            <div className="bg-gradient-to-r from-purple-500 to-indigo-500 px-6 py-4">
              <h3 className="text-xl font-bold text-white flex items-center">
                <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                  <FaBuilding className="h-4 w-4 text-white" />
                </div>
                Basic Information
              </h3>
            </div>
            <div className="p-6 bg-gradient-to-br from-white to-purple-50">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Customer Name */}
                <div>
                  <label className="block text-sm font-bold text-purple-700 mb-2">Customer Name *</label>
                  <input
                    type="text"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.customerName
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-purple-200 focus:ring-purple-500 focus:border-purple-500 bg-white hover:border-purple-300'
                    }`}
                    name="customerName"
                    value={formData.customerName}
                    onChange={handleInputChange}
                    placeholder="Enter customer name"
                  />
                  {errors.customerName && <p className="mt-2 text-sm text-red-600 font-medium">{errors.customerName}</p>}
                </div>

                {/* Tally Serial No */}
                <div>
                  <label className="block text-sm font-bold text-blue-700 mb-2">
                    Tally Serial No *
                    <span className="text-xs text-blue-600 ml-2">(e.g., GS001)</span>
                  </label>
                  <input
                    type="text"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.tallySerialNo
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-blue-200 focus:ring-blue-500 focus:border-blue-500 bg-white hover:border-blue-300'
                    }`}
                    name="tallySerialNo"
                    value={formData.tallySerialNo}
                    onChange={handleInputChange}
                    placeholder="GS001"
                  />
                  {errors.tallySerialNo && <p className="mt-2 text-sm text-red-600 font-medium">{errors.tallySerialNo}</p>}
                </div>

                {/* Product */}
                <div>
                  <label className="block text-sm font-bold text-green-700 mb-2">
                    Product
                    <span className="text-xs text-green-600 ml-2">
                      (Type to search)
                    </span>
                  </label>
                  <SearchableSelect
                    options={tallyProducts}
                    value={formData.product}
                    onChange={(productId) => setFormData(prev => ({ ...prev, product: productId }))}
                    placeholder="Search products..."
                    searchFields={['name', 'description', 'category']}
                    displayField="name"
                    valueField="id"
                    className="w-full"
                    disabled={loadingTallyProducts}
                    minSearchLength={1}
                    maxResults={10}
                    allowClear={true}
                    noResultsText="No products found"
                    searchingText="Type to search products..."
                  />
                  {loadingTallyProducts && (
                    <p className="mt-2 text-sm text-green-600">Loading products...</p>
                  )}
                </div>

                {/* Licence Edition */}
                <div>
                  <label className="block text-sm font-bold text-orange-700 mb-2">
                    Licence Edition
                    <span className="text-xs text-orange-600 ml-2">
                      (Type to search)
                    </span>
                  </label>
                  <SearchableSelect
                    options={licenseEditions}
                    value={formData.licenceEdition}
                    onChange={(editionId) => setFormData(prev => ({ ...prev, licenceEdition: editionId }))}
                    placeholder="Search license editions..."
                    searchFields={['name', 'description', 'version']}
                    displayField="name"
                    valueField="id"
                    className="w-full"
                    disabled={loadingLicenseEditions}
                    minSearchLength={1}
                    maxResults={10}
                    allowClear={true}
                    noResultsText="No license editions found"
                    searchingText="Type to search editions..."
                  />
                  {loadingLicenseEditions && (
                    <p className="mt-2 text-sm text-orange-600">Loading license editions...</p>
                  )}
                </div>

                {/* Location */}
                <div>
                  <label className="block text-sm font-bold text-teal-700 mb-2">
                    Location
                    <span className="text-xs text-teal-600 ml-2">
                      (Type to search)
                    </span>
                  </label>
                  <SearchableSelect
                    options={areas}
                    value={formData.location}
                    onChange={(areaId) => setFormData(prev => ({ ...prev, location: areaId }))}
                    placeholder="Search locations..."
                    searchFields={['name', 'city', 'state', 'description']}
                    displayField="name"
                    valueField="id"
                    className="w-full"
                    disabled={loadingAreas}
                    minSearchLength={1}
                    maxResults={10}
                    allowClear={true}
                    noResultsText="No locations found"
                    searchingText="Type to search locations..."
                  />
                  {loadingAreas && (
                    <p className="mt-2 text-sm text-teal-600">Loading locations...</p>
                  )}
                </div>

                {/* Profile Status */}
                <div>
                  <label className="block text-sm font-bold text-indigo-700 mb-2">Profile Status</label>
                  <select
                    className="block w-full px-4 py-3 border-2 border-indigo-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white hover:border-indigo-300 transition-all duration-200 sm:text-sm"
                    name="profileStatus"
                    value={formData.profileStatus}
                    onChange={handleInputChange}
                  >
                    <option value="FOLLOW UP">📞 FOLLOW UP</option>
                    <option value="OTHERS">📋 OTHERS</option>
                  </select>
                </div>

                {/* Follow-up Executive */}
                <div>
                  <label className="block text-sm font-bold text-pink-700 mb-2">
                    Follow-up Executive
                    <span className="text-xs text-pink-600 ml-2">
                      (Type to search)
                    </span>
                  </label>
                  <SearchableSelect
                    options={executives}
                    value={formData.followUpExecutive}
                    onChange={(executiveId) => setFormData(prev => ({ ...prev, followUpExecutive: executiveId }))}
                    placeholder="Search executives..."
                    searchFields={['first_name', 'last_name', 'email', 'employee_code']}
                    displayField="first_name"
                    valueField="id"
                    className="w-full"
                    disabled={loadingExecutives}
                    minSearchLength={1}
                    maxResults={10}
                    allowClear={true}
                    noResultsText="No executives found"
                    searchingText="Type to search executives..."
                  />
                  {loadingExecutives && (
                    <p className="mt-2 text-sm text-pink-600">Loading executives...</p>
                  )}
                </div>

                {/* GST No */}
                <div>
                  <label className="block text-sm font-bold text-red-700 mb-2">GST No</label>
                  <input
                    type="text"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.gstNo
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-red-200 focus:ring-red-500 focus:border-red-500 bg-white hover:border-red-300'
                    }`}
                    name="gstNo"
                    value={formData.gstNo}
                    onChange={handleInputChange}
                    placeholder="27AABCU9603R1ZX"
                    maxLength="15"
                  />
                  {errors.gstNo && <p className="mt-2 text-sm text-red-600 font-medium">{errors.gstNo}</p>}
                </div>


              </div>

              {/* Map Location Section */}
              <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl border border-blue-200">
                <h4 className="text-lg font-bold text-blue-700 mb-4 flex items-center">
                  <FaMap className="mr-2" />
                  Map Location
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="md:col-span-1">
                    <label className="block text-sm font-bold text-blue-700 mb-2">Map Location</label>
                    <input
                      type="text"
                      className="block w-full px-4 py-3 border-2 border-blue-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white hover:border-blue-300 transition-all duration-200 sm:text-sm"
                      name="mapLocation"
                      value={formData.mapLocation}
                      onChange={handleInputChange}
                      placeholder="Click to select location"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-bold text-green-700 mb-2">Latitude</label>
                    <input
                      type="number"
                      step="any"
                      className="block w-full px-4 py-3 border-2 border-green-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white hover:border-green-300 transition-all duration-200 sm:text-sm"
                      name="latitude"
                      value={formData.latitude}
                      onChange={handleInputChange}
                      placeholder="Auto-filled"
                      readOnly
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-bold text-orange-700 mb-2">Longitude</label>
                    <input
                      type="number"
                      step="any"
                      className="block w-full px-4 py-3 border-2 border-orange-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white hover:border-orange-300 transition-all duration-200 sm:text-sm"
                      name="longitude"
                      value={formData.longitude}
                      onChange={handleInputChange}
                      placeholder="Auto-filled"
                      readOnly
                    />
                  </div>
                </div>
              </div>

              {/* Remarks */}
              <div className="mt-6">
                <label className="block text-sm font-bold text-gray-700 mb-2">Remarks</label>
                <textarea
                  className="block w-full px-4 py-3 border-2 border-gray-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-gray-500 bg-white hover:border-gray-300 transition-all duration-200"
                  name="remarks"
                  value={formData.remarks}
                  onChange={handleInputChange}
                  rows="3"
                  placeholder="Enter any remarks"
                ></textarea>
              </div>
            </div>
          </div>

          {/* Customer Address Book */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden border border-emerald-100">
            <div className="bg-gradient-to-r from-emerald-500 to-green-500 px-6 py-4">
              <h3 className="text-xl font-bold text-white flex items-center">
                <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                  <FaUser className="h-4 w-4 text-white" />
                </div>
                Customer Address Book
                <span className="ml-2 text-sm bg-white bg-opacity-20 px-2 py-1 rounded-full">
                  Owner contact is mandatory
                </span>
              </h3>
            </div>
            <div className="p-6 bg-gradient-to-br from-white to-emerald-50">
              {formData.addressBook.map((entry, index) => (
                <div key={index} className="mb-6 p-6 bg-gradient-to-r from-white to-emerald-50 rounded-xl border-2 border-emerald-200 shadow-sm">
                  <div className="flex justify-between items-center mb-4">
                    <h4 className="text-lg font-bold text-emerald-700">Contact #{index + 1}</h4>
                    {formData.addressBook.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeAddressBookEntry(index)}
                        className="text-red-600 hover:text-red-800 transition-colors duration-200"
                      >
                        <FaMinus className="h-5 w-5" />
                      </button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {/* Type */}
                    <div>
                      <label className="block text-sm font-bold text-emerald-700 mb-2">
                        Type *
                        {designationOptions.find(d => d.value === entry.type)?.isMandatory && (
                          <span className="text-red-600 ml-1">(Mandatory)</span>
                        )}
                      </label>
                      <select
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors[`addressBook_${index}_type`]
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-emerald-200 focus:ring-emerald-500 focus:border-emerald-500 bg-white hover:border-emerald-300'
                        }`}
                        value={entry.type}
                        onChange={(e) => handleAddressBookChange(index, 'type', e.target.value)}
                      >
                        <option value="">Select type</option>
                        {designationOptions.map(option => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                      {errors[`addressBook_${index}_type`] && (
                        <p className="mt-2 text-sm text-red-600 font-medium">{errors[`addressBook_${index}_type`]}</p>
                      )}
                    </div>

                    {/* Contact Person */}
                    <div>
                      <label className="block text-sm font-bold text-blue-700 mb-2">Contact Person *</label>
                      <input
                        type="text"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors[`addressBook_${index}_contactPerson`]
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-blue-200 focus:ring-blue-500 focus:border-blue-500 bg-white hover:border-blue-300'
                        }`}
                        value={entry.contactPerson}
                        onChange={(e) => handleAddressBookChange(index, 'contactPerson', e.target.value)}
                        placeholder="Enter contact person name"
                      />
                      {errors[`addressBook_${index}_contactPerson`] && (
                        <p className="mt-2 text-sm text-red-600 font-medium">{errors[`addressBook_${index}_contactPerson`]}</p>
                      )}
                    </div>

                    {/* Phone */}
                    <div>
                      <label className="block text-sm font-bold text-purple-700 mb-2">Phone</label>
                      <input
                        type="tel"
                        className="block w-full px-4 py-3 border-2 border-purple-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white hover:border-purple-300 transition-all duration-200 sm:text-sm"
                        value={entry.phone}
                        onChange={(e) => handleAddressBookChange(index, 'phone', e.target.value)}
                        placeholder="Enter phone number"
                      />
                    </div>

                    {/* Email */}
                    <div className="md:col-span-2">
                      <label className="block text-sm font-bold text-indigo-700 mb-2">Email *</label>
                      <input
                        type="email"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors[`addressBook_${index}_email`]
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-indigo-200 focus:ring-indigo-500 focus:border-indigo-500 bg-white hover:border-indigo-300'
                        }`}
                        value={entry.email}
                        onChange={(e) => handleAddressBookChange(index, 'email', e.target.value)}
                        placeholder="<EMAIL>"
                      />
                      {errors[`addressBook_${index}_email`] && (
                        <p className="mt-2 text-sm text-red-600 font-medium">{errors[`addressBook_${index}_email`]}</p>
                      )}
                    </div>
                  </div>

                  {/* Mobile Numbers */}
                  <div className="mt-4">
                    <label className="block text-sm font-bold text-green-700 mb-2">Mobile Numbers *</label>
                    {entry.mobileNumbers.map((mobile, mobileIndex) => (
                      <div key={mobileIndex} className="flex gap-2 mb-2">
                        <input
                          type="tel"
                          className={`flex-1 px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                            errors[`addressBook_${index}_mobile`]
                              ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                              : 'border-green-200 focus:ring-green-500 focus:border-green-500 bg-white hover:border-green-300'
                          }`}
                          value={mobile}
                          onChange={(e) => handleMobileNumberChange(index, mobileIndex, e.target.value)}
                          placeholder="Enter mobile number"
                        />
                        {entry.mobileNumbers.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeMobileNumber(index, mobileIndex)}
                            className="px-3 py-2 bg-red-500 text-white rounded-xl hover:bg-red-600 transition-colors duration-200"
                          >
                            <FaMinus className="h-4 w-4" />
                          </button>
                        )}
                        {mobileIndex === entry.mobileNumbers.length - 1 && (
                          <button
                            type="button"
                            onClick={() => addMobileNumber(index)}
                            className="px-3 py-2 bg-green-500 text-white rounded-xl hover:bg-green-600 transition-colors duration-200"
                          >
                            <FaPlus className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    ))}
                    {errors[`addressBook_${index}_mobile`] && (
                      <p className="mt-2 text-sm text-red-600 font-medium">{errors[`addressBook_${index}_mobile`]}</p>
                    )}
                  </div>
                </div>
              ))}

              {/* Add More Address Book Entry */}
              <div className="text-center">
                <button
                  type="button"
                  onClick={addAddressBookEntry}
                  className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-emerald-500 to-green-500 text-white font-bold rounded-xl hover:from-emerald-600 hover:to-green-600 transition-all duration-200 shadow-lg"
                >
                  <FaPlus className="mr-2 h-4 w-4" />
                  Add More Contact
                </button>
              </div>

              {errors.addressBook && (
                <p className="mt-4 text-sm text-red-600 font-medium text-center">{errors.addressBook}</p>
              )}
            </div>
          </div>

          {/* TSS Status Section */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden border border-green-100">
            <div className="bg-gradient-to-r from-green-500 to-emerald-500 px-6 py-4">
              <h3 className="text-xl font-bold text-white flex items-center">
                <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                  <FaShieldAlt className="h-4 w-4 text-white" />
                </div>
                Tally Software Service (TSS)
              </h3>
            </div>
            <div className="p-6 bg-gradient-to-br from-white to-green-50">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* TSS Status */}
                <div>
                  <label className="block text-sm font-bold text-green-700 mb-2">TSS Status</label>
                  <select
                    className="block w-full px-4 py-3 border-2 border-green-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white hover:border-green-300 transition-all duration-200 sm:text-sm"
                    name="tssStatus"
                    value={formData.tssStatus}
                    onChange={handleInputChange}
                  >
                    <option value="YES">✅ YES</option>
                    <option value="NO">❌ NO</option>
                  </select>
                </div>

                {/* Conditional fields based on TSS Status */}
                {formData.tssStatus === 'YES' ? (
                  <>
                    {/* TSS Expiry Date */}
                    <div>
                      <label className="block text-sm font-bold text-green-700 mb-2">TSS Expiry Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors.tssExpiryDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-green-200 focus:ring-green-500 focus:border-green-500 bg-white hover:border-green-300'
                        }`}
                        name="tssExpiryDate"
                        value={formData.tssExpiryDate}
                        onChange={handleInputChange}
                      />
                      {errors.tssExpiryDate && <p className="mt-2 text-sm text-red-600 font-medium">{errors.tssExpiryDate}</p>}
                    </div>

                    {/* Admin Email */}
                    <div>
                      <label className="block text-sm font-bold text-green-700 mb-2">Admin Email *</label>
                      <input
                        type="email"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors.adminEmail
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-green-200 focus:ring-green-500 focus:border-green-500 bg-white hover:border-green-300'
                        }`}
                        name="adminEmail"
                        value={formData.adminEmail}
                        onChange={handleInputChange}
                        placeholder="<EMAIL>"
                      />
                      {errors.adminEmail && <p className="mt-2 text-sm text-red-600 font-medium">{errors.adminEmail}</p>}
                    </div>
                  </>
                ) : null}
              </div>
            </div>
          </div>

          {/* AMC Section */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden border border-orange-100">
            <div className="bg-gradient-to-r from-orange-500 to-red-500 px-6 py-4">
              <h3 className="text-xl font-bold text-white flex items-center">
                <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                  <FaCalendar className="h-4 w-4 text-white" />
                </div>
                Annual Maintenance Contract (AMC)
              </h3>
            </div>
            <div className="p-6 bg-gradient-to-br from-white to-orange-50">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* AMC Status */}
                <div>
                  <label className="block text-sm font-bold text-orange-700 mb-2">AMC Status</label>
                  <select
                    className="block w-full px-4 py-3 border-2 border-orange-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white hover:border-orange-300 transition-all duration-200 sm:text-sm"
                    name="amcStatus"
                    value={formData.amcStatus}
                    onChange={handleInputChange}
                  >
                    <option value="YES">✅ YES</option>
                    <option value="NO">❌ NO</option>
                  </select>
                </div>

                {/* Conditional fields based on AMC Status */}
                {formData.amcStatus === 'YES' && (
                  <>
                    {/* AMC From Date */}
                    <div>
                      <label className="block text-sm font-bold text-orange-700 mb-2">AMC From Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors.amcFromDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-orange-200 focus:ring-orange-500 focus:border-orange-500 bg-white hover:border-orange-300'
                        }`}
                        name="amcFromDate"
                        value={formData.amcFromDate}
                        onChange={handleInputChange}
                      />
                      {errors.amcFromDate && <p className="mt-2 text-sm text-red-600 font-medium">{errors.amcFromDate}</p>}
                    </div>

                    {/* AMC To Date */}
                    <div>
                      <label className="block text-sm font-bold text-orange-700 mb-2">AMC To Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors.amcToDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-orange-200 focus:ring-orange-500 focus:border-orange-500 bg-white hover:border-orange-300'
                        }`}
                        name="amcToDate"
                        value={formData.amcToDate}
                        onChange={handleInputChange}
                      />
                      {errors.amcToDate && <p className="mt-2 text-sm text-red-600 font-medium">{errors.amcToDate}</p>}
                    </div>

                    {/* Renewal Date */}
                    <div>
                      <label className="block text-sm font-bold text-orange-700 mb-2">Renewal Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors.renewalDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-orange-200 focus:ring-orange-500 focus:border-orange-500 bg-white hover:border-orange-300'
                        }`}
                        name="renewalDate"
                        value={formData.renewalDate}
                        onChange={handleInputChange}
                      />
                      {errors.renewalDate && <p className="mt-2 text-sm text-red-600 font-medium">{errors.renewalDate}</p>}
                    </div>

                    {/* No of Visits */}
                    <div>
                      <label className="block text-sm font-bold text-orange-700 mb-2">No of Visits *</label>
                      <input
                        type="number"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors.noOfVisits
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-orange-200 focus:ring-orange-500 focus:border-orange-500 bg-white hover:border-orange-300'
                        }`}
                        name="noOfVisits"
                        value={formData.noOfVisits}
                        onChange={handleInputChange}
                        placeholder="Enter number of visits"
                        min="1"
                      />
                      {errors.noOfVisits && <p className="mt-2 text-sm text-red-600 font-medium">{errors.noOfVisits}</p>}
                    </div>

                    {/* Current AMC Amount */}
                    <div>
                      <label className="block text-sm font-bold text-orange-700 mb-2">Current AMC Amount (₹) *</label>
                      <input
                        type="number"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors.currentAmcAmount
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-orange-200 focus:ring-orange-500 focus:border-orange-500 bg-white hover:border-orange-300'
                        }`}
                        name="currentAmcAmount"
                        value={formData.currentAmcAmount}
                        onChange={handleInputChange}
                        placeholder="Enter current AMC amount"
                        min="0"
                        step="0.01"
                      />
                      {errors.currentAmcAmount && <p className="mt-2 text-sm text-red-600 font-medium">{errors.currentAmcAmount}</p>}
                    </div>

                    {/* Last Year AMC Amount */}
                    <div>
                      <label className="block text-sm font-bold text-orange-700 mb-2">Last Year AMC Amount (₹)</label>
                      <input
                        type="number"
                        className="block w-full px-4 py-3 border-2 border-orange-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white hover:border-orange-300 transition-all duration-200 sm:text-sm"
                        name="lastYearAmcAmount"
                        value={formData.lastYearAmcAmount}
                        onChange={handleInputChange}
                        placeholder="Enter last year AMC amount"
                        min="0"
                        step="0.01"
                      />
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Additional Services */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden border border-indigo-100">
            <div className="bg-gradient-to-r from-indigo-500 to-purple-500 px-6 py-4">
              <h3 className="text-xl font-bold text-white flex items-center">
                <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                  <FaTools className="h-4 w-4 text-white" />
                </div>
                Additional Services
              </h3>
            </div>
            <div className="p-6 bg-gradient-to-br from-white to-indigo-50">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {additionalServicesOptions.map((service) => {
                  const isSelected = formData.additionalServices.includes(service.id);
                  return (
                    <div
                      key={service.id}
                      className={`relative p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
                        isSelected
                          ? getServiceSelectedClasses(service.color)
                          : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-md'
                      }`}
                      onClick={() => handleAdditionalServiceToggle(service.id)}
                    >
                      <div className="flex items-center space-x-3">
                        <div className={`w-10 h-10 rounded-full flex items-center justify-center text-lg ${
                          isSelected ? getServiceIconClasses(service.color) : 'bg-gray-100 text-gray-600'
                        }`}>
                          {service.icon}
                        </div>
                        <div className="flex-1">
                          <h4 className={`font-bold text-sm ${
                            isSelected ? getServiceTextClasses(service.color) : 'text-gray-700'
                          }`}>
                            {service.name}
                          </h4>
                        </div>
                        {isSelected && (
                          <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                            getServiceCheckClasses(service.color)
                          }`}>
                            <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Additional Features */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden border border-cyan-100">
            <div className="bg-gradient-to-r from-cyan-500 to-blue-500 px-6 py-4">
              <h3 className="text-xl font-bold text-white flex items-center">
                <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                  <FaClipboardList className="h-4 w-4 text-white" />
                </div>
                Additional Features
              </h3>
            </div>
            <div className="p-6 bg-gradient-to-br from-white to-cyan-50">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-6">
                {/* TDL & Addons */}
                <div className="p-4 bg-gradient-to-r from-yellow-50 to-amber-50 rounded-xl border border-yellow-200">
                  <div className="flex items-center space-x-3 mb-3">
                    <input
                      type="checkbox"
                      id="tdlAddons"
                      name="tdlAddons"
                      checked={formData.tdlAddons}
                      onChange={handleInputChange}
                      className="w-5 h-5 text-yellow-600 bg-gray-100 border-gray-300 rounded focus:ring-yellow-500 focus:ring-2"
                    />
                    <label htmlFor="tdlAddons" className="text-sm font-bold text-yellow-700 cursor-pointer">
                      🔧 TDL & Addons
                    </label>
                  </div>
                  {formData.tdlAddons && (
                    <div>
                      <label className="block text-xs font-bold text-yellow-600 mb-1">Expiry Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-3 py-2 border-2 rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 text-xs ${
                          errors.tdlAddonsExpiryDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-yellow-200 focus:ring-yellow-500 focus:border-yellow-500 bg-white hover:border-yellow-300'
                        }`}
                        name="tdlAddonsExpiryDate"
                        value={formData.tdlAddonsExpiryDate}
                        onChange={handleInputChange}
                      />
                      {errors.tdlAddonsExpiryDate && <p className="mt-1 text-xs text-red-600 font-medium">{errors.tdlAddonsExpiryDate}</p>}
                    </div>
                  )}
                </div>

                {/* WhatsApp/Telegram Group */}
                <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-200">
                  <div className="flex items-center space-x-3 mb-3">
                    <input
                      type="checkbox"
                      id="whatsappTelegramGroup"
                      name="whatsappTelegramGroup"
                      checked={formData.whatsappTelegramGroup}
                      onChange={handleInputChange}
                      className="w-5 h-5 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2"
                    />
                    <label htmlFor="whatsappTelegramGroup" className="text-sm font-bold text-green-700 cursor-pointer">
                      📱 WhatsApp/Telegram Group
                    </label>
                  </div>
                  {formData.whatsappTelegramGroup && (
                    <div>
                      <label className="block text-xs font-bold text-green-600 mb-1">Expiry Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-3 py-2 border-2 rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 text-xs ${
                          errors.whatsappTelegramGroupExpiryDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-green-200 focus:ring-green-500 focus:border-green-500 bg-white hover:border-green-300'
                        }`}
                        name="whatsappTelegramGroupExpiryDate"
                        value={formData.whatsappTelegramGroupExpiryDate}
                        onChange={handleInputChange}
                      />
                      {errors.whatsappTelegramGroupExpiryDate && <p className="mt-1 text-xs text-red-600 font-medium">{errors.whatsappTelegramGroupExpiryDate}</p>}
                    </div>
                  )}
                </div>

                {/* Auto Backup */}
                <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200">
                  <div className="flex items-center space-x-3 mb-3">
                    <input
                      type="checkbox"
                      id="autoBackup"
                      name="autoBackup"
                      checked={formData.autoBackup}
                      onChange={handleInputChange}
                      className="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                    />
                    <label htmlFor="autoBackup" className="text-sm font-bold text-blue-700 cursor-pointer">
                      💾 Auto Backup
                    </label>
                  </div>
                  {formData.autoBackup && (
                    <div>
                      <label className="block text-xs font-bold text-blue-600 mb-1">Expiry Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-3 py-2 border-2 rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 text-xs ${
                          errors.autoBackupExpiryDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-blue-200 focus:ring-blue-500 focus:border-blue-500 bg-white hover:border-blue-300'
                        }`}
                        name="autoBackupExpiryDate"
                        value={formData.autoBackupExpiryDate}
                        onChange={handleInputChange}
                      />
                      {errors.autoBackupExpiryDate && <p className="mt-1 text-xs text-red-600 font-medium">{errors.autoBackupExpiryDate}</p>}
                    </div>
                  )}
                </div>

                {/* Cloud User */}
                <div className="p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl border border-purple-200">
                  <div className="flex items-center space-x-3 mb-3">
                    <input
                      type="checkbox"
                      id="cloudUser"
                      name="cloudUser"
                      checked={formData.cloudUser}
                      onChange={handleInputChange}
                      className="w-5 h-5 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 focus:ring-2"
                    />
                    <label htmlFor="cloudUser" className="text-sm font-bold text-purple-700 cursor-pointer">
                      ☁️ Cloud User
                    </label>
                  </div>
                  {formData.cloudUser && (
                    <div>
                      <label className="block text-xs font-bold text-purple-600 mb-1">Expiry Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-3 py-2 border-2 rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 text-xs ${
                          errors.cloudUserExpiryDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-purple-200 focus:ring-purple-500 focus:border-purple-500 bg-white hover:border-purple-300'
                        }`}
                        name="cloudUserExpiryDate"
                        value={formData.cloudUserExpiryDate}
                        onChange={handleInputChange}
                      />
                      {errors.cloudUserExpiryDate && <p className="mt-1 text-xs text-red-600 font-medium">{errors.cloudUserExpiryDate}</p>}
                    </div>
                  )}
                </div>

                {/* Mobile App */}
                <div className="p-4 bg-gradient-to-r from-orange-50 to-red-50 rounded-xl border border-orange-200">
                  <div className="flex items-center space-x-3 mb-3">
                    <input
                      type="checkbox"
                      id="mobileApp"
                      name="mobileApp"
                      checked={formData.mobileApp}
                      onChange={handleInputChange}
                      className="w-5 h-5 text-orange-600 bg-gray-100 border-gray-300 rounded focus:ring-orange-500 focus:ring-2"
                    />
                    <label htmlFor="mobileApp" className="text-sm font-bold text-orange-700 cursor-pointer">
                      📱 Mobile App
                    </label>
                  </div>
                  {formData.mobileApp && (
                    <div>
                      <label className="block text-xs font-bold text-orange-600 mb-1">Expiry Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-3 py-2 border-2 rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 text-xs ${
                          errors.mobileAppExpiryDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-orange-200 focus:ring-orange-500 focus:border-orange-500 bg-white hover:border-orange-300'
                        }`}
                        name="mobileAppExpiryDate"
                        value={formData.mobileAppExpiryDate}
                        onChange={handleInputChange}
                      />
                      {errors.mobileAppExpiryDate && <p className="mt-1 text-xs text-red-600 font-medium">{errors.mobileAppExpiryDate}</p>}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CustomerFormValidated;
