import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  await queryInterface.createTable('executives', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    employee_code: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    first_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    last_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    alternate_phone: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    designation_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'designations',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    staff_role_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'staff_roles',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    department: {
      type: DataTypes.ENUM('sales', 'technical', 'support', 'management', 'accounts', 'hr', 'marketing'),
      allowNull: false,
      defaultValue: 'support',
    },
    date_of_joining: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    date_of_birth: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    address: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    city: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    state: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    postal_code: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    emergency_contact_name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    emergency_contact_phone: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    salary: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
    },
    commission_rate: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    target_amount: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: true,
    },
    skills: {
      type: DataTypes.JSONB,
      defaultValue: [],
    },
    areas_covered: {
      type: DataTypes.JSONB,
      defaultValue: [],
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    profile_image: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes
  await queryInterface.addIndex('executives', ['tenant_id']);
  await queryInterface.addIndex('executives', ['tenant_id', 'employee_code'], { unique: true });
  await queryInterface.addIndex('executives', ['user_id']);
  await queryInterface.addIndex('executives', ['department']);
  await queryInterface.addIndex('executives', ['is_active']);
  await queryInterface.addIndex('executives', ['email']);
  await queryInterface.addIndex('executives', ['phone']);
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('executives');
};
