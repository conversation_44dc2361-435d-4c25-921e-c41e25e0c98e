# SearchableSelect Component

A powerful, accessible, and customizable searchable select component built with React and Tailwind CSS.

## Features

- 🔍 **Multi-field Search**: Search across multiple fields (name, description, category, etc.)
- ⌨️ **Keyboard Navigation**: Full keyboard support with arrow keys, Enter, and Escape
- 🎨 **Customizable Rendering**: Custom option and selected item rendering
- 📱 **Mobile Friendly**: Touch-friendly interface with responsive design
- ♿ **Accessible**: ARIA compliant with proper focus management
- 🚀 **Performance**: Optimized with debounced search and result limiting
- 🎯 **Flexible**: Configurable search behavior and appearance

## Basic Usage

```jsx
import { SearchableSelect } from '../components/ui';

const MyComponent = () => {
  const [selectedValue, setSelectedValue] = useState(null);
  
  const options = [
    { id: 1, name: 'Manufacturing', description: 'Manufacturing companies', category: 'Production' },
    { id: 2, name: 'Trading', description: 'Trading companies', category: 'Commerce' },
    // ... more options
  ];

  return (
    <SearchableSelect
      options={options}
      value={selectedValue}
      onChange={setSelectedValue}
      placeholder="Search industries..."
      searchFields={['name', 'description', 'category']}
    />
  );
};
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `options` | Array | `[]` | Array of option objects |
| `value` | Any | `undefined` | Currently selected value |
| `onChange` | Function | `undefined` | Callback when selection changes |
| `placeholder` | String | `"Search..."` | Input placeholder text |
| `searchFields` | Array | `['name']` | Fields to search in |
| `displayField` | String | `'name'` | Field to display in input |
| `valueField` | String | `'id'` | Field to use as value |
| `className` | String | `''` | Additional CSS classes |
| `error` | Boolean | `false` | Show error state |
| `disabled` | Boolean | `false` | Disable the component |
| `minSearchLength` | Number | `2` | Minimum characters to start search |
| `maxResults` | Number | `10` | Maximum results to show |
| `allowClear` | Boolean | `true` | Show clear button |
| `noResultsText` | String | `"No results found"` | Text when no results |
| `searchingText` | String | `"Type to search..."` | Text before min length |

## Advanced Usage

### Custom Option Rendering

```jsx
<SearchableSelect
  options={industries}
  value={selectedIndustry}
  onChange={setSelectedIndustry}
  renderOption={(option, isHighlighted) => (
    <div className={`px-4 py-3 ${isHighlighted ? 'bg-blue-50' : ''}`}>
      <div className="font-medium">{option.name}</div>
      <div className="text-sm text-gray-500">{option.description}</div>
      {option.category && (
        <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mt-1">
          {option.category}
        </span>
      )}
    </div>
  )}
/>
```

### Custom Selected Item Rendering

```jsx
<SearchableSelect
  options={options}
  value={selectedValue}
  onChange={setSelectedValue}
  renderSelected={(option) => (
    <div className="flex items-center">
      <span className="mr-2">{option.icon}</span>
      <span>{option.name}</span>
    </div>
  )}
/>
```

## Keyboard Navigation

- **Arrow Down**: Open dropdown or move to next option
- **Arrow Up**: Move to previous option
- **Enter**: Select highlighted option or open dropdown
- **Escape**: Close dropdown and clear search
- **Tab**: Navigate away from component

## Styling

The component uses Tailwind CSS classes and can be customized by:

1. **Passing custom className**: Add additional classes to the container
2. **Custom rendering**: Use `renderOption` and `renderSelected` props
3. **Error states**: The `error` prop applies error styling
4. **Disabled states**: The `disabled` prop applies disabled styling

## Examples

### Industry Selection (Current Implementation)
```jsx
<SearchableSelect
  options={industries}
  value={formData.industry_id}
  onChange={(value) => handleChange('industry_id', value)}
  placeholder="Search industries..."
  searchFields={['name', 'description', 'category']}
  displayField="name"
  valueField="id"
  error={!!errors.industry_id}
  minSearchLength={2}
  maxResults={8}
  noResultsText="No industries found"
  searchingText="Type 2+ letters to search industries..."
/>
```

### Customer Selection
```jsx
<SearchableSelect
  options={customers}
  value={selectedCustomer}
  onChange={setSelectedCustomer}
  placeholder="Search customers..."
  searchFields={['company_name', 'contact_person', 'email']}
  displayField="company_name"
  valueField="id"
  renderOption={(customer, isHighlighted) => (
    <div className={`px-4 py-3 ${isHighlighted ? 'bg-indigo-50' : ''}`}>
      <div className="font-medium">{customer.company_name}</div>
      <div className="text-sm text-gray-500">{customer.contact_person}</div>
      <div className="text-xs text-gray-400">{customer.email}</div>
    </div>
  )}
/>
```

## Accessibility

The component follows ARIA guidelines:
- Proper focus management
- Screen reader support
- Keyboard navigation
- Role and state attributes

## Performance Considerations

- Search is performed client-side for small datasets
- Results are limited by `maxResults` prop
- Consider server-side search for large datasets
- Component re-renders are optimized with proper key usage

## Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome Mobile)
- Keyboard and screen reader accessible
