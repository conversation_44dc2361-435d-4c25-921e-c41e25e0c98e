import express from 'express';
import { body, query, param } from 'express-validator';
import { validateRequest } from '../middleware/validation.js';
import { authenticateToken, requirePermission, requireTenantAccess } from '../middleware/auth.js';
import { withUsageTracking, trackApiUsage } from '../middleware/usageTracking.js';
import {
  getCustomers,
  getCustomerById,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  getCustomerStats,
} from '../controllers/customerController.js';

const router = express.Router();

// Apply authentication and tenant access to all routes
router.use(authenticateToken);
router.use(requireTenantAccess);
// Track API usage for all routes
router.use(trackApiUsage());

/**
 * @route   GET /api/customers
 * @desc    Get all customers with pagination and filters
 * @access  Private (requires customers.read permission)
 */
router.get('/', [
  requirePermission('customers.read'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('search')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters'),
  query('customerType')
    .optional()
    .isIn(['prospect', 'customer', 'inactive', 'blacklisted'])
    .withMessage('Invalid customer type'),
  query('industryId')
    .optional()
    .isUUID()
    .withMessage('Industry ID must be a valid UUID'),
  query('areaId')
    .optional()
    .isUUID()
    .withMessage('Area ID must be a valid UUID'),
  query('assignedExecutiveId')
    .optional()
    .isUUID()
    .withMessage('Assigned executive ID must be a valid UUID'),
  query('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  query('sortBy')
    .optional()
    .isIn(['created_at', 'updated_at', 'company_name', 'customer_code'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['ASC', 'DESC'])
    .withMessage('Sort order must be ASC or DESC'),
  validateRequest,
], getCustomers);

/**
 * @route   GET /api/customers/stats
 * @desc    Get customer statistics
 * @access  Private (requires customers.read permission)
 */
router.get('/stats', [
  requirePermission('customers.read'),
  query('dateFrom')
    .optional()
    .isISO8601()
    .withMessage('Date from must be a valid date'),
  query('dateTo')
    .optional()
    .isISO8601()
    .withMessage('Date to must be a valid date'),
  query('customerType')
    .optional()
    .isIn(['prospect', 'customer', 'inactive', 'blacklisted'])
    .withMessage('Invalid customer type'),
  query('industryId')
    .optional()
    .isUUID()
    .withMessage('Industry ID must be a valid UUID'),
  query('areaId')
    .optional()
    .isUUID()
    .withMessage('Area ID must be a valid UUID'),
  validateRequest,
], getCustomerStats);

/**
 * @route   GET /api/customers/:id
 * @desc    Get customer by ID
 * @access  Private (requires customers.read permission)
 */
router.get('/:id', [
  requirePermission('customers.read'),
  param('id')
    .notEmpty()
    .withMessage('Customer ID is required')
    .isUUID()
    .withMessage('Customer ID must be a valid UUID'),
  query('includeRelations')
    .optional()
    .isBoolean()
    .withMessage('includeRelations must be a boolean'),
  query('includeStats')
    .optional()
    .isBoolean()
    .withMessage('includeStats must be a boolean'),
  validateRequest,
], getCustomerById);

/**
 * @route   POST /api/customers
 * @desc    Create new customer
 * @access  Private (requires customers.create permission)
 */
router.post('/', [
  requirePermission('customers.create'),
  ...withUsageTracking('customers', { operation: 'create' }),
  body('company_name')
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Company name must be between 2 and 200 characters')
    .matches(/^[a-zA-Z0-9\s\-\.\&\(\)]+$/)
    .withMessage('Company name contains invalid characters'),
  body('customer_code')
    .optional()
    .trim()
    .isLength({ min: 2, max: 20 })
    .withMessage('Customer code must be between 2 and 20 characters')
    .matches(/^[A-Z0-9]+$/)
    .withMessage('Customer code must contain only uppercase letters and numbers'),
  body('display_name')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Display name must be between 1 and 100 characters'),
  body('customer_type')
    .optional()
    .isIn(['prospect', 'customer', 'inactive', 'blacklisted'])
    .withMessage('Invalid customer type'),
  body('business_type')
    .optional()
    .isIn(['individual', 'partnership', 'private_limited', 'public_limited', 'llp', 'proprietorship', 'trust', 'society', 'government', 'other'])
    .withMessage('Invalid business type'),
  body('contact_person')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Contact person must be between 2 and 100 characters')
    .matches(/^[a-zA-Z\s\.]+$/)
    .withMessage('Contact person name contains invalid characters'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail(),
  body('phone')
    .optional()
    .isMobilePhone('any')
    .withMessage('Please provide a valid phone number'),
  body('gst_number')
    .optional({ nullable: true, checkFalsy: true })
    .customSanitizer((value) => {
      // Convert empty strings to null
      if (!value || typeof value !== 'string' || value.trim() === '') {
        return null;
      }
      return value.trim().toUpperCase();
    })
    .custom((value) => {
      // Skip validation if value is null or empty
      if (value === null || value === undefined || value === '') {
        return true;
      }
      if (value.length !== 15) {
        throw new Error('GST number must be exactly 15 characters');
      }
      if (!/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/.test(value)) {
        throw new Error('Invalid GST number format');
      }
      return true;
    }),
  body('pan_number')
    .optional({ nullable: true, checkFalsy: true })
    .customSanitizer((value) => {
      // Convert empty strings to null
      if (!value || typeof value !== 'string' || value.trim() === '') {
        return null;
      }
      return value.trim().toUpperCase();
    })
    .custom((value) => {
      // Skip validation if value is null or empty
      if (value === null || value === undefined || value === '') {
        return true;
      }
      if (value.length !== 10) {
        throw new Error('PAN number must be exactly 10 characters');
      }
      if (!/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(value)) {
        throw new Error('Invalid PAN number format');
      }
      return true;
    }),
  body('address_line_1')
    .optional()
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Address line 1 must be between 1 and 200 characters'),
  // City field is optional and not validated since frontend sends area_id as city
  body('city')
    .optional({ nullable: true, checkFalsy: true }),
  // State field is optional and not validated
  body('state')
    .optional({ nullable: true, checkFalsy: true }),
  // Postal code field is optional and not validated
  body('postal_code')
    .optional({ nullable: true, checkFalsy: true }),
  body('country')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Country must be between 1 and 100 characters')
    .matches(/^[a-zA-Z\s\-\.]+$/)
    .withMessage('Country name contains invalid characters'),
  body('industry_id')
    .optional()
    .isUUID()
    .withMessage('Industry ID must be a valid UUID'),
  body('area_id')
    .optional()
    .isUUID()
    .withMessage('Area ID must be a valid UUID'),
  body('assigned_executive_id')
    .optional()
    .isUUID()
    .withMessage('Assigned executive ID must be a valid UUID'),
  body('credit_limit')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Credit limit must be a positive number'),
  body('credit_days')
    .optional()
    .isInt({ min: 0, max: 365 })
    .withMessage('Credit days must be between 0 and 365'),
  body('website')
    .optional()
    .isURL()
    .withMessage('Please provide a valid website URL'),
  body('annual_revenue')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Annual revenue must be a positive number'),
  body('employee_count')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Employee count must be a positive integer'),
  body('first_contact_date')
    .optional()
    .isISO8601()
    .withMessage('First contact date must be a valid date'),
  body('next_follow_up_date')
    .optional()
    .isISO8601()
    .withMessage('Next follow up date must be a valid date'),
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  body('notes')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Notes must not exceed 1000 characters'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  body('tags.*')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Each tag must be between 1 and 50 characters'),
  body('custom_fields')
    .optional()
    .isObject()
    .withMessage('Custom fields must be an object'),
  validateRequest,
], createCustomer);

/**
 * @route   PUT /api/customers/:id
 * @desc    Update customer
 * @access  Private (requires customers.update permission)
 */
router.put('/:id', [
  requirePermission('customers.update'),
  param('id')
    .isUUID()
    .withMessage('Customer ID must be a valid UUID'),
  body('company_name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Company name must be between 2 and 200 characters')
    .matches(/^[a-zA-Z0-9\s\-\.\&\(\)]+$/)
    .withMessage('Company name contains invalid characters'),
  body('customer_code')
    .optional()
    .trim()
    .isLength({ min: 2, max: 20 })
    .withMessage('Customer code must be between 2 and 20 characters')
    .matches(/^[A-Z0-9]+$/)
    .withMessage('Customer code must contain only uppercase letters and numbers'),
  body('display_name')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Display name must be between 1 and 100 characters'),
  body('customer_type')
    .optional()
    .isIn(['prospect', 'customer', 'inactive', 'blacklisted'])
    .withMessage('Invalid customer type'),
  body('business_type')
    .optional()
    .isIn(['individual', 'partnership', 'private_limited', 'public_limited', 'llp', 'proprietorship', 'trust', 'society', 'government', 'other'])
    .withMessage('Invalid business type'),
  body('contact_person')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Contact person must be between 2 and 100 characters')
    .matches(/^[a-zA-Z\s\.]+$/)
    .withMessage('Contact person name contains invalid characters'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail(),
  body('phone')
    .optional()
    .isMobilePhone('any')
    .withMessage('Please provide a valid phone number'),
  body('gst_number')
    .optional({ nullable: true, checkFalsy: true })
    .customSanitizer((value) => {
      // Convert empty strings to null
      if (!value || typeof value !== 'string' || value.trim() === '') {
        return null;
      }
      return value.trim().toUpperCase();
    })
    .custom((value) => {
      // Skip validation if value is null or empty
      if (value === null || value === undefined || value === '') {
        return true;
      }
      if (value.length !== 15) {
        throw new Error('GST number must be exactly 15 characters');
      }
      if (!/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/.test(value)) {
        throw new Error('Invalid GST number format');
      }
      return true;
    }),
  body('pan_number')
    .optional({ nullable: true, checkFalsy: true })
    .customSanitizer((value) => {
      // Convert empty strings to null
      if (!value || typeof value !== 'string' || value.trim() === '') {
        return null;
      }
      return value.trim().toUpperCase();
    })
    .custom((value) => {
      // Skip validation if value is null or empty
      if (value === null || value === undefined || value === '') {
        return true;
      }
      if (value.length !== 10) {
        throw new Error('PAN number must be exactly 10 characters');
      }
      if (!/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(value)) {
        throw new Error('Invalid PAN number format');
      }
      return true;
    }),
  body('address_line_1')
    .optional()
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Address line 1 must be between 1 and 200 characters'),
  // City field is optional and not validated since frontend sends area_id as city
  body('city')
    .optional({ nullable: true, checkFalsy: true }),
  // State field is optional and not validated
  body('state')
    .optional({ nullable: true, checkFalsy: true }),
  // Postal code field is optional and not validated
  body('postal_code')
    .optional({ nullable: true, checkFalsy: true }),
  body('country')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Country must be between 1 and 100 characters')
    .matches(/^[a-zA-Z\s\-\.]+$/)
    .withMessage('Country name contains invalid characters'),
  body('industry_id')
    .optional()
    .isUUID()
    .withMessage('Industry ID must be a valid UUID'),
  body('area_id')
    .optional()
    .isUUID()
    .withMessage('Area ID must be a valid UUID'),
  body('assigned_executive_id')
    .optional()
    .isUUID()
    .withMessage('Assigned executive ID must be a valid UUID'),
  body('credit_limit')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Credit limit must be a positive number'),
  body('credit_days')
    .optional()
    .isInt({ min: 0, max: 365 })
    .withMessage('Credit days must be between 0 and 365'),
  body('website')
    .optional()
    .isURL()
    .withMessage('Please provide a valid website URL'),
  body('annual_revenue')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Annual revenue must be a positive number'),
  body('employee_count')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Employee count must be a positive integer'),
  body('first_contact_date')
    .optional()
    .isISO8601()
    .withMessage('First contact date must be a valid date'),
  body('next_follow_up_date')
    .optional()
    .isISO8601()
    .withMessage('Next follow up date must be a valid date'),
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  body('notes')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Notes must not exceed 1000 characters'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  body('tags.*')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Each tag must be between 1 and 50 characters'),
  body('custom_fields')
    .optional()
    .isObject()
    .withMessage('Custom fields must be an object'),
  validateRequest,
], updateCustomer);

/**
 * @route   DELETE /api/customers/:id
 * @desc    Delete customer (soft delete)
 * @access  Private (requires customers.delete permission)
 */
router.delete('/:id', [
  requirePermission('customers.delete'),
  param('id')
    .isUUID()
    .withMessage('Customer ID must be a valid UUID'),
  query('force')
    .optional()
    .isBoolean()
    .withMessage('Force delete must be a boolean'),
  query('reason')
    .optional()
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Deletion reason must be between 5 and 200 characters'),
  validateRequest,
], deleteCustomer);

export default router;
