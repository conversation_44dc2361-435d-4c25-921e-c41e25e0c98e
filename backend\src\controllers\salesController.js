import { Op } from 'sequelize';
import models from '../models/index.js';
import { logger } from '../utils/logger.js';

/**
 * Get all sales with pagination and filters
 */
export const getSales = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      customerId,
      salesExecutiveId,
      saleType,
      status,
      paymentStatus,
      dateFrom,
      dateTo,
      sortBy = 'created_at',
      sortOrder = 'DESC',
    } = req.query;

    const offset = (page - 1) * limit;
    const tenantId = req.user.tenant.id;

    // Build where conditions
    const whereConditions = {
      tenant_id: tenantId,
    };

    // Add filters
    if (customerId) whereConditions.customer_id = customerId;
    if (salesExecutiveId) whereConditions.sales_executive_id = salesExecutiveId;
    if (saleType) whereConditions.sale_type = saleType;
    if (status) whereConditions.status = status;
    if (paymentStatus) whereConditions.payment_status = paymentStatus;

    // Date range filter
    if (dateFrom || dateTo) {
      whereConditions.sale_date = {};
      if (dateFrom) whereConditions.sale_date[Op.gte] = new Date(dateFrom);
      if (dateTo) whereConditions.sale_date[Op.lte] = new Date(dateTo);
    }

    // Search functionality
    if (search) {
      whereConditions[Op.or] = [
        { sale_number: { [Op.iLike]: `%${search}%` } },
        { '$customer.company_name$': { [Op.iLike]: `%${search}%` } },
        { '$customer.customer_code$': { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } },
      ];
    }

    // Get sales with associations
    const { count, rows: sales } = await models.Sale.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'customer_code', 'display_name'],
        },
        {
          model: models.Executive,
          as: 'salesExecutive',
          attributes: ['id', 'first_name', 'last_name', 'employee_code'],
        },

      ],
      order: [[sortBy, sortOrder]],
      limit: parseInt(limit),
      offset: parseInt(offset),
      distinct: true,
    });

    // Calculate pagination info
    const totalPages = Math.ceil(count / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    res.json({
      success: true,
      data: {
        sales: sales.map(sale => ({
          id: sale.id,
          saleNumber: sale.sale_number,
          customer: sale.customer,
          salesExecutive: sale.salesExecutive ? {
            ...sale.salesExecutive.toJSON(),
            name: `${sale.salesExecutive.first_name || ''} ${sale.salesExecutive.last_name || ''}`.trim()
          } : null,
          saleType: sale.sale_type,
          saleDate: sale.sale_date,
          totalAmount: parseFloat(sale.total_amount),
          paidAmount: parseFloat(sale.paid_amount),
          balanceAmount: parseFloat(sale.balance_amount),
          status: sale.status,
          paymentStatus: sale.payment_status,
          description: sale.description,
          createdBy: null,
          createdAt: sale.created_at,
          updatedAt: sale.updated_at,
        })),
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems: count,
          itemsPerPage: parseInt(limit),
          hasNextPage,
          hasPrevPage,
        },
      },
    });
  } catch (error) {
    logger.error('Get sales error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve sales',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get sale by ID
 */
export const getSaleById = async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenant.id;

    const sale = await models.Sale.findOne({
      where: {
        id,
        tenant_id: tenantId,
      },
      include: [
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'customer_code', 'display_name'],
        },
        {
          model: models.Executive,
          as: 'salesExecutive',
          attributes: ['id', 'first_name', 'last_name', 'employee_code'],
        },
        {
          model: models.SaleItem,
          as: 'items',
          include: [
            {
              model: models.TallyProduct,
              as: 'product',
              attributes: ['id', 'name', 'version'],
            },
          ],
        },

      ],
    });

    if (!sale) {
      return res.status(404).json({
        success: false,
        message: 'Sale not found',
      });
    }

    res.json({
      success: true,
      data: {
        sale: {
          id: sale.id,
          saleNumber: sale.sale_number,
          customer: sale.customer,
          salesExecutive: sale.salesExecutive ? {
            ...sale.salesExecutive.toJSON(),
            name: `${sale.salesExecutive.first_name || ''} ${sale.salesExecutive.last_name || ''}`.trim()
          } : null,
          saleType: sale.sale_type,
          saleDate: sale.sale_date,
          totalAmount: parseFloat(sale.total_amount),
          paidAmount: parseFloat(sale.paid_amount),
          balanceAmount: parseFloat(sale.balance_amount),
          status: sale.status,
          paymentStatus: sale.payment_status,
          description: sale.description,
          remarks: sale.remarks,
          saleItems: sale.items?.map(item => ({
            id: item.id,
            product: item.product,
            quantity: item.quantity,
            unitPrice: parseFloat(item.unit_price),
            totalPrice: parseFloat(item.total_price),
            description: item.description,
          })) || [],
          createdBy: null,
          approvedBy: null,
          createdAt: sale.created_at,
          updatedAt: sale.updated_at,
        },
      },
    });
  } catch (error) {
    logger.error('Get sale by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve sale',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Create new sale
 */
export const createSale = async (req, res) => {
  const transaction = await models.sequelize.transaction();

  try {
    const {
      customer_id,
      sales_executive_id,
      sale_type = 'new',
      sale_date,
      total_amount,
      paid_amount = 0,
      description,
      remarks,
      sale_items = [],
    } = req.body;

    const tenantId = req.user.tenant.id;
    const userId = req.user.id;

    // Validate customer exists
    const customer = await models.Customer.findOne({
      where: { id: customer_id, tenant_id: tenantId },
    });

    if (!customer) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Customer not found',
      });
    }

    // Generate sale number
    const saleCount = await models.Sale.count({
      where: { tenant_id: tenantId },
    });
    const saleNumber = `SAL-${String(saleCount + 1).padStart(6, '0')}`;

    // Calculate balance amount
    const balanceAmount = parseFloat(total_amount) - parseFloat(paid_amount);

    // Create sale
    const sale = await models.Sale.create({
      tenant_id: tenantId,
      sale_number: saleNumber,
      customer_id,
      sales_executive_id,
      sale_type,
      sale_date: sale_date || new Date(),
      total_amount: parseFloat(total_amount),
      paid_amount: parseFloat(paid_amount),
      balance_amount: balanceAmount,
      status: 'draft',
      payment_status: balanceAmount > 0 ? 'partial' : 'paid',
      description,
      remarks,
      created_by: userId,
    }, { transaction });

    // Create sale items if provided
    if (sale_items && sale_items.length > 0) {
      const saleItemsData = sale_items.map(item => ({
        sale_id: sale.id,
        product_id: item.product_id,
        quantity: item.quantity || 1,
        unit_price: parseFloat(item.unit_price),
        total_price: parseFloat(item.total_price || item.unit_price * (item.quantity || 1)),
        description: item.description,
      }));

      await models.SaleItem.bulkCreate(saleItemsData, { transaction });
    }

    await transaction.commit();

    // Fetch the created sale with associations
    const createdSale = await models.Sale.findByPk(sale.id, {
      include: [
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'customer_code'],
        },
        {
          model: models.Executive,
          as: 'salesExecutive',
          attributes: ['id', 'first_name', 'last_name', 'employee_code'],
        },
      ],
    });

    logger.info('Sale created:', {
      saleId: sale.id,
      saleNumber: sale.sale_number,
      customerId: customer_id,
      userId,
      tenantId,
    });

    res.status(201).json({
      success: true,
      message: 'Sale created successfully',
      data: { sale: createdSale },
    });
  } catch (error) {
    await transaction.rollback();
    logger.error('Create sale error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create sale',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Update sale
 */
export const updateSale = async (req, res) => {
  const transaction = await models.sequelize.transaction();

  try {
    const { id } = req.params;
    const {
      customer_id,
      sales_executive_id,
      sale_type,
      sale_date,
      total_amount,
      paid_amount,
      description,
      remarks,
      status,
    } = req.body;

    const tenantId = req.user.tenant.id;
    const userId = req.user.id;

    // Find sale
    const sale = await models.Sale.findOne({
      where: {
        id,
        tenant_id: tenantId,
      },
    });

    if (!sale) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: 'Sale not found',
      });
    }

    // Validate customer if provided
    if (customer_id) {
      const customer = await models.Customer.findOne({
        where: { id: customer_id, tenant_id: tenantId },
      });

      if (!customer) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: 'Customer not found',
        });
      }
    }

    // Calculate balance amount if amounts are provided
    let balanceAmount = sale.balance_amount;
    if (total_amount !== undefined || paid_amount !== undefined) {
      const newTotalAmount = total_amount !== undefined ? parseFloat(total_amount) : sale.total_amount;
      const newPaidAmount = paid_amount !== undefined ? parseFloat(paid_amount) : sale.paid_amount;
      balanceAmount = newTotalAmount - newPaidAmount;
    }

    // Update sale
    await sale.update({
      ...(customer_id && { customer_id }),
      ...(sales_executive_id && { sales_executive_id }),
      ...(sale_type && { sale_type }),
      ...(sale_date && { sale_date }),
      ...(total_amount !== undefined && { total_amount: parseFloat(total_amount) }),
      ...(paid_amount !== undefined && { paid_amount: parseFloat(paid_amount) }),
      balance_amount: balanceAmount,
      ...(description && { description }),
      ...(remarks && { remarks }),
      ...(status && { status }),
      payment_status: balanceAmount > 0 ? 'partial' : 'paid',
      updated_by: userId,
    }, { transaction });

    await transaction.commit();

    // Fetch updated sale with associations
    const updatedSale = await models.Sale.findByPk(id, {
      include: [
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'customer_code'],
        },
        {
          model: models.Executive,
          as: 'salesExecutive',
          attributes: ['id', 'first_name', 'last_name', 'employee_code'],
        },
      ],
    });

    logger.info('Sale updated:', {
      saleId: id,
      userId,
      tenantId,
    });

    res.json({
      success: true,
      message: 'Sale updated successfully',
      data: { sale: updatedSale },
    });
  } catch (error) {
    await transaction.rollback();
    logger.error('Update sale error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update sale',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Delete sale
 */
export const deleteSale = async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenant.id;

    const sale = await models.Sale.findOne({
      where: {
        id,
        tenant_id: tenantId,
      },
    });

    if (!sale) {
      return res.status(404).json({
        success: false,
        message: 'Sale not found',
      });
    }

    // Check if sale can be deleted (only draft sales can be deleted)
    if (sale.status !== 'draft') {
      return res.status(409).json({
        success: false,
        message: 'Only draft sales can be deleted',
      });
    }

    await sale.destroy();

    logger.info('Sale deleted:', {
      saleId: id,
      userId: req.user.id,
      tenantId,
    });

    res.json({
      success: true,
      message: 'Sale deleted successfully',
    });
  } catch (error) {
    logger.error('Delete sale error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete sale',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get sales statistics
 */
export const getSalesStats = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;

    const stats = await Promise.all([
      // Total sales
      models.Sale.count({
        where: { tenant_id: tenantId },
      }),

      // Sales by status
      models.Sale.findAll({
        where: { tenant_id: tenantId },
        attributes: [
          'status',
          [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
        ],
        group: ['status'],
        raw: true,
      }),

      // Sales by type
      models.Sale.findAll({
        where: { tenant_id: tenantId },
        attributes: [
          'sale_type',
          [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
        ],
        group: ['sale_type'],
        raw: true,
      }),

      // Total revenue
      models.Sale.findOne({
        where: { tenant_id: tenantId },
        attributes: [
          [models.sequelize.fn('SUM', models.sequelize.col('total_amount')), 'total_revenue'],
          [models.sequelize.fn('SUM', models.sequelize.col('paid_amount')), 'paid_amount'],
          [models.sequelize.fn('SUM', models.sequelize.col('balance_amount')), 'outstanding_amount'],
        ],
        raw: true,
      }),

      // Recent sales (last 30 days)
      models.Sale.count({
        where: {
          tenant_id: tenantId,
          sale_date: {
            [Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          },
        },
      }),
    ]);

    const [totalSales, salesByStatus, salesByType, revenueStats, recentSales] = stats;

    res.json({
      success: true,
      data: {
        totalSales,
        recentSales,
        salesByStatus: salesByStatus.reduce((acc, item) => {
          acc[item.status] = parseInt(item.count);
          return acc;
        }, {}),
        salesByType: salesByType.reduce((acc, item) => {
          acc[item.sale_type] = parseInt(item.count);
          return acc;
        }, {}),
        revenue: {
          total: parseFloat(revenueStats?.total_revenue || 0),
          paid: parseFloat(revenueStats?.paid_amount || 0),
          outstanding: parseFloat(revenueStats?.outstanding_amount || 0),
        },
      },
    });
  } catch (error) {
    logger.error('Get sales stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve sales statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};