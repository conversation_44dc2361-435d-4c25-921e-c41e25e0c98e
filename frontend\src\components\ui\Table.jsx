import React from 'react';
import { cn } from '../../utils/helpers';

const Table = React.forwardRef(({
  children,
  className,
  striped = false,
  hover = false,
  bordered = false,
  responsive = false,
  size = 'md',
  ...props
}, ref) => {
  const baseClasses = 'w-full text-sm text-left min-w-0';

  const sizes = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  };

  const tableClasses = cn(
    baseClasses,
    sizes[size],
    bordered && 'border border-gray-200',
    className
  );

  const table = (
    <table
      ref={ref}
      className={tableClasses}
      {...props}
    >
      {children}
    </table>
  );

  if (responsive) {
    return (
      <div className="overflow-x-auto min-w-0 w-full">
        {table}
      </div>
    );
  }

  return table;
});

const TableHead = React.forwardRef(({
  children,
  className,
  ...props
}, ref) => {
  return (
    <thead
      ref={ref}
      className={cn('bg-gray-50', className)}
      {...props}
    >
      {children}
    </thead>
  );
});

const TableBody = React.forwardRef(({
  children,
  className,
  striped = false,
  hover = false,
  ...props
}, ref) => {
  const bodyClasses = cn(
    'divide-y divide-gray-200',
    striped && '[&>tr:nth-child(odd)]:bg-gray-50',
    hover && '[&>tr:hover]:bg-gray-50',
    className
  );

  return (
    <tbody
      ref={ref}
      className={bodyClasses}
      {...props}
    >
      {children}
    </tbody>
  );
});

const TableRow = React.forwardRef(({
  children,
  className,
  ...props
}, ref) => {
  return (
    <tr
      ref={ref}
      className={cn('border-b border-gray-200', className)}
      {...props}
    >
      {children}
    </tr>
  );
});

const TableHeader = React.forwardRef(({
  children,
  className,
  ...props
}, ref) => {
  return (
    <th
      ref={ref}
      className={cn('px-4 py-3 font-semibold text-gray-900 border-b-2 border-gray-200', className)}
      {...props}
    >
      {children}
    </th>
  );
});

const TableCell = React.forwardRef(({
  children,
  className,
  ...props
}, ref) => {
  return (
    <td
      ref={ref}
      className={cn('px-4 py-3', className)}
      {...props}
    >
      {children}
    </td>
  );
});

Table.displayName = 'Table';
TableHead.displayName = 'TableHead';
TableBody.displayName = 'TableBody';
TableRow.displayName = 'TableRow';
TableHeader.displayName = 'TableHeader';
TableCell.displayName = 'TableCell';

export default Table;
export { TableHead, TableBody, TableRow, TableHeader, TableCell };
