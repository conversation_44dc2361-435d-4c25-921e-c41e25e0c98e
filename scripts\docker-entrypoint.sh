#!/bin/sh

# Docker entrypoint script for TallyCRM
# This script handles environment setup and application startup

set -e

echo "🚀 Starting TallyCRM Container..."
echo "📍 Environment: ${NODE_ENV:-development}"
echo "🌐 Port: ${PORT:-8080}"

# Function to log with timestamp
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

# Check if we're running as root and switch to tallycrm user if needed
if [ "$(id -u)" = "0" ]; then
    log "⚠️  Running as root, switching to tallycrm user..."
    exec su-exec tallycrm "$0" "$@"
fi

# Ensure we're in the correct directory
cd /app

# Check if environment files exist
log "🔍 Checking environment files..."
if [ -f "/app/backend/.env" ]; then
    log "✅ Backend .env file found"
else
    log "⚠️  Backend .env file not found, using example"
    if [ -f "/app/backend/.env.example" ]; then
        cp /app/backend/.env.example /app/backend/.env
        log "✅ Created backend .env from example"
    fi
fi

if [ -f "/app/frontend/.env" ]; then
    log "✅ Frontend .env file found"
else
    log "⚠️  Frontend .env file not found, using example"
    if [ -f "/app/frontend/.env.example" ]; then
        cp /app/frontend/.env.example /app/frontend/.env
        log "✅ Created frontend .env from example"
    fi
fi

# Check essential environment variables
log "🔧 Checking essential environment variables..."

if [ -z "$NODE_ENV" ]; then
    log "⚠️  NODE_ENV not set, defaulting to production"
    export NODE_ENV=production
fi

if [ -z "$PORT" ]; then
    log "⚠️  PORT not set, defaulting to 8080"
    export PORT=8080
fi

# Database connection check
if [ -z "$DB_HOST" ]; then
    log "❌ DB_HOST not set! Database connection will fail."
    log "💡 Set DB_HOST environment variable to your database host"
fi

if [ -z "$DB_PASSWORD" ]; then
    log "❌ DB_PASSWORD not set! Database connection will fail."
    log "💡 Set DB_PASSWORD environment variable"
fi

if [ -z "$JWT_SECRET" ]; then
    log "❌ JWT_SECRET not set! Authentication will fail."
    log "💡 Set JWT_SECRET environment variable (minimum 32 characters)"
fi

# Create necessary directories
log "📁 Creating necessary directories..."
mkdir -p /app/logs /app/uploads
chmod 755 /app/logs /app/uploads

# Wait for database if DB_HOST is set
if [ -n "$DB_HOST" ] && [ "$DB_HOST" != "localhost" ]; then
    log "⏳ Waiting for database at $DB_HOST:${DB_PORT:-5432}..."
    
    # Simple wait for database
    timeout=60
    while [ $timeout -gt 0 ]; do
        if nc -z "$DB_HOST" "${DB_PORT:-5432}" 2>/dev/null; then
            log "✅ Database is ready!"
            break
        fi
        timeout=$((timeout - 1))
        sleep 1
    done
    
    if [ $timeout -eq 0 ]; then
        log "⚠️  Database connection timeout. Proceeding anyway..."
    fi
fi

# Display startup information
log "📋 Startup Information:"
log "   Node.js Version: $(node --version)"
log "   Working Directory: $(pwd)"
log "   User: $(whoami)"
log "   Environment: $NODE_ENV"
log "   Port: $PORT"
log "   Database Host: ${DB_HOST:-not set}"
log "   Frontend Files: $(ls -la /app/frontend/dist 2>/dev/null | wc -l) files"

# Start the application
log "🚀 Starting TallyCRM Backend Server..."

# Execute the main command
exec "$@"
