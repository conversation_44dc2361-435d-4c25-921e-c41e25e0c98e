import { DataTypes, Op } from 'sequelize';

export const up = async (queryInterface) => {
  await queryInterface.createTable('payments', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    invoice_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'invoices',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    stripe_payment_intent_id: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: true,
    },
    stripe_charge_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    payment_method: {
      type: DataTypes.ENUM(
        'card',
        'bank_transfer',
        'upi',
        'wallet',
        'cash',
        'cheque',
        'other'
      ),
      defaultValue: 'card',
    },
    status: {
      type: DataTypes.ENUM(
        'pending',
        'processing',
        'succeeded',
        'failed',
        'canceled',
        'refunded',
        'partially_refunded'
      ),
      defaultValue: 'pending',
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    amount_refunded: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0,
    },
    currency: {
      type: DataTypes.STRING(3),
      defaultValue: 'INR',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    failure_reason: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    failure_code: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    receipt_email: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    receipt_url: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    refund_reason: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    processed_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    failed_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    refunded_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    metadata: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Create indexes
  await queryInterface.addIndex('payments', ['tenant_id'], {
    name: 'payments_tenant_id_idx',
  });

  await queryInterface.addIndex('payments', ['invoice_id'], {
    name: 'payments_invoice_id_idx',
  });

  await queryInterface.addIndex('payments', ['stripe_payment_intent_id'], {
    unique: true,
    name: 'payments_stripe_payment_intent_id_unique',
    where: {
      stripe_payment_intent_id: {
        [Op.ne]: null,
      },
    },
  });

  await queryInterface.addIndex('payments', ['status'], {
    name: 'payments_status_idx',
  });

  await queryInterface.addIndex('payments', ['payment_method'], {
    name: 'payments_payment_method_idx',
  });

  await queryInterface.addIndex('payments', ['processed_at'], {
    name: 'payments_processed_at_idx',
  });
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('payments');
};
