import { Sequelize } from 'sequelize';
import 'dotenv/config';

const testConnection = async () => {
  console.log('🔧 Testing PostgreSQL connection...');
  console.log('Host:', process.env.DB_HOST);
  console.log('Port:', process.env.DB_PORT);
  console.log('Database:', process.env.DB_NAME);
  console.log('Username:', process.env.DB_USERNAME);

  const sequelize = new Sequelize(
    process.env.DB_NAME,
    process.env.DB_USERNAME,
    process.env.DB_PASSWORD,
    {
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      dialect: 'postgres',
      logging: console.log,
      dialectOptions: {
        ssl: false,
        connectTimeout: 10000
      },
      pool: {
        max: 5,
        min: 0,
        acquire: 10000,
        idle: 10000
      }
    }
  );

  try {
    console.log('🔄 Attempting to connect...');
    await sequelize.authenticate();
    console.log('✅ Database connection successful!');

    // Test a simple query
    const [results] = await sequelize.query('SELECT version()');
    console.log('📊 PostgreSQL version:', results[0].version);

  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.error('Error code:', error.code);
    console.error('Error name:', error.name);
  } finally {
    await sequelize.close();
  }
};

testConnection();
