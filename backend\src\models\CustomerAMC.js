import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const CustomerAMC = sequelize.define('CustomerAMC', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    customer_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'customers',
        key: 'id',
      },
    },
    tss_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'customer_tss',
        key: 'id',
      },
    },
    amc_number: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      comment: 'AMC contract number',
    },
    contract_type: {
      type: DataTypes.ENUM('standard', 'premium', 'enterprise', 'custom'),
      allowNull: false,
      defaultValue: 'standard',
    },
    start_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    end_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    renewal_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: 'Next renewal date',
    },
    contract_value: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    payment_terms: {
      type: DataTypes.ENUM('annual', 'semi_annual', 'quarterly', 'monthly'),
      allowNull: false,
      defaultValue: 'annual',
    },
    payment_status: {
      type: DataTypes.ENUM('paid', 'pending', 'overdue', 'partial'),
      allowNull: false,
      defaultValue: 'pending',
    },
    last_payment_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    next_payment_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    status: {
      type: DataTypes.ENUM('active', 'expired', 'suspended', 'cancelled', 'renewed'),
      allowNull: false,
      defaultValue: 'active',
    },
    services_included: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Array of services included in AMC',
    },
    service_limits: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: 'Service limits and quotas',
    },
    calls_allowed: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Number of service calls allowed per year',
    },
    calls_used: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: 'Number of service calls used',
    },
    onsite_visits_allowed: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Number of onsite visits allowed per year',
    },
    onsite_visits_used: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: 'Number of onsite visits used',
    },
    remote_support_hours: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Remote support hours allowed per year',
    },
    remote_support_used: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: 'Remote support hours used',
    },
    training_hours: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Training hours included per year',
    },
    training_used: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: 'Training hours used',
    },
    response_time_sla: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Response time SLA in hours',
    },
    resolution_time_sla: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Resolution time SLA in hours',
    },
    coverage_hours: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: '9 AM to 6 PM',
      comment: 'Support coverage hours',
    },
    coverage_days: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'Monday to Saturday',
      comment: 'Support coverage days',
    },
    escalation_matrix: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Escalation matrix for issues',
    },
    exclusions: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Services and items excluded from AMC',
    },
    special_terms: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Special terms and conditions',
    },
    auto_renewal: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Whether contract auto-renews',
    },
    renewal_notice_days: {
      type: DataTypes.INTEGER,
      defaultValue: 30,
      comment: 'Days before expiry to send renewal notice',
    },
    discount_percentage: {
      type: DataTypes.DECIMAL(5, 2),
      defaultValue: 0.00,
      comment: 'Discount percentage applied',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    created_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'customer_amc',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['customer_id'],
      },
      {
        fields: ['tss_id'],
      },
      {
        fields: ['amc_number'],
        unique: true,
      },
      {
        fields: ['status'],
      },
      {
        fields: ['start_date'],
      },
      {
        fields: ['end_date'],
      },
      {
        fields: ['renewal_date'],
      },
      {
        fields: ['payment_status'],
      },
      {
        fields: ['is_active'],
      },
    ],
  });

  // Instance methods
  CustomerAMC.prototype.isExpired = function() {
    return new Date(this.end_date) < new Date();
  };

  CustomerAMC.prototype.isExpiringSoon = function(days = 30) {
    const endDate = new Date(this.end_date);
    const warningDate = new Date();
    warningDate.setDate(warningDate.getDate() + days);
    return endDate <= warningDate && endDate >= new Date();
  };

  CustomerAMC.prototype.getDaysUntilExpiry = function() {
    const endDate = new Date(this.end_date);
    const today = new Date();
    const diffTime = endDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  CustomerAMC.prototype.getDaysUntilRenewal = function() {
    if (!this.renewal_date) return null;
    const renewalDate = new Date(this.renewal_date);
    const today = new Date();
    const diffTime = renewalDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  CustomerAMC.prototype.getCallsRemaining = function() {
    if (!this.calls_allowed) return null;
    return Math.max(0, this.calls_allowed - this.calls_used);
  };

  CustomerAMC.prototype.getOnsiteVisitsRemaining = function() {
    if (!this.onsite_visits_allowed) return null;
    return Math.max(0, this.onsite_visits_allowed - this.onsite_visits_used);
  };

  CustomerAMC.prototype.getRemoteSupportHoursRemaining = function() {
    if (!this.remote_support_hours) return null;
    return Math.max(0, this.remote_support_hours - this.remote_support_used);
  };

  CustomerAMC.prototype.getTrainingHoursRemaining = function() {
    if (!this.training_hours) return null;
    return Math.max(0, this.training_hours - this.training_used);
  };

  CustomerAMC.prototype.getUtilizationPercentage = function() {
    if (!this.calls_allowed) return 0;
    return (this.calls_used / this.calls_allowed) * 100;
  };

  CustomerAMC.prototype.canCreateServiceCall = function() {
    if (!this.is_active || this.status !== 'active') return false;
    if (this.isExpired()) return false;
    if (this.calls_allowed && this.calls_used >= this.calls_allowed) return false;
    return true;
  };

  CustomerAMC.prototype.canCreateOnsiteVisit = function() {
    if (!this.canCreateServiceCall()) return false;
    if (this.onsite_visits_allowed && this.onsite_visits_used >= this.onsite_visits_allowed) return false;
    return true;
  };

  CustomerAMC.prototype.getStatusColor = function() {
    const colors = {
      active: '#28a745',
      expired: '#dc3545',
      suspended: '#ffc107',
      cancelled: '#6c757d',
      renewed: '#17a2b8',
    };
    return colors[this.status] || '#6c757d';
  };

  CustomerAMC.prototype.getPaymentStatusColor = function() {
    const colors = {
      paid: '#28a745',
      pending: '#ffc107',
      overdue: '#dc3545',
      partial: '#fd7e14',
    };
    return colors[this.payment_status] || '#6c757d';
  };

  // Associations
  CustomerAMC.associate = function(models) {
    CustomerAMC.belongsTo(models.Customer, {
      foreignKey: 'customer_id',
      as: 'customer',
    });

    CustomerAMC.belongsTo(models.CustomerTSS, {
      foreignKey: 'tss_id',
      as: 'tss',
    });

    CustomerAMC.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator',
    });

    CustomerAMC.hasMany(models.ServiceCall, {
      foreignKey: 'amc_id',
      as: 'serviceCalls',
    });
  };

  return CustomerAMC;
}
