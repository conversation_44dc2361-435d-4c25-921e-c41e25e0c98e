import { Op } from 'sequelize';
import models from '../models/index.js';
import { logger } from '../utils/logger.js';

/**
 * Get customer reports
 */
export const getCustomerReports = async (req, res) => {
  try {
    const {
      dateFrom,
      dateTo,
      customerType,
      industryId,
      areaId,
      assignedExecutiveId,
    } = req.query;

    const tenantId = req.user.tenant.id;

    // Build where conditions
    const whereConditions = {
      tenant_id: tenantId,
    };

    if (customerType) whereConditions.customer_type = customerType;
    if (industryId) whereConditions.industry_id = industryId;
    if (areaId) whereConditions.area_id = areaId;
    if (assignedExecutiveId) whereConditions.assigned_executive_id = assignedExecutiveId;

    // Date range filter
    if (dateFrom || dateTo) {
      whereConditions.created_at = {};
      if (dateFrom) whereConditions.created_at[Op.gte] = new Date(dateFrom);
      if (dateTo) whereConditions.created_at[Op.lte] = new Date(dateTo);
    }

    const customers = await models.Customer.findAll({
      where: whereConditions,
      include: [
        {
          model: models.Industry,
          as: 'industry',
          attributes: ['id', 'name'],
        },
        {
          model: models.Area,
          as: 'area',
          attributes: ['id', 'name'],
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          attributes: ['id', 'first_name', 'last_name', 'employee_code'],
        },
      ],
      order: [['created_at', 'DESC']],
    });

    // Generate summary statistics
    const summary = {
      totalCustomers: customers.length,
      byType: customers.reduce((acc, customer) => {
        acc[customer.customer_type] = (acc[customer.customer_type] || 0) + 1;
        return acc;
      }, {}),
      byIndustry: customers.reduce((acc, customer) => {
        const industry = customer.industry?.name || 'Unknown';
        acc[industry] = (acc[industry] || 0) + 1;
        return acc;
      }, {}),
      byArea: customers.reduce((acc, customer) => {
        const area = customer.area?.name || 'Unknown';
        acc[area] = (acc[area] || 0) + 1;
        return acc;
      }, {}),
    };

    res.json({
      success: true,
      data: {
        customers: customers.map(customer => ({
          id: customer.id,
          companyName: customer.company_name,
          customerCode: customer.customer_code,
          customerType: customer.customer_type,
          industry: customer.industry,
          area: customer.area,
          assignedExecutive: customer.assignedExecutive ? {
            ...customer.assignedExecutive.toJSON(),
            name: `${customer.assignedExecutive.first_name || ''} ${customer.assignedExecutive.last_name || ''}`.trim()
          } : null,
          contactPerson: customer.contact_person,
          email: customer.email,
          phone: customer.phone,
          createdAt: customer.created_at,
        })),
        summary,
      },
    });
  } catch (error) {
    logger.error('Get customer reports error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate customer reports',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get service call reports
 */
export const getServiceCallReports = async (req, res) => {
  try {
    const {
      dateFrom,
      dateTo,
      statusId,
      priority,
      assignedTo,
      customerId,
    } = req.query;

    const tenantId = req.user.tenant.id;

    // Build where conditions
    const whereConditions = {
      tenant_id: tenantId,
    };

    if (statusId) whereConditions.status_id = statusId;
    if (priority) whereConditions.priority = priority;
    if (assignedTo) whereConditions.assigned_to = assignedTo;
    if (customerId) whereConditions.customer_id = customerId;

    // Date range filter
    if (dateFrom || dateTo) {
      whereConditions.call_date = {};
      if (dateFrom) whereConditions.call_date[Op.gte] = new Date(dateFrom);
      if (dateTo) whereConditions.call_date[Op.lte] = new Date(dateTo);
    }

    const serviceCalls = await models.ServiceCall.findAll({
      where: whereConditions,
      include: [
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'customer_code'],
        },
        {
          model: models.CallStatus,
          as: 'status',
          attributes: ['id', 'name', 'category', 'color'],
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          attributes: ['id', 'first_name', 'last_name', 'employee_code'],
        },
      ],
      order: [['call_date', 'DESC']],
    });

    // Generate summary statistics
    const summary = {
      totalCalls: serviceCalls.length,
      byStatus: serviceCalls.reduce((acc, call) => {
        const status = call.status?.name || 'Unknown';
        acc[status] = (acc[status] || 0) + 1;
        return acc;
      }, {}),
      byPriority: serviceCalls.reduce((acc, call) => {
        acc[call.priority] = (acc[call.priority] || 0) + 1;
        return acc;
      }, {}),
      avgResolutionTime: 0, // This would need to be calculated based on actual resolution times
    };

    res.json({
      success: true,
      data: {
        serviceCalls: serviceCalls.map(call => ({
          id: call.id,
          callNumber: call.call_number,
          customer: call.customer,
          subject: call.subject,
          priority: call.priority,
          status: call.status,
          assignedExecutive: call.assignedExecutive ? {
            ...call.assignedExecutive.toJSON(),
            name: `${call.assignedExecutive.first_name || ''} ${call.assignedExecutive.last_name || ''}`.trim()
          } : null,
          callDate: call.call_date,
          createdAt: call.created_at,
        })),
        summary,
      },
    });
  } catch (error) {
    logger.error('Get service call reports error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate service call reports',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get sales reports
 */
export const getSalesReports = async (req, res) => {
  try {
    const {
      dateFrom,
      dateTo,
      salesExecutiveId,
      customerId,
      saleType,
      status,
    } = req.query;

    const tenantId = req.user.tenant.id;

    // Build where conditions
    const whereConditions = {
      tenant_id: tenantId,
    };

    if (salesExecutiveId) whereConditions.sales_executive_id = salesExecutiveId;
    if (customerId) whereConditions.customer_id = customerId;
    if (saleType) whereConditions.sale_type = saleType;
    if (status) whereConditions.status = status;

    // Date range filter
    if (dateFrom || dateTo) {
      whereConditions.sale_date = {};
      if (dateFrom) whereConditions.sale_date[Op.gte] = new Date(dateFrom);
      if (dateTo) whereConditions.sale_date[Op.lte] = new Date(dateTo);
    }

    const sales = await models.Sale.findAll({
      where: whereConditions,
      include: [
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'customer_code'],
        },
        {
          model: models.Executive,
          as: 'salesExecutive',
          attributes: ['id', 'first_name', 'last_name', 'employee_code'],
        },
      ],
      order: [['sale_date', 'DESC']],
    });

    // Generate summary statistics
    const totalRevenue = sales.reduce((sum, sale) => sum + parseFloat(sale.total_amount), 0);
    const totalPaid = sales.reduce((sum, sale) => sum + parseFloat(sale.paid_amount), 0);
    const totalOutstanding = sales.reduce((sum, sale) => sum + parseFloat(sale.balance_amount), 0);

    const summary = {
      totalSales: sales.length,
      totalRevenue,
      totalPaid,
      totalOutstanding,
      byType: sales.reduce((acc, sale) => {
        acc[sale.sale_type] = (acc[sale.sale_type] || 0) + 1;
        return acc;
      }, {}),
      byStatus: sales.reduce((acc, sale) => {
        acc[sale.status] = (acc[sale.status] || 0) + 1;
        return acc;
      }, {}),
    };

    res.json({
      success: true,
      data: {
        sales: sales.map(sale => ({
          id: sale.id,
          saleNumber: sale.sale_number,
          customer: sale.customer,
          salesExecutive: sale.salesExecutive ? {
            ...sale.salesExecutive.toJSON(),
            name: `${sale.salesExecutive.first_name || ''} ${sale.salesExecutive.last_name || ''}`.trim()
          } : null,
          saleType: sale.sale_type,
          saleDate: sale.sale_date,
          totalAmount: parseFloat(sale.total_amount),
          paidAmount: parseFloat(sale.paid_amount),
          balanceAmount: parseFloat(sale.balance_amount),
          status: sale.status,
          paymentStatus: sale.payment_status,
        })),
        summary,
      },
    });
  } catch (error) {
    logger.error('Get sales reports error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate sales reports',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get AMC reports
 */
export const getAmcReports = async (req, res) => {
  try {
    const {
      dateFrom,
      dateTo,
      customerId,
      status,
      renewalDateFrom,
      renewalDateTo,
    } = req.query;

    const tenantId = req.user.tenant.id;

    // Build where conditions
    const whereConditions = {
      '$customer.tenant_id$': tenantId,
    };

    if (customerId) whereConditions.customer_id = customerId;
    if (status) whereConditions.status = status;

    // Date range filter for start date
    if (dateFrom || dateTo) {
      whereConditions.start_date = {};
      if (dateFrom) whereConditions.start_date[Op.gte] = new Date(dateFrom);
      if (dateTo) whereConditions.start_date[Op.lte] = new Date(dateTo);
    }

    // Date range filter for renewal date
    if (renewalDateFrom || renewalDateTo) {
      whereConditions.renewal_date = {};
      if (renewalDateFrom) whereConditions.renewal_date[Op.gte] = new Date(renewalDateFrom);
      if (renewalDateTo) whereConditions.renewal_date[Op.lte] = new Date(renewalDateTo);
    }

    const amcs = await models.CustomerAMC.findAll({
      where: whereConditions,
      include: [
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'customer_code'],
          where: { tenant_id: tenantId },
        },
      ],
      order: [['start_date', 'DESC']],
    });

    // Generate summary statistics
    const totalValue = amcs.reduce((sum, amc) => sum + parseFloat(amc.contract_value), 0);
    const activeAmcs = amcs.filter(amc => amc.status === 'active').length;
    const expiringAmcs = amcs.filter(amc => {
      const renewalDate = new Date(amc.renewal_date);
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
      return renewalDate <= thirtyDaysFromNow && amc.status === 'active';
    }).length;

    const summary = {
      totalAmcs: amcs.length,
      activeAmcs,
      expiringAmcs,
      totalValue,
      byStatus: amcs.reduce((acc, amc) => {
        acc[amc.status] = (acc[amc.status] || 0) + 1;
        return acc;
      }, {}),
    };

    res.json({
      success: true,
      data: {
        amcs: amcs.map(amc => ({
          id: amc.id,
          customer: amc.customer,
          contractValue: parseFloat(amc.contract_value),
          startDate: amc.start_date,
          endDate: amc.end_date,
          renewalDate: amc.renewal_date,
          status: amc.status,
          numberOfVisits: amc.number_of_visits,
          visitedCount: amc.visited_count,
        })),
        summary,
      },
    });
  } catch (error) {
    logger.error('Get AMC reports error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate AMC reports',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Export reports to Excel
 */
export const exportReports = async (req, res) => {
  try {
    const { reportType, filters, format = 'xlsx' } = req.body;

    // This is a placeholder implementation
    // In a real application, you would use a library like ExcelJS or xlsx
    // to generate actual Excel files

    logger.info('Export report requested:', {
      reportType,
      filters,
      format,
      userId: req.user.id,
      tenantId: req.user.tenant.id,
    });

    res.json({
      success: true,
      message: 'Report export initiated',
      data: {
        downloadUrl: `/api/v1/reports/download/${reportType}-${Date.now()}.${format}`,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      },
    });
  } catch (error) {
    logger.error('Export reports error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export reports',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};