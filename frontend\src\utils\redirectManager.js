/**
 * Redirect Manager
 * Prevents multiple rapid navigation attempts that cause React Router throttling
 */

class RedirectManager {
  constructor() {
    this.isRedirecting = false;
    this.redirectTimeout = null;
  }

  // Check if a redirect is already in progress
  isRedirectInProgress() {
    return this.isRedirecting;
  }

  // Perform a safe redirect with throttling protection
  safeRedirect(navigate, path, options = {}) {
    if (this.isRedirecting) {
      console.log('Redirect already in progress, skipping...');
      return false;
    }

    this.isRedirecting = true;
    console.log(`Redirecting to: ${path}`);

    // Clear any existing timeout
    if (this.redirectTimeout) {
      clearTimeout(this.redirectTimeout);
    }

    // Perform redirect with small delay to prevent throttling
    this.redirectTimeout = setTimeout(() => {
      try {
        navigate(path, { replace: true, ...options });
      } catch (error) {
        console.error('Navigation error:', error);
      } finally {
        // Reset flag after navigation
        setTimeout(() => {
          this.isRedirecting = false;
        }, 100);
      }
    }, 50);

    return true;
  }

  // Reset the redirect state (useful for cleanup)
  reset() {
    this.isRedirecting = false;
    if (this.redirectTimeout) {
      clearTimeout(this.redirectTimeout);
      this.redirectTimeout = null;
    }
  }
}

// Create a singleton instance
export const redirectManager = new RedirectManager();
