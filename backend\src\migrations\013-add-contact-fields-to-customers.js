import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  // Add contact_person and designation columns to customers table
  await queryInterface.addColumn('customers', 'contact_person', {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Primary contact person name'
  });

  await queryInterface.addColumn('customers', 'designation', {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Designation of the contact person'
  });

  await queryInterface.addColumn('customers', 'alternate_phone', {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Alternate phone number'
  });

  // Add Tally-related fields that are missing
  await queryInterface.addColumn('customers', 'tally_version', {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Tally software version'
  });

  await queryInterface.addColumn('customers', 'tally_serial_number', {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Tally serial number'
  });

  await queryInterface.addColumn('customers', 'license_type', {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Tally license type'
  });

  await queryInterface.addColumn('customers', 'installation_date', {
    type: DataTypes.DATEONLY,
    allowNull: true,
    comment: 'Tally installation date'
  });

  // Add address field if missing (some schemas might have address_line_1 instead)
  try {
    await queryInterface.addColumn('customers', 'address', {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Complete address'
    });
  } catch (error) {
    // Column might already exist, ignore error
    console.log('Address column might already exist:', error.message);
  }

  // Add pincode field if missing (some schemas might have postal_code instead)
  try {
    await queryInterface.addColumn('customers', 'pincode', {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Postal/PIN code'
    });
  } catch (error) {
    // Column might already exist, ignore error
    console.log('Pincode column might already exist:', error.message);
  }

  console.log('✅ Added contact and Tally-related fields to customers table');
};

export const down = async (queryInterface) => {
  // Remove the added columns
  await queryInterface.removeColumn('customers', 'contact_person');
  await queryInterface.removeColumn('customers', 'designation');
  await queryInterface.removeColumn('customers', 'alternate_phone');
  await queryInterface.removeColumn('customers', 'tally_version');
  await queryInterface.removeColumn('customers', 'tally_serial_number');
  await queryInterface.removeColumn('customers', 'license_type');
  await queryInterface.removeColumn('customers', 'installation_date');
  
  try {
    await queryInterface.removeColumn('customers', 'address');
  } catch (error) {
    // Column might not exist, ignore error
  }
  
  try {
    await queryInterface.removeColumn('customers', 'pincode');
  } catch (error) {
    // Column might not exist, ignore error
  }

  console.log('✅ Removed contact and Tally-related fields from customers table');
};
