# API Connection Fix - ERR_EMPTY_RESPONSE

## 🚨 Issue Identified

**Error:** `POST http://localhost:3001/api/v1/auth/login net::ERR_EMPTY_RESPONSE`

**Root Cause:** The frontend is still trying to connect to `localhost:3001` instead of using the same-origin path in the Docker container.

## 🔍 Problem Analysis

1. **Wrong API URL**: Frontend is using `http://localhost:3001/api/v1/auth/login`
2. **Should be**: `/api/v1/auth/login` (relative path for same-origin)
3. **Cause**: `frontend/.env` file has `VITE_API_BASE_URL=http://localhost:3001/api/v1` which overrides the smart detection

## ✅ Fix Applied

**File:** `frontend/.env`

**Before:**
```env
VITE_API_BASE_URL=http://localhost:3001/api/v1
```

**After:**
```env
# VITE_API_BASE_URL=http://localhost:3001/api/v1  (commented out)
```

This allows the smart API URL detection to work properly.

## 🚀 Complete Fix Steps

### Step 1: Stop Current Container
```bash
docker stop tallycrmv1 && docker rm tallycrmv1
```

### Step 2: Rebuild Docker Image (IMPORTANT!)
```bash
# Rebuild with the updated frontend/.env file
docker build --no-cache -t cloudstier/tally-crm:v1 .
```

### Step 3: Run the Fixed Container
```bash
docker run -d -p 5372:8080 --name tallycrmv1 cloudstier/tally-crm:v1
```

### Step 4: Verify the Fix
```bash
# Check container logs
docker logs tallycrmv1

# Test health endpoint
curl http://localhost:5372/health

# Test API endpoint directly
curl http://localhost:5372/api/v1/health
```

### Step 5: Test in Browser
1. Open `http://localhost:5372`
2. Open browser console (F12)
3. Check the API URL being used:
```javascript
console.log('API Base URL:', window.location.origin + '/api/v1');
```

## 🔍 Verification Commands

### Check API URL in Container
```bash
# Check frontend .env file in container
docker exec tallycrmv1 cat /app/frontend/.env

# Should NOT contain VITE_API_BASE_URL or it should be commented out
```

### Test API Endpoints
```bash
# Test health endpoint
curl http://localhost:5372/health

# Test API health
curl http://localhost:5372/api/v1/health

# Test with verbose output
curl -v http://localhost:5372/api/v1/health
```

### Check Network Requests in Browser
1. Open `http://localhost:5372`
2. Open Developer Tools (F12)
3. Go to Network tab
4. Try to login
5. Check that API calls go to `/api/v1/auth/login` (relative) not `http://localhost:3001/api/v1/auth/login`

## 🎯 Expected Results

After the fix:

1. **API calls use relative paths**: `/api/v1/auth/login`
2. **No ERR_EMPTY_RESPONSE errors**
3. **Frontend loads properly** at `http://localhost:5372`
4. **Authentication works**

## 🚨 Troubleshooting

### If you still see localhost:3001 in API calls:

1. **Clear browser cache** completely
2. **Hard refresh** (Ctrl+Shift+R)
3. **Check if rebuild was successful**:
   ```bash
   docker exec tallycrmv1 cat /app/frontend/.env | grep VITE_API_BASE_URL
   # Should show commented line or no line
   ```

### If API still doesn't work:

1. **Check backend is running**:
   ```bash
   docker logs tallycrmv1 | grep "Server started"
   ```

2. **Test backend directly**:
   ```bash
   curl http://localhost:5372/api/v1/health
   ```

3. **Check database connection**:
   ```bash
   docker logs tallycrmv1 | grep -i "database\|db"
   ```

### If container won't start:

1. **Check build logs**:
   ```bash
   docker build --no-cache -t cloudstier/tally-crm:v1 . 2>&1 | tee build.log
   ```

2. **Check for errors**:
   ```bash
   docker logs tallycrmv1
   ```

## 🔧 Alternative Quick Fix

If rebuilding doesn't work, you can force the API URL:

### Option 1: Set Relative Path Explicitly
```bash
# Create a new frontend/.env with relative path
cat > frontend/.env << 'EOF'
VITE_API_BASE_URL=/api/v1
VITE_APP_NAME=TallyCRM
EOF

# Rebuild
docker build --no-cache -t cloudstier/tally-crm:v1 .
docker run -d -p 5372:8080 --name tallycrmv1 cloudstier/tally-crm:v1
```

### Option 2: Override at Runtime
```bash
# Run with environment variable override
docker run -d -p 5372:8080 --name tallycrmv1 \
  -e VITE_API_BASE_URL=/api/v1 \
  cloudstier/tally-crm:v1
```

## ✅ Success Indicators

- ✅ No `localhost:3001` in network requests
- ✅ API calls use `/api/v1/...` (relative paths)
- ✅ No ERR_EMPTY_RESPONSE errors
- ✅ Login/authentication works
- ✅ Backend logs show successful API requests

## 📝 Summary

The issue was that the frontend `.env` file was hardcoded to use `http://localhost:3001/api/v1`, which doesn't exist in the Docker container. By commenting out `VITE_API_BASE_URL`, the smart detection logic kicks in and uses relative paths (`/api/v1`) for same-origin requests.

**Key Point:** Always rebuild the Docker image after changing environment files!
