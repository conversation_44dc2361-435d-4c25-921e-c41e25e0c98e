import { apiService as api } from './api';

export const subscriptionService = {
  // Get all subscription plans
  getPlans: async () => {
    try {
      const response = await api.get('/subscription/plans');
      return response.data;
    } catch (error) {
      console.error('Error fetching subscription plans:', error);
      throw error;
    }
  },

  // Get current subscription
  getCurrentSubscription: async () => {
    try {
      const response = await api.get('/subscription/current');
      return response.data;
    } catch (error) {
      console.error('Error fetching current subscription:', error);
      throw error;
    }
  },

  // Create checkout session
  createCheckoutSession: async (planId, interval = 'monthly') => {
    try {
      const response = await api.post('/subscription/checkout', {
        planId,
        interval,
      });
      return response.data;
    } catch (error) {
      console.error('Error creating checkout session:', error);
      throw error;
    }
  },

  // Cancel subscription
  cancelSubscription: async (cancelAtPeriodEnd = true) => {
    try {
      const response = await api.post('/subscription/cancel', {
        cancelAtPeriodEnd,
      });
      return response.data;
    } catch (error) {
      console.error('Error canceling subscription:', error);
      throw error;
    }
  },

  // Reactivate subscription
  reactivateSubscription: async () => {
    try {
      const response = await api.post('/subscription/reactivate');
      return response.data;
    } catch (error) {
      console.error('Error reactivating subscription:', error);
      throw error;
    }
  },

  // Get subscription usage
  getUsage: async (months = 3) => {
    try {
      const response = await api.get(`/subscription/usage?months=${months}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching subscription usage:', error);
      throw error;
    }
  },
};

export const billingService = {
  // Get billing history
  getBillingHistory: async (page = 1, limit = 10) => {
    try {
      const response = await api.get(`/billing/history?page=${page}&limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching billing history:', error);
      throw error;
    }
  },

  // Get billing summary
  getBillingSummary: async (months = 12) => {
    try {
      const response = await api.get(`/billing/summary?months=${months}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching billing summary:', error);
      throw error;
    }
  },

  // Get invoice details
  getInvoice: async (invoiceId) => {
    try {
      const response = await api.get(`/billing/invoices/${invoiceId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching invoice:', error);
      throw error;
    }
  },

  // Download invoice
  downloadInvoice: async (invoiceId) => {
    try {
      const response = await api.get(`/billing/invoices/${invoiceId}/download`, {
        responseType: 'blob',
      });

      // Create blob link to download
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `invoice-${invoiceId}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      return { success: true };
    } catch (error) {
      console.error('Error downloading invoice:', error);
      throw error;
    }
  },

  // Get payment methods
  getPaymentMethods: async () => {
    try {
      const response = await api.get('/billing/payment-methods');
      return response.data;
    } catch (error) {
      console.error('Error fetching payment methods:', error);
      throw error;
    }
  },

  // Add payment method
  addPaymentMethod: async (paymentMethodId) => {
    try {
      const response = await api.post('/billing/payment-methods', {
        paymentMethodId,
      });
      return response.data;
    } catch (error) {
      console.error('Error adding payment method:', error);
      throw error;
    }
  },

  // Remove payment method
  removePaymentMethod: async (paymentMethodId) => {
    try {
      const response = await api.delete(`/billing/payment-methods/${paymentMethodId}`);
      return response.data;
    } catch (error) {
      console.error('Error removing payment method:', error);
      throw error;
    }
  },
};

// Utility functions
export const formatCurrency = (amount, currency = 'INR') => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount);
};

export const formatDate = (date) => {
  return new Intl.DateTimeFormat('en-IN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(new Date(date));
};

export const getSubscriptionStatusColor = (status) => {
  const statusColors = {
    trial: 'bg-blue-100 text-blue-800',
    active: 'bg-green-100 text-green-800',
    past_due: 'bg-yellow-100 text-yellow-800',
    canceled: 'bg-red-100 text-red-800',
    unpaid: 'bg-red-100 text-red-800',
    incomplete: 'bg-gray-100 text-gray-800',
    incomplete_expired: 'bg-gray-100 text-gray-800',
    paused: 'bg-gray-100 text-gray-800',
  };
  return statusColors[status] || 'bg-gray-100 text-gray-800';
};

export const getInvoiceStatusColor = (status) => {
  const statusColors = {
    draft: 'bg-gray-100 text-gray-800',
    open: 'bg-yellow-100 text-yellow-800',
    paid: 'bg-green-100 text-green-800',
    void: 'bg-red-100 text-red-800',
    uncollectible: 'bg-red-100 text-red-800',
  };
  return statusColors[status] || 'bg-gray-100 text-gray-800';
};

export const calculateUsagePercentage = (current, limit) => {
  if (!limit || limit === 0) return 0;
  return Math.min((current / limit) * 100, 100);
};

export const getUsageColor = (percentage) => {
  if (percentage >= 90) return 'text-red-600';
  if (percentage >= 75) return 'text-yellow-600';
  return 'text-green-600';
};

export const getUsageBarColor = (percentage) => {
  if (percentage >= 90) return 'bg-red-500';
  if (percentage >= 75) return 'bg-yellow-500';
  return 'bg-green-500';
};
