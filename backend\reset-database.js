import { Sequelize } from 'sequelize';
import 'dotenv/config';

const resetDatabase = async () => {
  console.log('🔧 Resetting database...');

  const sequelize = new Sequelize(
    process.env.DB_NAME,
    process.env.DB_USERNAME,
    process.env.DB_PASSWORD,
    {
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      dialect: 'postgres',
      logging: console.log,
      dialectOptions: {
        ssl: false
      }
    }
  );

  try {
    await sequelize.authenticate();
    console.log('✅ Connected to database');

    // Get all tables
    const [tables] = await sequelize.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_type = 'BASE TABLE'
      ORDER BY table_name;
    `);

    console.log('📊 Found tables:', tables.map(t => t.table_name));

    // Drop all tables
    for (const table of tables) {
      console.log(`🗑️  Dropping table: ${table.table_name}`);
      await sequelize.query(`DROP TABLE IF EXISTS "${table.table_name}" CASCADE;`);
    }

    // Also drop any remaining indexes
    const [indexes] = await sequelize.query(`
      SELECT indexname
      FROM pg_indexes
      WHERE schemaname = 'public'
      AND indexname NOT LIKE 'pg_%';
    `);

    for (const index of indexes) {
      console.log(`🗑️  Dropping index: ${index.indexname}`);
      await sequelize.query(`DROP INDEX IF EXISTS "${index.indexname}" CASCADE;`);
    }

    console.log('✅ All tables and indexes dropped successfully!');
    console.log('🎯 Database is now clean and ready for migrations');

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await sequelize.close();
  }
};

resetDatabase();
