import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Container, Button } from '../components/ui';
import { Helmet } from 'react-helmet-async';

const NotFound = () => {
  return (
    <>
      <Helmet>
        <title>Page Not Found - TallyCRM</title>
      </Helmet>

      <Container className="py-20">
        <div className="flex justify-center text-center">
          <div className="max-w-md">
            <div className="mb-6">
              <i className="bi bi-exclamation-triangle text-warning-500 text-8xl"></i>
            </div>

            <h1 className="text-6xl font-bold text-gray-900 mb-3">404</h1>
            <h2 className="text-2xl font-semibold text-gray-700 mb-3">Page Not Found</h2>
            <p className="text-gray-500 mb-6">
              The page you are looking for might have been removed, had its name changed,
              or is temporarily unavailable.
            </p>

            <div className="flex gap-2 justify-center">
              <Button as={Link} to="/" variant="primary">
                <i className="bi bi-house mr-2"></i>
                Go Home
              </Button>
              <Button variant="outline" onClick={() => window.history.back()}>
                <i className="bi bi-arrow-left mr-2"></i>
                Go Back
              </Button>
            </div>
          </div>
        </div>
      </Container>
    </>
  );
};

export default NotFound;
