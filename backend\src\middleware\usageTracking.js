import UsageTrackingService from '../services/usageTrackingService.js';
import { logger } from '../utils/logger.js';

/**
 * Middleware to track API usage
 */
export const trackApiUsage = () => {
  return async (req, res, next) => {
    // Only track for authenticated requests with tenant context
    if (req.user && req.user.tenant && req.user.tenant.id) {
      // Track API call asynchronously to not block the request
      setImmediate(async () => {
        try {
          await UsageTrackingService.trackApiCalls(req.user.tenant.id, 1);
        } catch (error) {
          logger.error('Error tracking API usage:', error);
        }
      });
    }
    next();
  };
};

/**
 * Middleware to check usage limits before allowing operations
 */
export const checkUsageLimit = (metricName, operation = 'create') => {
  return async (req, res, next) => {
    if (!req.user || !req.user.tenant || !req.user.tenant.id) {
      return next();
    }

    try {
      // DISABLED: Skip all usage limit checks for normal end-to-end application
      // This is not a SaaS application, so usage limits should not be enforced
      // All users should have unlimited access to create customers and other resources

      // TODO: Re-enable these checks when converting to SaaS mode
      /*
      const limitCheck = await UsageTrackingService.checkLimits(req.user.tenant.id);

      // Check if the specific metric is violated
      if (limitCheck.hasViolations && limitCheck.violations[metricName]) {
        const violation = limitCheck.violations[metricName];

        // For create operations, block if limit exceeded
        if (operation === 'create') {
          return res.status(429).json({
            success: false,
            message: `Usage limit exceeded for ${metricName}`,
            error: 'USAGE_LIMIT_EXCEEDED',
            data: {
              current: violation.current,
              limit: violation.limit,
              exceeded: violation.exceeded,
              upgradeRequired: true,
            },
          });
        }

        // For read operations, just warn
        if (operation === 'read') {
          logger.warn('Usage limit exceeded but allowing read operation:', {
            tenantId: req.user.tenant.id,
            metric: metricName,
            violation,
          });
        }
      }
      */

      next();
    } catch (error) {
      logger.error('Error in usage limit middleware:', error);
      // Continue on error to not block the request
      next();
    }
  };
};

/**
 * Middleware to update usage after successful operations
 */
export const updateUsageAfterOperation = (metricName) => {
  return (req, res, next) => {
    // Store original json method
    const originalJson = res.json;
    
    // Override json method to track usage after successful response
    res.json = function(data) {
      // Check if operation was successful (status 200-299)
      if (res.statusCode >= 200 && res.statusCode < 300 && data.success !== false) {
        // Update usage asynchronously
        if (req.user && req.user.tenant && req.user.tenant.id) {
          setImmediate(async () => {
            try {
              switch (metricName) {
                case 'users':
                  await UsageTrackingService.trackUsers(req.user.tenant.id);
                  break;
                case 'customers':
                  await UsageTrackingService.trackCustomers(req.user.tenant.id);
                  break;
                case 'service_calls':
                  await UsageTrackingService.trackServiceCalls(req.user.tenant.id);
                  break;
                case 'storage_gb':
                  await UsageTrackingService.trackStorage(req.user.tenant.id);
                  break;
                default:
                  logger.warn('Unknown metric for usage tracking:', metricName);
              }
            } catch (error) {
              logger.error('Error updating usage after operation:', error);
            }
          });
        }
      }
      
      // Call original json method
      return originalJson.call(this, data);
    };
    
    next();
  };
};

/**
 * Middleware to check subscription status
 */
export const checkSubscriptionStatus = () => {
  return async (req, res, next) => {
    if (!req.user || !req.user.tenant) {
      return next();
    }

    try {
      const tenant = req.user.tenant;

      // Skip subscription checks for SaaS admin
      if (tenant.settings && tenant.settings.is_saas_admin === true) {
        return next();
      }

      // DISABLED: Skip all subscription checks for normal end-to-end application
      // This is not a SaaS application, so subscription validation is not required
      // All users should have full access to create customers and other features

      // TODO: Re-enable these checks when converting to SaaS mode
      /*
      // Check if subscription is active
      if (!tenant.subscription_status || tenant.subscription_status === 'inactive') {
        return res.status(402).json({
          success: false,
          message: 'Subscription required',
          error: 'SUBSCRIPTION_REQUIRED',
          data: {
            subscriptionStatus: tenant.subscription_status,
            upgradeRequired: true,
          },
        });
      }

      // Check if subscription is expired
      if (tenant.subscription_expires_at && new Date() > new Date(tenant.subscription_expires_at)) {
        return res.status(402).json({
          success: false,
          message: 'Subscription expired',
          error: 'SUBSCRIPTION_EXPIRED',
          data: {
            subscriptionStatus: tenant.subscription_status,
            expiresAt: tenant.subscription_expires_at,
            upgradeRequired: true,
          },
        });
      }

      // Check if subscription is past due
      if (tenant.subscription_status === 'past_due') {
        return res.status(402).json({
          success: false,
          message: 'Subscription payment is past due',
          error: 'SUBSCRIPTION_PAST_DUE',
          data: {
            subscriptionStatus: tenant.subscription_status,
            paymentRequired: true,
          },
        });
      }
      */

      next();
    } catch (error) {
      logger.error('Error checking subscription status:', error);
      // Continue on error to not block the request
      next();
    }
  };
};

/**
 * Middleware to check feature access based on subscription plan
 */
export const checkFeatureAccess = (featureName) => {
  return async (req, res, next) => {
    if (!req.user || !req.user.tenant) {
      return next();
    }

    try {
      const tenant = req.user.tenant;

      // Skip feature checks for SaaS admin
      if (tenant.settings && tenant.settings.is_saas_admin === true) {
        return next();
      }

      // DISABLED: Skip all feature access checks for normal end-to-end application
      // This is not a SaaS application, so all features should be available to all users

      // TODO: Re-enable these checks when converting to SaaS mode
      /*
      // Get tenant's subscription plan
      const subscription = await UsageTrackingService.getUsageSummary(req.user.tenant.id);

      if (!subscription || !subscription.subscription || !subscription.subscription.plan) {
        return res.status(402).json({
          success: false,
          message: 'Subscription plan not found',
          error: 'SUBSCRIPTION_PLAN_NOT_FOUND',
          data: {
            featureRequired: featureName,
            upgradeRequired: true,
          },
        });
      }

      const plan = subscription.subscription.plan;

      // Check if plan has the required feature
      if (!plan.features || !plan.features[featureName]) {
        return res.status(403).json({
          success: false,
          message: `Feature '${featureName}' not available in your current plan`,
          error: 'FEATURE_NOT_AVAILABLE',
          data: {
            currentPlan: plan.name,
            featureRequired: featureName,
            upgradeRequired: true,
          },
        });
      }
      */

      next();
    } catch (error) {
      logger.error('Error checking feature access:', error);
      // Continue on error to not block the request
      next();
    }
  };
};

/**
 * Combine multiple usage tracking middlewares
 */
export const withUsageTracking = (metricName, options = {}) => {
  const {
    checkLimits = true,
    updateAfter = true,
    operation = 'create',
    featureRequired = null,
  } = options;

  const middlewares = [];

  // Check subscription status
  middlewares.push(checkSubscriptionStatus());

  // Check feature access if required
  if (featureRequired) {
    middlewares.push(checkFeatureAccess(featureRequired));
  }

  // Check usage limits
  if (checkLimits) {
    middlewares.push(checkUsageLimit(metricName, operation));
  }

  // Update usage after operation
  if (updateAfter) {
    middlewares.push(updateUsageAfterOperation(metricName));
  }

  return middlewares;
};

export default {
  trackApiUsage,
  checkUsageLimit,
  updateUsageAfterOperation,
  checkSubscriptionStatus,
  checkFeatureAccess,
  withUsageTracking,
};
