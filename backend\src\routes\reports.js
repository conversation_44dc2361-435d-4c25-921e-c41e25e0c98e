import express from 'express';
import { body, query } from 'express-validator';
import { validateRequest as validate } from '../middleware/validation.js';
import { authenticateToken, requirePermission, requireTenantAccess } from '../middleware/auth.js';
import {
  getCustomerReports,
  getServiceCallReports,
  getSalesReports,
  getAmcReports,
  exportReports,
} from '../controllers/reportsController.js';

const router = express.Router();

// Apply authentication and tenant access to all routes
router.use(authenticateToken);
router.use(requireTenantAccess);

/**
 * @route   GET /api/reports/customers
 * @desc    Get customer reports
 * @access  Private (requires reports.read permission)
 */
router.get('/customers', [
  requirePermission('reports.read'),
  query('dateFrom')
    .optional()
    .isISO8601()
    .withMessage('Date from must be a valid date'),
  query('dateTo')
    .optional()
    .isISO8601()
    .withMessage('Date to must be a valid date'),
  query('customerType')
    .optional()
    .isIn(['prospect', 'customer', 'inactive', 'blacklisted'])
    .withMessage('Invalid customer type'),
  query('industryId')
    .optional()
    .isUUID()
    .withMessage('Industry ID must be a valid UUID'),
  query('areaId')
    .optional()
    .isUUID()
    .withMessage('Area ID must be a valid UUID'),
  query('assignedExecutiveId')
    .optional()
    .isUUID()
    .withMessage('Assigned executive ID must be a valid UUID'),
  validate,
], getCustomerReports);

/**
 * @route   GET /api/reports/service-calls
 * @desc    Get service call reports
 * @access  Private (requires reports.read permission)
 */
router.get('/service-calls', [
  requirePermission('reports.read'),
  query('dateFrom')
    .optional()
    .isISO8601()
    .withMessage('Date from must be a valid date'),
  query('dateTo')
    .optional()
    .isISO8601()
    .withMessage('Date to must be a valid date'),
  query('statusId')
    .optional()
    .isUUID()
    .withMessage('Status ID must be a valid UUID'),
  query('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Invalid priority'),
  query('assignedTo')
    .optional()
    .isUUID()
    .withMessage('Assigned to must be a valid UUID'),
  query('customerId')
    .optional()
    .isUUID()
    .withMessage('Customer ID must be a valid UUID'),
  validate,
], getServiceCallReports);

/**
 * @route   GET /api/reports/sales
 * @desc    Get sales reports
 * @access  Private (requires reports.read permission)
 */
router.get('/sales', [
  requirePermission('reports.read'),
  query('dateFrom')
    .optional()
    .isISO8601()
    .withMessage('Date from must be a valid date'),
  query('dateTo')
    .optional()
    .isISO8601()
    .withMessage('Date to must be a valid date'),
  query('salesExecutiveId')
    .optional()
    .isUUID()
    .withMessage('Sales executive ID must be a valid UUID'),
  query('customerId')
    .optional()
    .isUUID()
    .withMessage('Customer ID must be a valid UUID'),
  query('saleType')
    .optional()
    .isIn(['new', 'renewal', 'upgrade', 'additional'])
    .withMessage('Invalid sale type'),
  query('status')
    .optional()
    .isIn(['draft', 'confirmed', 'delivered', 'cancelled'])
    .withMessage('Invalid status'),
  validate,
], getSalesReports);

/**
 * @route   GET /api/reports/amc
 * @desc    Get AMC reports
 * @access  Private (requires reports.read permission)
 */
router.get('/amc', [
  requirePermission('reports.read'),
  query('dateFrom')
    .optional()
    .isISO8601()
    .withMessage('Date from must be a valid date'),
  query('dateTo')
    .optional()
    .isISO8601()
    .withMessage('Date to must be a valid date'),
  query('customerId')
    .optional()
    .isUUID()
    .withMessage('Customer ID must be a valid UUID'),
  query('status')
    .optional()
    .isIn(['active', 'expired', 'cancelled', 'suspended'])
    .withMessage('Invalid status'),
  query('renewalDateFrom')
    .optional()
    .isISO8601()
    .withMessage('Renewal date from must be a valid date'),
  query('renewalDateTo')
    .optional()
    .isISO8601()
    .withMessage('Renewal date to must be a valid date'),
  validate,
], getAmcReports);

/**
 * @route   POST /api/reports/export
 * @desc    Export reports to Excel
 * @access  Private (requires reports.export permission)
 */
router.post('/export', [
  requirePermission('reports.export'),
  body('reportType')
    .isIn(['customers', 'service-calls', 'sales', 'amc'])
    .withMessage('Invalid report type'),
  body('filters')
    .optional()
    .isObject()
    .withMessage('Filters must be an object'),
  body('format')
    .optional()
    .isIn(['xlsx', 'csv', 'pdf'])
    .withMessage('Invalid format'),
  validate,
], exportReports);

export default router;
