import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const Referral = sequelize.define('Referral', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    customer_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'customers',
        key: 'id',
      },
    },
    referred_customer_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'customers',
        key: 'id',
      },
    },
    referral_code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      comment: 'Unique referral code',
    },
    referred_company_name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 200],
      },
    },
    referred_contact_person: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 100],
      },
    },
    referred_phone: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [10, 15],
      },
    },
    referred_email: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isEmail: true,
      },
    },
    referred_address: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    referral_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    status: {
      type: DataTypes.ENUM('pending', 'contacted', 'interested', 'converted', 'rejected', 'lost'),
      allowNull: false,
      defaultValue: 'pending',
    },
    first_contact_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    conversion_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    sale_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'sales',
        key: 'id',
      },
    },
    sale_amount: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    referral_commission_percentage: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    referral_commission_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    commission_paid: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    commission_paid_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    assigned_executive_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'executives',
        key: 'id',
      },
    },
    follow_up_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    rejection_reason: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    tags: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Array of tags for categorization',
    },
    created_by: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'referrals',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['customer_id'],
      },
      {
        fields: ['referred_customer_id'],
      },
      {
        fields: ['referral_code'],
        unique: true,
      },
      {
        fields: ['status'],
      },
      {
        fields: ['referral_date'],
      },
      {
        fields: ['conversion_date'],
      },
      {
        fields: ['commission_paid'],
      },
      {
        fields: ['assigned_executive_id'],
      },
      {
        fields: ['follow_up_date'],
      },
      {
        fields: ['created_by'],
      },
    ],
  });

  // Instance methods
  Referral.prototype.isConverted = function() {
    return this.status === 'converted';
  };

  Referral.prototype.isPending = function() {
    return this.status === 'pending';
  };

  Referral.prototype.calculateCommissionAmount = function() {
    if (this.referral_commission_percentage > 0 && this.sale_amount > 0) {
      return (parseFloat(this.sale_amount) * parseFloat(this.referral_commission_percentage)) / 100;
    }
    return parseFloat(this.referral_commission_amount) || 0;
  };

  Referral.prototype.getDaysFromReferral = function() {
    const referralDate = new Date(this.referral_date);
    const today = new Date();
    const diffTime = today - referralDate;
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  Referral.prototype.getDaysToConversion = function() {
    if (!this.conversion_date) return null;
    const referralDate = new Date(this.referral_date);
    const conversionDate = new Date(this.conversion_date);
    const diffTime = conversionDate - referralDate;
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  Referral.prototype.isFollowUpDue = function() {
    if (!this.follow_up_date) return false;
    return new Date(this.follow_up_date) <= new Date();
  };

  Referral.prototype.getStatusColor = function() {
    const colors = {
      pending: '#6c757d',
      contacted: '#17a2b8',
      interested: '#ffc107',
      converted: '#28a745',
      rejected: '#dc3545',
      lost: '#fd7e14',
    };
    return colors[this.status] || '#6c757d';
  };

  // Class methods
  Referral.generateReferralCode = function() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = 'REF';
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  // Associations
  Referral.associate = function(models) {
    Referral.belongsTo(models.Customer, {
      foreignKey: 'customer_id',
      as: 'referringCustomer',
    });

    Referral.belongsTo(models.Customer, {
      foreignKey: 'referred_customer_id',
      as: 'referredCustomer',
    });

    Referral.belongsTo(models.Sale, {
      foreignKey: 'sale_id',
      as: 'sale',
    });

    Referral.belongsTo(models.Executive, {
      foreignKey: 'assigned_executive_id',
      as: 'assignedExecutive',
    });

    Referral.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator',
    });

    Referral.hasMany(models.Sale, {
      foreignKey: 'referral_id',
      as: 'sales',
    });
  };

  return Referral;
}
