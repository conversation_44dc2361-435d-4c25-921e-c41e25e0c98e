import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { apiService } from '../../services/api';
import themeManager from '../../utils/themeManager';
import {
  FaPlus,
  FaEdit,
  FaEye,
  FaTrash,
  FaSearch,
  FaFilter,
  FaDownload,
  FaUser,
  FaCalendar,
  FaClock,
  FaRupeeSign,
  FaTools
} from 'react-icons/fa';
import LoadingScreen from '../../components/ui/LoadingScreen';

const ServiceList = () => {
  const navigate = useNavigate();
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [servicesPerPage] = useState(10);
  const [currentThemeColor, setCurrentThemeColor] = useState('#7c3aed');

  // Get current theme color
  useEffect(() => {
    const savedTheme = localStorage.getItem('primaryColor') || '#7c3aed';
    setCurrentThemeColor(savedTheme);
  }, []);

  // Fetch services data from API - Combined useEffect with proper dependencies
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchServicesData();
    }, searchTerm ? 500 : 0); // Debounce only for search, immediate for other changes

    return () => clearTimeout(timeoutId);
  }, [searchTerm, filterStatus, filterType, currentPage]); // Removed duplicate useEffect

  const fetchServicesData = async () => {
    try {
      setLoading(true);
      const response = await apiService.get('/service-calls', {
        params: {
          page: currentPage,
          limit: servicesPerPage,
          ...(searchTerm && { search: searchTerm }),
          ...(filterStatus !== 'all' && { status: filterStatus }),
          ...(filterType !== 'all' && { serviceType: filterType }),
        }
      });

      if (response.data?.success) {
        const servicesData = response.data.data.serviceCalls || [];
        // Transform API data to match frontend expectations
        const transformedServices = servicesData.map(service => ({
          id: service.id,
          serviceNumber: service.callNumber || `SRV-${service.id}`,
          customer: service.customer?.company_name || service.customer?.display_name || 'Unknown Customer',
          customerId: service.customer?.id,
          contactPerson: service.customer?.contact_person || 'N/A',
          type: service.serviceType || 'Support',
          description: service.description || 'No description available',
          priority: service.priority || 'medium',
          status: service.status || 'pending',
          assignedTo: service.assignedTechnician?.name || 'Unassigned',
          createdDate: service.createdAt,
          scheduledDate: service.scheduledDate || service.createdAt,
          completedDate: service.completedDate,
          estimatedHours: service.estimatedHours || 0,
          actualHours: service.actualHours || 0,
          amount: service.serviceCharge || 0,
          location: service.customer?.city && service.customer?.state ?
                   `${service.customer.city}, ${service.customer.state}` : 'N/A'
        }));

        setServices(transformedServices);
      } else {
        console.error('Failed to fetch services data:', response.data?.message);
        setServices([]);
      }
    } catch (error) {
      console.error('Error fetching services data:', error);
      toast.error('Failed to load services data');
      setServices([]);
    } finally {
      setLoading(false);
    }
  };

  // Filter services based on search, status, and type
  const filteredServices = services.filter(service => {
    const matchesSearch = service.serviceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         service.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         service.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         service.assignedTo.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || service.status === filterStatus;
    const matchesType = filterType === 'all' || service.type === filterType;
    return matchesSearch && matchesStatus && matchesType;
  });

  // Pagination
  const indexOfLastService = currentPage * servicesPerPage;
  const indexOfFirstService = indexOfLastService - servicesPerPage;
  const currentServices = filteredServices.slice(indexOfFirstService, indexOfLastService);
  const totalPages = Math.ceil(filteredServices.length / servicesPerPage);

  const handleDelete = async (serviceId) => {
    if (window.confirm('Are you sure you want to delete this service request?')) {
      try {
        const response = await apiService.delete(`/service-calls/${serviceId}`);
        if (response.data?.success) {
          setServices(services.filter(service => service.id !== serviceId));
          toast.success('Service request deleted successfully');
        } else {
          toast.error(response.data?.message || 'Failed to delete service request');
        }
      } catch (error) {
        console.error('Error deleting service:', error);
        toast.error('Failed to delete service request');
      }
    }
  };

  const getStatusBadge = (status) => {
    // Handle both string and object status
    const statusValue = typeof status === 'object' && status !== null ? status.name || status.status || 'unknown' : status || 'unknown';
    const statusString = String(statusValue).toLowerCase();

    const badgeConfig = {
      'completed': { bg: 'bg-gradient-to-r from-green-100 to-emerald-100', text: 'text-green-800', icon: '✅' },
      'in-progress': { bg: 'bg-gradient-to-r from-blue-100 to-cyan-100', text: 'text-blue-800', icon: '🔄' },
      'scheduled': { bg: 'bg-gradient-to-r from-yellow-100 to-orange-100', text: 'text-yellow-800', icon: '📅' },
      'pending': { bg: 'bg-gradient-to-r from-gray-100 to-slate-100', text: 'text-gray-800', icon: '⏳' },
      'cancelled': { bg: 'bg-gradient-to-r from-red-100 to-pink-100', text: 'text-red-800', icon: '❌' },
      'open': { bg: 'bg-gradient-to-r from-purple-100 to-indigo-100', text: 'text-purple-800', icon: '🔓' }
    };

    const config = badgeConfig[statusString] || badgeConfig['pending'];
    const displayText = statusString.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());

    return (
      <span className={`inline-flex items-center px-3 py-1.5 text-xs font-bold rounded-full shadow-sm ${config.bg} ${config.text} border border-opacity-20`}>
        <span className="mr-1">{config.icon}</span>
        {displayText}
      </span>
    );
  };

  const getPriorityBadge = (priority) => {
    const priorityValue = typeof priority === 'object' && priority !== null ? priority.name || priority.priority || 'medium' : priority || 'medium';
    const priorityString = String(priorityValue).toLowerCase();

    const badgeConfig = {
      'high': { bg: 'bg-gradient-to-r from-red-100 to-pink-100', text: 'text-red-800', icon: '🔥' },
      'critical': { bg: 'bg-gradient-to-r from-red-200 to-red-100', text: 'text-red-900', icon: '🚨' },
      'medium': { bg: 'bg-gradient-to-r from-yellow-100 to-amber-100', text: 'text-yellow-800', icon: '⚡' },
      'low': { bg: 'bg-gradient-to-r from-green-100 to-emerald-100', text: 'text-green-800', icon: '🌱' }
    };

    const config = badgeConfig[priorityString] || badgeConfig['medium'];
    const displayText = priorityString.charAt(0).toUpperCase() + priorityString.slice(1);

    return (
      <span className={`inline-flex items-center px-3 py-1.5 text-xs font-bold rounded-full shadow-sm ${config.bg} ${config.text} border border-opacity-20`}>
        <span className="mr-1">{config.icon}</span>
        {displayText}
      </span>
    );
  };

  const getServiceTypeIcon = (type) => {
    const typeConfig = {
      'Installation': { icon: FaTools, color: 'text-blue-600', bg: 'bg-blue-100', emoji: '🔧' },
      'Support': { icon: FaUser, color: 'text-green-600', bg: 'bg-green-100', emoji: '🛠️' },
      'Training': { icon: FaUser, color: 'text-purple-600', bg: 'bg-purple-100', emoji: '🎓' },
      'Maintenance': { icon: FaTools, color: 'text-orange-600', bg: 'bg-orange-100', emoji: '⚙️' },
      'Consultation': { icon: FaUser, color: 'text-indigo-600', bg: 'bg-indigo-100', emoji: '💡' }
    };

    const config = typeConfig[type] || typeConfig['Support'];
    const IconComponent = config.icon;

    return (
      <div className={`inline-flex items-center px-2 py-1 rounded-lg ${config.bg}`}>
        <span className="mr-1 text-sm">{config.emoji}</span>
        <IconComponent className={`${config.color} h-4 w-4`} />
      </div>
    );
  };

  const handleExport = () => {
    try {
      // Prepare data for export
      const exportData = filteredServices.map(service => ({
        'Service Number': service.serviceNumber,
        'Customer': service.customer,
        'Contact Person': service.contactPerson,
        'Type': service.type,
        'Description': service.description,
        'Assigned To': service.assignedTo,
        'Priority': service.priority,
        'Status': service.status,
        'Scheduled Date': new Date(service.scheduledDate).toLocaleDateString(),
        'Amount': service.amount
      }));

      // Convert to CSV
      const headers = Object.keys(exportData[0] || {});
      const csvContent = [
        headers.join(','),
        ...exportData.map(row =>
          headers.map(header =>
            `"${(row[header] || '').toString().replace(/"/g, '""')}"`
          ).join(',')
        )
      ].join('\n');

      // Download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `services_export_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success(`Exported ${exportData.length} services successfully`);
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export services');
    }
  };

  if (loading) {
    return (
      <LoadingScreen
        title="Loading Services..."
        subtitle="Fetching service requests and tracking progress"
        variant="page"
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100">
      <div className="w-full">
        {/* Colorful Header */}
        <div className="mb-8">
          <div className="header-gradient rounded-2xl shadow-xl p-6 text-white">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-2 flex items-center">
                  <div className="w-12 h-12 rounded-full flex items-center justify-center mr-4" style={{ backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)' }}>
                    <FaTools className="text-xl" style={{ color: 'var(--primary-text, #ffffff)' }} />
                  </div>
                  Service Management
                </h2>
                <p className="text-sm sm:text-base" style={{ color: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)' }}>Manage service requests and track progress efficiently</p>
              </div>
              <div className="flex gap-3">
                <button
                  className="inline-flex items-center px-6 py-3 border-2 text-sm font-medium rounded-xl focus:outline-none focus:ring-2 transition-all duration-200 backdrop-blur-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{
                    borderColor: themeManager.getContrastingTextColor(currentThemeColor),
                    color: themeManager.getContrastingTextColor(currentThemeColor),
                    backgroundColor: `${themeManager.getContrastingTextColor(currentThemeColor)}20`,
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.backgroundColor = `${themeManager.getContrastingTextColor(currentThemeColor)}30`;
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.backgroundColor = `${themeManager.getContrastingTextColor(currentThemeColor)}20`;
                  }}
                  onClick={handleExport}
                  disabled={filteredServices.length === 0}
                >
                  <FaDownload className="mr-2 h-4 w-4" />
                  Export ({filteredServices.length})
                </button>
                <Link
                  to="/services/add"
                  className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-xl hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-all duration-200 shadow-lg"
                  style={{
                    backgroundColor: themeManager.getContrastingTextColor(currentThemeColor),
                    color: currentThemeColor
                  }}
                >
                  <FaPlus className="mr-2 h-4 w-4" />
                  New Service Request
                </Link>
              </div>
            </div>
          </div>
        </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="stats-card-primary rounded-lg shadow-lg hover:shadow-xl transition-all duration-300">
          <div className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="text-xl sm:text-2xl font-bold mb-0">{services.length}</h4>
                <p className="mb-0 text-sm" style={{ color: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)' }}>Total Services</p>
              </div>
              <div className="rounded-lg p-3" style={{ backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)' }}>
                <FaTools className="h-6 w-6" style={{ color: 'var(--primary-text, #ffffff)' }} />
              </div>
            </div>
            <div className="mt-3 flex items-center">
              <div className="bg-green-500/20 text-green-300 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                <i className="bi bi-arrow-up mr-1"></i>
                +18%
              </div>
              <span className="text-xs ml-2" style={{ color: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.7)' }}>vs last month</span>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
          <div className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="text-xl sm:text-2xl font-bold mb-0 text-gray-900">{services.filter(s => s.status === 'in-progress').length}</h4>
                <p className="text-gray-600 mb-0 text-sm">In Progress</p>
              </div>
              <div className="white-stats-icon-bg rounded-lg p-3">
                <FaClock className="h-6 w-6 white-stats-icon-text" />
              </div>
            </div>
            <div className="mt-3 flex items-center">
              <div className="bg-blue-100 text-blue-600 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                <i className="bi bi-arrow-up mr-1"></i>
                +5%
              </div>
              <span className="text-gray-500 text-xs ml-2">vs last month</span>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
          <div className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="text-xl sm:text-2xl font-bold mb-0 text-gray-900">{services.filter(s => s.status === 'scheduled').length}</h4>
                <p className="text-gray-600 mb-0 text-sm">Scheduled</p>
              </div>
              <div className="white-stats-icon-bg rounded-lg p-3">
                <FaCalendar className="h-6 w-6 white-stats-icon-text" />
              </div>
            </div>
            <div className="mt-3 flex items-center">
              <div className="bg-orange-100 text-orange-600 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                <i className="bi bi-arrow-up mr-1"></i>
                +12%
              </div>
              <span className="text-gray-500 text-xs ml-2">vs last month</span>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
          <div className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="text-xl sm:text-2xl font-bold mb-0 text-gray-900">{services.filter(s => s.status === 'completed').length}</h4>
                <p className="text-gray-600 mb-0 text-sm">Completed</p>
              </div>
              <div className="white-stats-icon-bg rounded-lg p-3">
                <FaUser className="h-6 w-6 white-stats-icon-text" />
              </div>
            </div>
            <div className="mt-3 flex items-center">
              <div className="bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                <i className="bi bi-arrow-up mr-1"></i>
                +22%
              </div>
              <span className="text-gray-500 text-xs ml-2">vs last month</span>
            </div>
          </div>
        </div>
      </div>

        {/* Enhanced Filters */}
        <div className="bg-white rounded-2xl shadow-xl p-6 mb-8 border border-purple-100">
          <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
            <div className="md:col-span-4">
              <label className="block text-sm font-bold text-purple-700 mb-2">🔍 Search Services</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <FaSearch className="h-5 w-5 text-purple-400" />
                </div>
                <input
                  type="text"
                  className="block w-full pl-12 pr-4 py-3 border-2 border-purple-200 rounded-xl leading-5 bg-white placeholder-purple-400 focus:outline-none focus:placeholder-purple-300 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 sm:text-sm"
                  placeholder="Search by service number, customer..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div className="md:col-span-3">
              <label className="block text-sm font-bold text-blue-700 mb-2">📊 Status Filter</label>
              <select
                className="block w-full px-4 py-3 border-2 border-blue-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white hover:border-blue-300 transition-all duration-200 sm:text-sm"
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <option value="all">🌟 All Status</option>
                <option value="pending">⏳ Pending</option>
                <option value="scheduled">📅 Scheduled</option>
                <option value="in-progress">🔄 In Progress</option>
                <option value="completed">✅ Completed</option>
                <option value="cancelled">❌ Cancelled</option>
              </select>
            </div>
            <div className="md:col-span-3">
              <label className="block text-sm font-bold text-green-700 mb-2">🛠️ Service Type</label>
              <select
                className="block w-full px-4 py-3 border-2 border-green-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white hover:border-green-300 transition-all duration-200 sm:text-sm"
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
              >
                <option value="all">🌈 All Types</option>
                <option value="Installation">🔧 Installation</option>
                <option value="Support">🛠️ Support</option>
                <option value="Training">🎓 Training</option>
                <option value="Maintenance">⚙️ Maintenance</option>
              </select>
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-bold text-orange-700 mb-2">⚡ Actions</label>
              <button className="w-full inline-flex items-center justify-center px-4 py-3 border-2 border-orange-200 text-sm font-medium rounded-xl text-orange-700 bg-white hover:bg-orange-50 hover:border-orange-300 focus:outline-none focus:ring-2 focus:ring-orange-500 transition-all duration-200">
                <FaFilter className="mr-2 h-4 w-4" />
                More Filters
              </button>
            </div>
          </div>
        </div>

        {/* Enhanced Service Table */}
        <div className="bg-white shadow-2xl rounded-2xl overflow-hidden border border-gray-100">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="header-gradient">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-bold uppercase tracking-wider" style={{ color: 'var(--primary-text, #ffffff)' }}>🔢 Service #</th>
                  <th className="px-6 py-4 text-left text-xs font-bold uppercase tracking-wider" style={{ color: 'var(--primary-text, #ffffff)' }}>👤 Customer</th>
                  <th className="px-6 py-4 text-left text-xs font-bold uppercase tracking-wider" style={{ color: 'var(--primary-text, #ffffff)' }}>🛠️ Type</th>
                  <th className="px-6 py-4 text-left text-xs font-bold uppercase tracking-wider" style={{ color: 'var(--primary-text, #ffffff)' }}>📝 Description</th>
                  <th className="px-6 py-4 text-left text-xs font-bold uppercase tracking-wider" style={{ color: 'var(--primary-text, #ffffff)' }}>👨‍💼 Assigned To</th>
                  <th className="px-6 py-4 text-left text-xs font-bold uppercase tracking-wider" style={{ color: 'var(--primary-text, #ffffff)' }}>⚡ Priority</th>
                  <th className="px-6 py-4 text-left text-xs font-bold uppercase tracking-wider" style={{ color: 'var(--primary-text, #ffffff)' }}>📊 Status</th>
                  <th className="px-6 py-4 text-left text-xs font-bold uppercase tracking-wider" style={{ color: 'var(--primary-text, #ffffff)' }}>📅 Scheduled Date</th>
                  <th className="px-6 py-4 text-left text-xs font-bold uppercase tracking-wider" style={{ color: 'var(--primary-text, #ffffff)' }}>💰 Amount</th>
                  <th className="px-6 py-4 text-left text-xs font-bold uppercase tracking-wider" style={{ color: 'var(--primary-text, #ffffff)' }}>⚙️ Actions</th>
                </tr>
              </thead>
              <tbody className="bg-gradient-to-br from-white to-purple-50 divide-y divide-purple-100">
                {currentServices.map((service, index) => (
                  <tr key={service.id} className={`hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50 transition-all duration-200 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-25'}`}>
                    <td className="px-6 py-5 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-8 h-8 dashboard-icon-bg rounded-full flex items-center justify-center mr-3">
                          <span className="dashboard-icon-text font-bold text-xs">#</span>
                        </div>
                        <span className="text-sm font-bold text-gray-900">{service.serviceNumber}</span>
                      </div>
                    </td>
                    <td className="px-6 py-5 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-10 h-10 dashboard-icon-bg rounded-full flex items-center justify-center mr-3">
                          <span className="dashboard-icon-text font-bold text-sm">{service.customer?.charAt(0) || 'C'}</span>
                        </div>
                        <div>
                          <div className="text-sm font-bold text-gray-900">{service.customer}</div>
                          <div className="text-sm dashboard-icon-text">{service.contactPerson}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-5 whitespace-nowrap">
                      <div className="flex items-center">
                        {getServiceTypeIcon(service.type)}
                        <span className="ml-3 text-sm font-medium text-gray-900">{service.type}</span>
                      </div>
                    </td>
                    <td className="px-6 py-5">
                      <div className="max-w-xs">
                        <span className="text-sm text-gray-700 leading-relaxed" title={service.description}>
                          {service.description?.length > 50
                            ? `${service.description.substring(0, 50)}...`
                            : service.description || 'No description'}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-5 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-8 h-8 dashboard-icon-bg rounded-full flex items-center justify-center mr-2">
                          <span className="dashboard-icon-text font-bold text-xs">{service.assignedTo?.charAt(0) || 'A'}</span>
                        </div>
                        <span className="text-sm font-medium text-gray-900">{service.assignedTo || 'Unassigned'}</span>
                      </div>
                    </td>
                    <td className="px-6 py-5 whitespace-nowrap">{getPriorityBadge(service.priority)}</td>
                    <td className="px-6 py-5 whitespace-nowrap">{getStatusBadge(service.status)}</td>
                    <td className="px-6 py-5 whitespace-nowrap">
                      <div className="flex items-center bg-gradient-to-r from-orange-50 to-yellow-50 px-3 py-2 rounded-lg">
                        <FaCalendar className="text-orange-500 mr-2 h-4 w-4" />
                        <span className="text-sm font-medium text-orange-700">{new Date(service.scheduledDate).toLocaleDateString()}</span>
                      </div>
                    </td>
                    <td className="px-6 py-5 whitespace-nowrap">
                      <div className="flex items-center bg-gradient-to-r from-green-50 to-emerald-50 px-3 py-2 rounded-lg">
                        <FaRupeeSign className="text-green-500 mr-1 h-4 w-4" />
                        <span className="text-sm font-bold text-green-700">₹{service.amount?.toLocaleString() || '0'}</span>
                      </div>
                    </td>
                    <td className="px-6 py-5 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          className="p-2 text-blue-600 hover:text-blue-800 bg-blue-50 hover:bg-blue-100 rounded-lg transition-all duration-200 shadow-sm"
                          onClick={() => navigate(`/services/${service.id}`)}
                          title="View Details"
                        >
                          <FaEye className="h-4 w-4" />
                        </button>
                        <button
                          className="p-2 text-green-600 hover:text-green-800 bg-green-50 hover:bg-green-100 rounded-lg transition-all duration-200 shadow-sm"
                          onClick={() => navigate(`/services/${service.id}/edit`)}
                          title="Edit"
                        >
                          <FaEdit className="h-4 w-4" />
                        </button>
                        <button
                          className="p-2 text-red-600 hover:text-red-800 bg-red-50 hover:bg-red-100 rounded-lg transition-all duration-200 shadow-sm"
                          onClick={() => handleDelete(service.id)}
                          title="Delete"
                        >
                          <FaTrash className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
            </tbody>
          </table>
        </div>

          {/* Enhanced Pagination */}
          {totalPages > 1 && (
            <div className="bg-gradient-to-r from-purple-50 to-indigo-50 px-6 py-4 border-t border-purple-200">
              <div className="flex items-center justify-between">
                <div className="flex-1 flex justify-between sm:hidden">
                  <button
                    onClick={() => setCurrentPage(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-4 py-2 border-2 border-purple-300 text-sm font-medium rounded-xl text-purple-700 bg-white hover:bg-purple-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="ml-3 relative inline-flex items-center px-4 py-2 border-2 border-purple-300 text-sm font-medium rounded-xl text-purple-700 bg-white hover:bg-purple-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    Next
                  </button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm font-medium text-purple-700">
                      📄 Showing page <span className="font-bold text-purple-800">{currentPage}</span> of{' '}
                      <span className="font-bold text-purple-800">{totalPages}</span>
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex rounded-xl shadow-lg -space-x-px" aria-label="Pagination">
                      <button
                        onClick={() => setCurrentPage(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="relative inline-flex items-center px-3 py-2 rounded-l-xl border-2 border-purple-300 bg-white text-sm font-medium text-purple-600 hover:bg-purple-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                      >
                        Previous
                      </button>
                      {[...Array(totalPages)].map((_, index) => (
                        <button
                          key={index}
                          onClick={() => setCurrentPage(index + 1)}
                          className={`relative inline-flex items-center px-4 py-2 border-2 text-sm font-bold transition-all duration-200 ${
                            currentPage === index + 1
                              ? 'z-10 bg-gradient-to-r from-purple-500 to-indigo-500 border-purple-500 text-white shadow-lg'
                              : 'bg-white border-purple-300 text-purple-600 hover:bg-purple-50'
                          }`}
                        >
                          {index + 1}
                        </button>
                      ))}
                      <button
                        onClick={() => setCurrentPage(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="relative inline-flex items-center px-3 py-2 rounded-r-xl border-2 border-purple-300 bg-white text-sm font-medium text-purple-600 hover:bg-purple-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                      >
                        Next
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ServiceList;
