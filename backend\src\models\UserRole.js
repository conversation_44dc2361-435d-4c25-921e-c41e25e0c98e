import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const UserRole = sequelize.define('UserRole', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    role_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'roles',
        key: 'id',
      },
    },
    assigned_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    assigned_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    expires_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Optional expiration date for temporary role assignments',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'user_roles',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['user_id', 'role_id'],
        unique: true,
      },
      {
        fields: ['user_id'],
      },
      {
        fields: ['role_id'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['expires_at'],
      },
    ],
  });

  // Instance methods
  UserRole.prototype.isExpired = function() {
    return this.expires_at && this.expires_at < new Date();
  };

  UserRole.prototype.isValid = function() {
    return this.is_active && !this.isExpired();
  };

  // Associations
  UserRole.associate = function(models) {
    UserRole.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user',
    });

    UserRole.belongsTo(models.Role, {
      foreignKey: 'role_id',
      as: 'role',
    });

    UserRole.belongsTo(models.User, {
      foreignKey: 'assigned_by',
      as: 'assignedBy',
    });
  };

  return UserRole;
}
