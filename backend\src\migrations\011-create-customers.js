import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  await queryInterface.createTable('customers', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    customer_code: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    company_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    display_name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    industry_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'industries',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    area_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'areas',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    assigned_executive_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'executives',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    customer_type: {
      type: DataTypes.ENUM('prospect', 'customer', 'inactive', 'blacklisted'),
      allowNull: false,
      defaultValue: 'prospect',
    },
    business_type: {
      type: DataTypes.ENUM('proprietorship', 'partnership', 'private_limited', 'public_limited', 'llp', 'trust', 'society', 'other'),
      allowNull: true,
    },
    address_line_1: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    address_line_2: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    city: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    state: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    country: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'India',
    },
    postal_code: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    website: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    gst_number: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    pan_number: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    annual_turnover: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
    },
    employee_count: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    credit_limit: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    credit_days: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0,
    },
    payment_terms: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    bank_name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    bank_account_number: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    bank_ifsc_code: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    coordinates: {
      type: DataTypes.JSONB,
      allowNull: true,
    },
    lead_source: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    referred_by: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    first_contact_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    last_contact_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    next_follow_up_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    tags: {
      type: DataTypes.JSONB,
      defaultValue: [],
    },
    custom_fields: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    created_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes
  await queryInterface.addIndex('customers', ['tenant_id']);
  await queryInterface.addIndex('customers', ['tenant_id', 'customer_code'], { unique: true });
  await queryInterface.addIndex('customers', ['company_name']);
  await queryInterface.addIndex('customers', ['customer_type']);
  await queryInterface.addIndex('customers', ['industry_id']);
  await queryInterface.addIndex('customers', ['area_id']);
  await queryInterface.addIndex('customers', ['assigned_executive_id']);
  await queryInterface.addIndex('customers', ['gst_number']);
  await queryInterface.addIndex('customers', ['pan_number']);
  await queryInterface.addIndex('customers', ['is_active']);
  await queryInterface.addIndex('customers', ['created_by']);
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('customers');
};
