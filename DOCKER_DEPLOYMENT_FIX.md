# Docker Deployment Fix Guide

## 🚨 Current Issue Analysis

Your command: `docker run -d -p 5372:3004 --name tallycrmv1 cloudstier/tally-crm:v1`

**Problems identified:**
1. **Wrong port mapping**: `5372:3004` should be `5372:8080` (container runs on port 8080)
2. **Missing environment variables**: Container needs database connection and other config
3. **No database**: TallyCRM requires PostgreSQL and Redis to function

## ✅ Solution 1: Quick Fix (Single Container)

### Step 1: Stop and remove existing container
```bash
docker stop tallycrmv1 2>/dev/null || true
docker rm tallycrmv1 2>/dev/null || true
```

### Step 2: Create environment file
```bash
# Create .env.prod file with required variables
cat > .env.prod << 'EOF'
NODE_ENV=production
PORT=8080
DB_HOST=your_database_host
DB_PORT=5432
DB_NAME=tallycrm_prod
DB_USERNAME=postgres
DB_PASSWORD=your_secure_password
DB_DIALECT=postgres
JWT_SECRET=your_very_secure_jwt_secret_32_chars_min
JWT_REFRESH_SECRET=your_very_secure_refresh_secret_32_chars_min
ENABLE_CORS=false
ENABLE_RATE_LIMITING=true
APP_URL=http://localhost:5372
EOF
```

### Step 3: Run with correct command
```bash
# Option A: Using environment file
docker run -d -p 5372:8080 --name tallycrmv1 --env-file .env.prod cloudstier/tally-crm:v1

# Option B: Using individual environment variables
docker run -d -p 5372:8080 --name tallycrmv1 \
  -e NODE_ENV=production \
  -e PORT=8080 \
  -e DB_HOST=your_database_host \
  -e DB_PORT=5432 \
  -e DB_NAME=tallycrm_prod \
  -e DB_USERNAME=postgres \
  -e DB_PASSWORD=your_secure_password \
  -e JWT_SECRET=your_very_secure_jwt_secret_32_chars_min \
  -e JWT_REFRESH_SECRET=your_very_secure_refresh_secret_32_chars_min \
  cloudstier/tally-crm:v1
```

## ✅ Solution 2: Complete Setup with Database (Recommended)

### Step 1: Create docker-compose.yml for production
```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: tallycrm-postgres
    restart: always
    environment:
      POSTGRES_DB: tallycrm_prod
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: your_secure_postgres_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    container_name: tallycrm-redis
    restart: always
    command: redis-server --requirepass your_secure_redis_password
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  app:
    image: cloudstier/tally-crm:v1
    container_name: tallycrmv1
    restart: always
    environment:
      NODE_ENV: production
      PORT: 8080
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: tallycrm_prod
      DB_USERNAME: postgres
      DB_PASSWORD: your_secure_postgres_password
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: your_secure_redis_password
      JWT_SECRET: your_very_secure_jwt_secret_32_chars_min
      JWT_REFRESH_SECRET: your_very_secure_refresh_secret_32_chars_min
      ENABLE_CORS: false
      APP_URL: http://localhost:5372
    ports:
      - "5372:8080"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads

volumes:
  postgres_data:
  redis_data:
```

### Step 2: Run the complete setup
```bash
# Start all services
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f app
```

## 🔧 Troubleshooting Steps

### 1. Check container logs
```bash
docker logs tallycrmv1
```

### 2. Check if container is running
```bash
docker ps -a | grep tallycrmv1
```

### 3. Test application health
```bash
curl http://localhost:5372/health
```

### 4. Check environment variables inside container
```bash
docker exec tallycrmv1 env | grep -E "(NODE_ENV|PORT|DB_)"
```

### 5. Check if files exist in container
```bash
docker exec tallycrmv1 ls -la /app/
docker exec tallycrmv1 ls -la /app/backend/
docker exec tallycrmv1 cat /app/backend/.env
```

## 🚀 Quick Commands to Fix Your Current Issue

```bash
# 1. Stop current container
docker stop tallycrmv1 && docker rm tallycrmv1

# 2. Create environment file
cat > .env.prod << 'EOF'
NODE_ENV=production
PORT=8080
DB_HOST=host.docker.internal
DB_PORT=5432
DB_NAME=tallycrm_prod
DB_USERNAME=postgres
DB_PASSWORD=postgres123
JWT_SECRET=your_jwt_secret_must_be_at_least_32_characters_long_for_security
JWT_REFRESH_SECRET=your_refresh_secret_must_be_at_least_32_characters_long
ENABLE_CORS=false
APP_URL=http://localhost:5372
EOF

# 3. Run with correct port and environment
docker run -d -p 5372:8080 --name tallycrmv1 --env-file .env.prod cloudstier/tally-crm:v1

# 4. Check logs
docker logs -f tallycrmv1

# 5. Test access
curl http://localhost:5372/health
```

## 📝 Environment Variables Required

**Essential variables:**
- `NODE_ENV=production`
- `PORT=8080` (container internal port)
- `DB_HOST` (database host)
- `DB_PASSWORD` (database password)
- `JWT_SECRET` (minimum 32 characters)

**Database connection:**
- If database is on host: `DB_HOST=host.docker.internal`
- If database is external: `DB_HOST=your_db_server_ip`

## 🔍 Verification Steps

After running the container:

1. **Check container status:**
   ```bash
   docker ps | grep tallycrmv1
   ```

2. **Check application logs:**
   ```bash
   docker logs tallycrmv1
   ```

3. **Test health endpoint:**
   ```bash
   curl http://localhost:5372/health
   ```

4. **Test frontend access:**
   ```bash
   curl http://localhost:5372/
   ```

The application should now be accessible at `http://localhost:5372`
