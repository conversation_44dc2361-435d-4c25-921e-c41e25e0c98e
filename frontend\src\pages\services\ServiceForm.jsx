import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { apiService } from '../../services/api';
import { FaSave, FaTimes, FaUser, FaTools, FaCalendar, FaRupeeSign } from 'react-icons/fa';

const ServiceForm = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = Boolean(id);

  // Helper function to map service types
  const mapServiceType = (type) => {
    const mapping = {
      'Installation': 'onsite',
      'Support': 'online',
      'Training': 'onsite',
      'Maintenance': 'onsite',
      'Remote Support': 'online',
      'Phone Support': 'phone'
    };
    return mapping[type] || 'online';
  };

  // Reverse mapping function for edit mode
  const reverseMapServiceType = (callType) => {
    const reverseMapping = {
      'onsite': 'Installation',
      'online': 'Support',
      'phone': 'Phone Support'
    };
    return reverseMapping[callType] || 'Support';
  };

  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState([]);
  const [technicians, setTechnicians] = useState([]);
  const [formData, setFormData] = useState({
    // Service Information
    serviceNumber: '',
    customerId: '',
    type: '',
    category: '',
    description: '',
    priority: 'medium',
    status: 'pending',

    // Assignment
    assignedTo: '',

    // Scheduling
    scheduledDate: '',
    scheduledTime: '',
    estimatedHours: '',

    // Location
    serviceLocation: 'customer-site',
    address: '',
    city: '',
    state: '',

    // Financial
    amount: '',
    currency: 'INR',
    paymentStatus: 'pending',

    // Additional Details
    requirements: '',
    notes: '',

    // Customer Contact
    contactPerson: '',
    contactPhone: '',
    contactEmail: ''
  });

  const [errors, setErrors] = useState({});

  // Fetch data for dropdowns and edit mode
  useEffect(() => {
    fetchCustomers();
    fetchTechnicians();

    if (isEdit) {
      fetchService();
    } else {
      // Generate service number for new services
      const serviceNumber = `SRV-${new Date().getFullYear()}-${String(Date.now()).slice(-3)}`;
      setFormData(prev => ({ ...prev, serviceNumber }));
    }
  }, [isEdit, id]);

  const fetchCustomers = async () => {
    try {
      const response = await apiService.get('/customers');
      if (response.data?.success && response.data?.data?.customers) {
        setCustomers(response.data.data.customers.map(customer => ({
          id: customer.id,
          name: customer.company_name,
          contactPerson: customer.contact_person
        })));
      }
    } catch (error) {
      console.error('Error fetching customers:', error);
      setCustomers([]);
    }
  };

  const fetchTechnicians = async () => {
    try {
      const response = await apiService.get('/executives');
      if (response.data?.success && response.data?.data?.executives) {
        setTechnicians(response.data.data.executives.map(executive => ({
          id: executive.id,
          name: `${executive.first_name} ${executive.last_name}`,
          specialization: executive.department || 'Support'
        })));
      }
    } catch (error) {
      console.error('Error fetching technicians:', error);
      // Fallback to mock data with UUID-like IDs
      setTechnicians([
        { id: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890', name: 'Raj Kumar', specialization: 'Installation' },
        { id: 'b2c3d4e5-f6g7-8901-bcde-f23456789012', name: 'Priya Sharma', specialization: 'Support' },
        { id: 'c3d4e5f6-g7h8-9012-cdef-************', name: 'Amit Singh', specialization: 'Training' },
        { id: 'd4e5f6g7-h8i9-0123-def0-************', name: 'Vikash Gupta', specialization: 'Maintenance' }
      ]);
    }
  };

  const fetchService = async () => {
    try {
      setLoading(true);
      const response = await apiService.get(`/service-calls/${id}`);
      if (response.data?.success && response.data?.data) {
        const service = response.data.data;
        setFormData({
          serviceNumber: service.call_number || `SRV-${new Date().getFullYear()}-${String(Date.now()).slice(-3)}`,
          customerId: service.customer_id || '',
          type: reverseMapServiceType(service.call_type) || '',
          category: service.category || '',
          priority: service.priority ? service.priority.charAt(0).toUpperCase() + service.priority.slice(1) : 'Medium',
          status: service.status || 'open',
          description: service.description || service.subject || '',
          assignedTo: service.assigned_to || '',
          scheduledDate: service.scheduled_date ? service.scheduled_date.split('T')[0] : '',
          scheduledTime: service.scheduled_time || '',
          estimatedHours: service.estimated_hours || '',
          serviceLocation: service.service_location || 'onsite',
          address: service.address || '',
          city: service.city || '',
          state: service.state || '',
          amount: service.service_charges || '',
          currency: service.currency || 'INR',
          paymentStatus: service.payment_status || 'pending',
          requirements: service.customer_reported_issue || '',
          notes: service.internal_notes || '',
          contactPerson: service.contact_person || '',
          contactPhone: service.contact_phone || '',
          contactEmail: service.contact_email || ''
        });
      }
    } catch (error) {
      console.error('Error fetching service:', error);
      toast.error('Failed to load service data');
      navigate('/services');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }

    // Auto-fill customer details when customer is selected
    if (name === 'customerId' && value) {
      const selectedCustomer = customers.find(c => c.id === value);
      if (selectedCustomer) {
        setFormData(prev => ({
          ...prev,
          contactPerson: selectedCustomer.contactPerson
        }));
      }
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Required fields
    if (!formData.customerId) newErrors.customerId = 'Customer is required';
    if (!formData.type.trim()) newErrors.type = 'Service type is required';
    if (!formData.description.trim()) newErrors.description = 'Description is required';
    if (!formData.scheduledDate) newErrors.scheduledDate = 'Scheduled date is required';
    if (!formData.assignedTo) newErrors.assignedTo = 'Technician assignment is required';
    if (!formData.amount.trim()) newErrors.amount = 'Amount is required';

    // Amount validation
    if (formData.amount && (isNaN(formData.amount) || parseFloat(formData.amount) <= 0)) {
      newErrors.amount = 'Please enter a valid amount';
    }

    // Hours validation
    if (formData.estimatedHours && (isNaN(formData.estimatedHours) || parseFloat(formData.estimatedHours) <= 0)) {
      newErrors.estimatedHours = 'Please enter valid estimated hours';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    setLoading(true);

    try {
      // Prepare data for API - only include non-empty values
      const serviceData = {};

      // Required fields
      if (formData.customerId) serviceData.customer_id = formData.customerId;
      if (formData.description) {
        serviceData.subject = formData.description.substring(0, 200); // Use description as subject
        serviceData.description = formData.description;
      }

      // Optional fields - only include if they have values
      if (formData.assignedTo && formData.assignedTo !== '') {
        serviceData.assigned_to = formData.assignedTo;
        console.log('🔍 Setting assigned_to:', { value: formData.assignedTo, type: typeof formData.assignedTo });
      } else {
        console.log('🔍 assigned_to is empty, not including in request');
      }
      if (formData.type) serviceData.call_type = mapServiceType(formData.type);
      if (formData.priority) serviceData.priority = formData.priority.toLowerCase();
      if (formData.scheduledDate) serviceData.scheduled_date = formData.scheduledDate;
      if (formData.estimatedHours) serviceData.estimated_hours = parseFloat(formData.estimatedHours);
      if (formData.amount) serviceData.service_charges = parseFloat(formData.amount);

      // Map other fields
      if (formData.requirements) serviceData.customer_reported_issue = formData.requirements;
      if (formData.notes) serviceData.internal_notes = formData.notes;

      console.log('📤 Sending service data to API:', serviceData);

      let response;
      if (isEdit) {
        response = await apiService.put(`/service-calls/${id}`, serviceData);
      } else {
        response = await apiService.post('/service-calls', serviceData);
      }

      if (response.data?.success) {
        toast.success(isEdit ? 'Service request updated successfully' : 'Service request created successfully');
        navigate('/services');
      } else {
        toast.error(response.data?.message || 'Failed to save service request');
      }
    } catch (error) {
      console.error('Error saving service request:', error);
      toast.error(error.response?.data?.message || 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/services');
  };

  if (loading && isEdit) {
    return (
      <div className="flex justify-center items-center" style={{ height: '400px' }}>
        <div className="animate-spin rounded-full border-2 border-gray-300 border-t-current text-primary-600" role="status">
          <span className="sr-only">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-100">
      <div className="w-full">
        {/* Colorful Header */}
        <div className="mb-8">
          <div className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 rounded-2xl shadow-xl p-6 text-white">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-3xl font-bold mb-2 flex items-center">
                  <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-4">
                    <FaTools className="text-white text-xl" />
                  </div>
                  {isEdit ? 'Edit Service Request' : 'New Service Request'}
                </h2>
                <p className="text-green-100 text-lg">
                  {isEdit ? 'Update service request details and assignments' : 'Create a new service request for customer support'}
                </p>
              </div>
              <div className="flex gap-3">
                <button
                  type="button"
                  className="inline-flex items-center px-6 py-3 border-2 border-white border-opacity-30 text-sm font-medium rounded-xl text-white bg-white bg-opacity-10 hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-all duration-200 backdrop-blur-sm"
                  onClick={handleCancel}
                >
                  <FaTimes className="mr-2 h-4 w-4" />
                  Cancel
                </button>
                <button
                  type="submit"
                  form="serviceForm"
                  className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-xl text-green-600 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-all duration-200 shadow-lg"
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-green-600 mr-2"></div>
                      {isEdit ? 'Updating...' : 'Creating...'}
                    </>
                  ) : (
                    <>
                      <FaSave className="mr-2 h-4 w-4" />
                      {isEdit ? 'Update Service' : 'Create Service'}
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Form */}
        <form id="serviceForm" onSubmit={handleSubmit} className="space-y-8">
          {/* Service Information */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden border border-green-100">
            <div className="bg-gradient-to-r from-green-500 to-emerald-500 px-6 py-4">
              <h3 className="text-xl font-bold text-white flex items-center">
                <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                  <FaTools className="h-4 w-4 text-white" />
                </div>
                Service Information
              </h3>
            </div>
            <div className="p-6 bg-gradient-to-br from-white to-green-50">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Service Number */}
                <div>
                  <label className="block text-sm font-bold text-green-700 mb-2">Service Number</label>
                  <input
                    type="text"
                    className="block w-full px-4 py-3 border-2 border-green-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-gray-50 transition-all duration-200 sm:text-sm"
                    name="serviceNumber"
                    value={formData.serviceNumber}
                    readOnly
                    placeholder="Auto-generated"
                  />
                </div>

                {/* Customer */}
                <div>
                  <label className="block text-sm font-bold text-blue-700 mb-2">Customer *</label>
                  <select
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.customerId
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-blue-200 focus:ring-blue-500 focus:border-blue-500 bg-white hover:border-blue-300'
                    }`}
                    name="customerId"
                    value={formData.customerId}
                    onChange={handleInputChange}
                  >
                    <option value="">Select customer</option>
                    {customers.map(customer => (
                      <option key={customer.id} value={customer.id}>
                        {customer.name}
                      </option>
                    ))}
                  </select>
                  {errors.customerId && <p className="mt-2 text-sm text-red-600 font-medium">{errors.customerId}</p>}
                </div>

                {/* Service Type */}
                <div>
                  <label className="block text-sm font-bold text-purple-700 mb-2">Service Type *</label>
                  <select
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.type
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-purple-200 focus:ring-purple-500 focus:border-purple-500 bg-white hover:border-purple-300'
                    }`}
                    name="type"
                    value={formData.type}
                    onChange={handleInputChange}
                  >
                    <option value="">Select service type</option>
                    <option value="Installation">🔧 Installation</option>
                    <option value="Support">🛠️ Support</option>
                    <option value="Training">📚 Training</option>
                    <option value="Maintenance">⚙️ Maintenance</option>
                    <option value="Consultation">💡 Consultation</option>
                    <option value="Data Migration">📊 Data Migration</option>
                  </select>
                  {errors.type && <p className="mt-2 text-sm text-red-600 font-medium">{errors.type}</p>}
                </div>

                {/* Priority */}
                <div>
                  <label className="block text-sm font-bold text-orange-700 mb-2">Priority</label>
                  <select
                    className="block w-full px-4 py-3 border-2 border-orange-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white hover:border-orange-300 transition-all duration-200 sm:text-sm"
                    name="priority"
                    value={formData.priority}
                    onChange={handleInputChange}
                  >
                    <option value="low">🟢 Low</option>
                    <option value="medium">🟡 Medium</option>
                    <option value="high">🟠 High</option>
                    <option value="urgent">🔴 Urgent</option>
                  </select>
                </div>

                {/* Status */}
                <div>
                  <label className="block text-sm font-bold text-indigo-700 mb-2">Status</label>
                  <select
                    className="block w-full px-4 py-3 border-2 border-indigo-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white hover:border-indigo-300 transition-all duration-200 sm:text-sm"
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                  >
                    <option value="pending">⏳ Pending</option>
                    <option value="scheduled">📅 Scheduled</option>
                    <option value="in-progress">🔄 In Progress</option>
                    <option value="completed">✅ Completed</option>
                    <option value="cancelled">❌ Cancelled</option>
                  </select>
                </div>

                {/* Description */}
                <div className="md:col-span-3">
                  <label className="block text-sm font-bold text-gray-700 mb-2">Description *</label>
                  <textarea
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 ${
                      errors.description
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-gray-200 focus:ring-gray-500 focus:border-gray-500 bg-white hover:border-gray-300'
                    }`}
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows="3"
                    placeholder="Describe the service requirements in detail"
                  ></textarea>
                  {errors.description && <p className="mt-2 text-sm text-red-600 font-medium">{errors.description}</p>}
                </div>
              </div>
            </div>
          </div>

          {/* Assignment & Scheduling */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden border border-blue-100">
            <div className="bg-gradient-to-r from-blue-500 to-cyan-500 px-6 py-4">
              <h3 className="text-xl font-bold text-white flex items-center">
                <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                  <FaUser className="h-4 w-4 text-white" />
                </div>
                Assignment & Scheduling
              </h3>
            </div>
            <div className="p-6 bg-gradient-to-br from-white to-blue-50">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Assigned Technician */}
                <div className="md:col-span-3">
                  <label className="block text-sm font-bold text-blue-700 mb-2">Assigned Technician *</label>
                  <select
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.assignedTo
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-blue-200 focus:ring-blue-500 focus:border-blue-500 bg-white hover:border-blue-300'
                    }`}
                    name="assignedTo"
                    value={formData.assignedTo}
                    onChange={handleInputChange}
                  >
                    <option value="">Select technician</option>
                    {technicians.map(tech => (
                      <option key={tech.id} value={tech.id}>
                        {tech.name} ({tech.specialization})
                      </option>
                    ))}
                  </select>
                  {errors.assignedTo && <p className="mt-2 text-sm text-red-600 font-medium">{errors.assignedTo}</p>}
                </div>

                {/* Scheduled Date */}
                <div>
                  <label className="block text-sm font-bold text-green-700 mb-2">Scheduled Date *</label>
                  <input
                    type="date"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.scheduledDate
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-green-200 focus:ring-green-500 focus:border-green-500 bg-white hover:border-green-300'
                    }`}
                    name="scheduledDate"
                    value={formData.scheduledDate}
                    onChange={handleInputChange}
                    min={new Date().toISOString().split('T')[0]}
                  />
                  {errors.scheduledDate && <p className="mt-2 text-sm text-red-600 font-medium">{errors.scheduledDate}</p>}
                </div>

                {/* Scheduled Time */}
                <div>
                  <label className="block text-sm font-bold text-purple-700 mb-2">Scheduled Time</label>
                  <input
                    type="time"
                    className="block w-full px-4 py-3 border-2 border-purple-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white hover:border-purple-300 transition-all duration-200 sm:text-sm"
                    name="scheduledTime"
                    value={formData.scheduledTime}
                    onChange={handleInputChange}
                  />
                </div>

                {/* Estimated Hours */}
                <div>
                  <label className="block text-sm font-bold text-orange-700 mb-2">Estimated Hours</label>
                  <input
                    type="number"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.estimatedHours
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-orange-200 focus:ring-orange-500 focus:border-orange-500 bg-white hover:border-orange-300'
                    }`}
                    name="estimatedHours"
                    value={formData.estimatedHours}
                    onChange={handleInputChange}
                    placeholder="0"
                    min="0"
                    step="0.5"
                  />
                  {errors.estimatedHours && <p className="mt-2 text-sm text-red-600 font-medium">{errors.estimatedHours}</p>}
                </div>

                {/* Service Amount */}
                <div>
                  <label className="block text-sm font-bold text-teal-700 mb-2">Service Amount *</label>
                  <input
                    type="number"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.amount
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-teal-200 focus:ring-teal-500 focus:border-teal-500 bg-white hover:border-teal-300'
                    }`}
                    name="amount"
                    value={formData.amount}
                    onChange={handleInputChange}
                    placeholder="₹ 0"
                    min="0"
                    step="0.01"
                  />
                  {errors.amount && <p className="mt-2 text-sm text-red-600 font-medium">{errors.amount}</p>}
                </div>

                {/* Service Location */}
                <div>
                  <label className="block text-sm font-bold text-indigo-700 mb-2">Service Location</label>
                  <select
                    className="block w-full px-4 py-3 border-2 border-indigo-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white hover:border-indigo-300 transition-all duration-200 sm:text-sm"
                    name="serviceLocation"
                    value={formData.serviceLocation}
                    onChange={handleInputChange}
                  >
                    <option value="customer-site">🏢 Customer Site</option>
                    <option value="office">🏬 Our Office</option>
                    <option value="remote">💻 Remote</option>
                  </select>
                </div>

                {/* Additional Notes */}
                <div className="md:col-span-3">
                  <label className="block text-sm font-bold text-gray-700 mb-2">Additional Notes</label>
                  <textarea
                    className="block w-full px-4 py-3 border-2 border-gray-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-gray-500 bg-white hover:border-gray-300 transition-all duration-200"
                    name="notes"
                    value={formData.notes}
                    onChange={handleInputChange}
                    rows="3"
                    placeholder="Any additional notes or special instructions"
                  ></textarea>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ServiceForm;
