import nodemailer from 'nodemailer';
import { logger } from './logger.js';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Email utility functions
 */

// Create transporter
let transporter = null;

const createTransporter = () => {
  if (transporter) return transporter;

  const config = {
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT) || 587,
    secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  };

  // For development, use ethereal email
  if (process.env.NODE_ENV === 'development' && !process.env.SMTP_HOST) {
    logger.info('Using Ethereal Email for development');
    // You can create an account at https://ethereal.email/
    config.host = 'smtp.ethereal.email';
    config.port = 587;
    config.auth = {
      user: '<EMAIL>',
      pass: 'ethereal.pass',
    };
  }

  transporter = nodemailer.createTransporter(config);

  // Verify connection
  transporter.verify((error, success) => {
    if (error) {
      logger.error('SMTP connection error:', error);
    } else {
      logger.info('SMTP server is ready to send emails');
    }
  });

  return transporter;
};

/**
 * Email templates
 */
const emailTemplates = {
  'email-verification': {
    subject: 'Verify your email address - TallyCRM',
    template: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
          <h1 style="color: #007bff; margin: 0;">TallyCRM</h1>
        </div>
        <div style="padding: 30px 20px;">
          <h2 style="color: #333;">Welcome to TallyCRM, {{firstName}}!</h2>
          <p style="color: #666; line-height: 1.6;">
            Thank you for registering with TallyCRM. To complete your registration and start using our platform, 
            please verify your email address by clicking the button below.
          </p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{verificationLink}}" 
               style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Verify Email Address
            </a>
          </div>
          <p style="color: #666; line-height: 1.6;">
            If the button doesn't work, you can copy and paste this link into your browser:
          </p>
          <p style="color: #007bff; word-break: break-all;">{{verificationLink}}</p>
          <p style="color: #666; line-height: 1.6;">
            This verification link will expire in 24 hours for security reasons.
          </p>
          <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
          <p style="color: #999; font-size: 12px;">
            If you didn't create an account with TallyCRM, please ignore this email.
          </p>
        </div>
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 12px;">
          <p>© 2024 TallyCRM by Cloudstier Solutions. All rights reserved.</p>
        </div>
      </div>
    `,
  },

  'password-reset': {
    subject: 'Reset your password - TallyCRM',
    template: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
          <h1 style="color: #007bff; margin: 0;">TallyCRM</h1>
        </div>
        <div style="padding: 30px 20px;">
          <h2 style="color: #333;">Password Reset Request</h2>
          <p style="color: #666; line-height: 1.6;">
            Hi {{firstName}},
          </p>
          <p style="color: #666; line-height: 1.6;">
            We received a request to reset your password for your TallyCRM account. 
            Click the button below to reset your password.
          </p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{resetLink}}" 
               style="background-color: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Reset Password
            </a>
          </div>
          <p style="color: #666; line-height: 1.6;">
            If the button doesn't work, you can copy and paste this link into your browser:
          </p>
          <p style="color: #007bff; word-break: break-all;">{{resetLink}}</p>
          <p style="color: #666; line-height: 1.6;">
            This password reset link will expire in 1 hour for security reasons.
          </p>
          <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
          <p style="color: #999; font-size: 12px;">
            If you didn't request a password reset, please ignore this email. Your password will remain unchanged.
          </p>
        </div>
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 12px;">
          <p>© 2024 TallyCRM by Cloudstier Solutions. All rights reserved.</p>
        </div>
      </div>
    `,
  },

  'service-call-created': {
    subject: 'New Service Call Created - {{callNumber}}',
    template: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
          <h1 style="color: #007bff; margin: 0;">TallyCRM</h1>
        </div>
        <div style="padding: 30px 20px;">
          <h2 style="color: #333;">Service Call Created</h2>
          <p style="color: #666; line-height: 1.6;">
            A new service call has been created for {{customerName}}.
          </p>
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #333;">Call Number:</td>
                <td style="padding: 8px 0; color: #666;">{{callNumber}}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #333;">Customer:</td>
                <td style="padding: 8px 0; color: #666;">{{customerName}}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #333;">Subject:</td>
                <td style="padding: 8px 0; color: #666;">{{subject}}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #333;">Priority:</td>
                <td style="padding: 8px 0; color: #666;">{{priority}}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #333;">Assigned To:</td>
                <td style="padding: 8px 0; color: #666;">{{assignedTo}}</td>
              </tr>
            </table>
          </div>
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{callLink}}" 
               style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              View Service Call
            </a>
          </div>
        </div>
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 12px;">
          <p>© 2024 TallyCRM by Cloudstier Solutions. All rights reserved.</p>
        </div>
      </div>
    `,
  },

  'amc-expiry-reminder': {
    subject: 'AMC Contract Expiring Soon - {{customerName}}',
    template: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
          <h1 style="color: #007bff; margin: 0;">TallyCRM</h1>
        </div>
        <div style="padding: 30px 20px;">
          <h2 style="color: #333;">AMC Contract Expiring Soon</h2>
          <p style="color: #666; line-height: 1.6;">
            The AMC contract for {{customerName}} is expiring soon.
          </p>
          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #333;">Customer:</td>
                <td style="padding: 8px 0; color: #666;">{{customerName}}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #333;">AMC Number:</td>
                <td style="padding: 8px 0; color: #666;">{{amcNumber}}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #333;">Expiry Date:</td>
                <td style="padding: 8px 0; color: #666;">{{expiryDate}}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #333;">Days Remaining:</td>
                <td style="padding: 8px 0; color: #666;">{{daysRemaining}}</td>
              </tr>
            </table>
          </div>
          <p style="color: #666; line-height: 1.6;">
            Please contact the customer to discuss renewal options.
          </p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{amcLink}}" 
               style="background-color: #ffc107; color: #212529; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              View AMC Details
            </a>
          </div>
        </div>
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 12px;">
          <p>© 2024 TallyCRM by Cloudstier Solutions. All rights reserved.</p>
        </div>
      </div>
    `,
  },
};

/**
 * Replace template variables with actual values
 * @param {string} template - Email template
 * @param {Object} data - Data to replace in template
 * @returns {string} Processed template
 */
const processTemplate = (template, data) => {
  let processed = template;
  
  Object.keys(data).forEach(key => {
    const regex = new RegExp(`{{${key}}}`, 'g');
    processed = processed.replace(regex, data[key] || '');
  });
  
  return processed;
};

/**
 * Send email
 * @param {Object} options - Email options
 * @param {string} options.to - Recipient email
 * @param {string} options.subject - Email subject
 * @param {string} options.template - Template name
 * @param {Object} options.data - Template data
 * @param {string} options.html - Custom HTML content
 * @param {string} options.text - Plain text content
 * @returns {Promise} Email send result
 */
export const sendEmail = async (options) => {
  try {
    const emailTransporter = createTransporter();
    
    let { to, subject, template, data = {}, html, text } = options;
    
    // Process template if provided
    if (template && emailTemplates[template]) {
      const templateData = emailTemplates[template];
      subject = subject || processTemplate(templateData.subject, data);
      html = html || processTemplate(templateData.template, data);
    }
    
    // Generate plain text from HTML if not provided
    if (html && !text) {
      text = html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
    }
    
    const mailOptions = {
      from: `"${process.env.SMTP_FROM_NAME || 'TallyCRM'}" <${process.env.SMTP_FROM_EMAIL || process.env.SMTP_USER}>`,
      to,
      subject,
      text,
      html,
    };
    
    const result = await emailTransporter.sendMail(mailOptions);
    
    logger.info('Email sent successfully:', {
      to,
      subject,
      messageId: result.messageId,
    });
    
    // Log preview URL for development
    if (process.env.NODE_ENV === 'development') {
      const previewUrl = nodemailer.getTestMessageUrl(result);
      if (previewUrl) {
        logger.info('Email preview URL:', previewUrl);
      }
    }
    
    return result;
    
  } catch (error) {
    logger.error('Error sending email:', error);
    throw error;
  }
};

/**
 * Send bulk emails
 * @param {Array} emails - Array of email options
 * @returns {Promise} Bulk send results
 */
export const sendBulkEmails = async (emails) => {
  const results = [];
  
  for (const emailOptions of emails) {
    try {
      const result = await sendEmail(emailOptions);
      results.push({ success: true, result });
    } catch (error) {
      results.push({ success: false, error: error.message });
    }
  }
  
  return results;
};

/**
 * Validate email configuration
 * @returns {Promise<boolean>} True if configuration is valid
 */
export const validateEmailConfig = async () => {
  try {
    const emailTransporter = createTransporter();
    await emailTransporter.verify();
    return true;
  } catch (error) {
    logger.error('Email configuration validation failed:', error);
    return false;
  }
};

/**
 * Send test email
 * @param {string} to - Test email recipient
 * @returns {Promise} Test email result
 */
export const sendTestEmail = async (to) => {
  return sendEmail({
    to,
    subject: 'TallyCRM - Test Email',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #007bff;">TallyCRM Test Email</h2>
        <p>This is a test email to verify that your email configuration is working correctly.</p>
        <p><strong>Sent at:</strong> ${new Date().toISOString()}</p>
        <p>If you received this email, your email configuration is working properly!</p>
      </div>
    `,
  });
};
