/**
 * Theme Manager Utility
 * Handles dynamic theme color application across the entire application
 */

class ThemeManager {
  constructor() {
    this.root = document.documentElement;
    this.defaultPrimaryColor = '#7c3aed';
    this.init();
  }

  /**
   * Initialize theme manager
   */
  init() {
    // Apply default theme if no theme is set
    const savedColor = localStorage.getItem('primaryColor') || this.defaultPrimaryColor;
    this.applyTheme(savedColor);
  }

  /**
   * Initialize theme with database color (called from Settings component)
   */
  initWithDatabaseColor(databaseColor) {
    if (databaseColor && databaseColor !== this.defaultPrimaryColor) {
      this.applyTheme(databaseColor);
    } else {
      // Fallback to localStorage or default
      const savedColor = localStorage.getItem('primaryColor') || this.defaultPrimaryColor;
      this.applyTheme(savedColor);
    }
  }

  /**
   * Convert hex to RGB values
   */
  hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }

  /**
   * Convert RGB to hex
   */
  rgbToHex(r, g, b) {
    const toHex = (n) => {
      const hex = Math.round(n).toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    };
    return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
  }

  /**
   * Adjust color brightness
   */
  adjustBrightness(hex, percent) {
    const rgb = this.hexToRgb(hex);
    if (!rgb) return hex;

    const factor = percent / 100;
    const newR = Math.max(0, Math.min(255, rgb.r + (rgb.r * factor)));
    const newG = Math.max(0, Math.min(255, rgb.g + (rgb.g * factor)));
    const newB = Math.max(0, Math.min(255, rgb.b + (rgb.b * factor)));

    return this.rgbToHex(newR, newG, newB);
  }

  /**
   * Calculate luminance of a color (0-1, where 0 is darkest and 1 is lightest)
   */
  calculateLuminance(r, g, b) {
    // Convert RGB to relative luminance using WCAG formula
    const rsRGB = r / 255;
    const gsRGB = g / 255;
    const bsRGB = b / 255;

    const rLinear = rsRGB <= 0.03928 ? rsRGB / 12.92 : Math.pow((rsRGB + 0.055) / 1.055, 2.4);
    const gLinear = gsRGB <= 0.03928 ? gsRGB / 12.92 : Math.pow((gsRGB + 0.055) / 1.055, 2.4);
    const bLinear = bsRGB <= 0.03928 ? bsRGB / 12.92 : Math.pow((bsRGB + 0.055) / 1.055, 2.4);

    return 0.2126 * rLinear + 0.7152 * gLinear + 0.0722 * bLinear;
  }

  /**
   * Determine if a color is light or dark
   */
  isLightColor(hexColor) {
    const rgb = this.hexToRgb(hexColor);
    if (!rgb) return false;

    const luminance = this.calculateLuminance(rgb.r, rgb.g, rgb.b);
    return luminance > 0.5; // Threshold for light vs dark
  }

  /**
   * Get contrasting text color (black or white) for a given background color
   */
  getContrastingTextColor(backgroundColor) {
    return this.isLightColor(backgroundColor) ? '#000000' : '#ffffff';
  }

  /**
   * Generate color palette from primary color
   */
  generatePalette(primaryColor) {
    const rgb = this.hexToRgb(primaryColor);
    if (!rgb) return {};

    // Calculate contrasting text color
    const textColor = this.getContrastingTextColor(primaryColor);
    const isLight = this.isLightColor(primaryColor);

    return {
      primary: primaryColor,
      primaryDark: this.adjustBrightness(primaryColor, -20),
      primaryDarker: this.adjustBrightness(primaryColor, -40),
      primaryLight: this.adjustBrightness(primaryColor, 20),
      primaryLighter: this.adjustBrightness(primaryColor, 40),
      primaryRgb: `${rgb.r}, ${rgb.g}, ${rgb.b}`,
      primaryText: textColor,
      primaryTextRgb: textColor === '#ffffff' ? '255, 255, 255' : '0, 0, 0',
      isLightTheme: isLight,
    };
  }

  /**
   * Apply theme colors to CSS custom properties
   */
  applyTheme(primaryColor) {
    const palette = this.generatePalette(primaryColor);

    // Set CSS custom properties for colors
    this.root.style.setProperty('--primary-color', palette.primary);
    this.root.style.setProperty('--primary-dark', palette.primaryDark);
    this.root.style.setProperty('--primary-darker', palette.primaryDarker);
    this.root.style.setProperty('--primary-light', palette.primaryLight);
    this.root.style.setProperty('--primary-lighter', palette.primaryLighter);
    this.root.style.setProperty('--primary-rgb', palette.primaryRgb);

    // Set CSS custom properties for text colors
    this.root.style.setProperty('--primary-text', palette.primaryText);
    this.root.style.setProperty('--primary-text-rgb', palette.primaryTextRgb);
    this.root.style.setProperty('--is-light-theme', palette.isLightTheme ? '1' : '0');

    // Generate gradients with appropriate text colors
    const sidebarGradient = `linear-gradient(180deg, ${palette.primary} 0%, ${palette.primaryDark} 50%, ${palette.primaryDarker} 100%)`;
    const buttonGradient = `linear-gradient(135deg, ${palette.primary}, ${palette.primaryDark})`;

    this.root.style.setProperty('--sidebar-gradient', sidebarGradient);
    this.root.style.setProperty('--button-gradient', buttonGradient);

    // Save to localStorage
    localStorage.setItem('primaryColor', primaryColor);

    // Dispatch custom event for components that need to react to theme changes
    window.dispatchEvent(new CustomEvent('themeChanged', {
      detail: { primaryColor, palette }
    }));
  }

  /**
   * Get current primary color
   */
  getCurrentPrimaryColor() {
    return localStorage.getItem('primaryColor') || this.defaultPrimaryColor;
  }

  /**
   * Reset to default theme
   */
  resetTheme() {
    this.applyTheme(this.defaultPrimaryColor);
  }

  /**
   * Get predefined color presets
   */
  getColorPresets() {
    return [
      { name: 'Purple', color: '#7c3aed' },
      { name: 'Blue', color: '#3b82f6' },
      { name: 'Green', color: '#10b981' },
      { name: 'Red', color: '#ef4444' },
      { name: 'Orange', color: '#f97316' },
      { name: 'Pink', color: '#ec4899' },
      { name: 'Indigo', color: '#6366f1' },
      { name: 'Teal', color: '#14b8a6' },
      { name: 'Yellow', color: '#eab308' },
      { name: 'Slate', color: '#64748b' },
      { name: 'Emerald', color: '#059669' },
      { name: 'Rose', color: '#f43f5e' },
      { name: 'Cyan', color: '#06b6d4' },
      { name: 'Lime', color: '#84cc16' },
      { name: 'Amber', color: '#f59e0b' },
      { name: 'Violet', color: '#8b5cf6' },
      // Light colors to test dynamic text
      { name: 'Light Blue', color: '#bfdbfe' },
      { name: 'Light Green', color: '#bbf7d0' },
      { name: 'Light Yellow', color: '#fef3c7' },
      { name: 'Light Pink', color: '#fce7f3' },
      { name: 'Light Gray', color: '#f3f4f6' },
      { name: 'White', color: '#ffffff' }
    ];
  }
}

// Create singleton instance
const themeManager = new ThemeManager();

export default themeManager;
