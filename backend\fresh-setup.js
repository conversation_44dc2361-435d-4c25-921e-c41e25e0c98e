#!/usr/bin/env node

/**
 * Complete Fresh Database Setup Script
 * This script will:
 * 1. Reset the database completely
 * 2. Run all migrations
 * 3. Seed subscription plans
 * 4. Create admin user
 */

import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

const runCommand = async (command, description) => {
  console.log(`\n🔄 ${description}...`);
  try {
    const { stdout, stderr } = await execAsync(command);
    if (stdout) console.log(stdout);
    if (stderr) console.warn(stderr);
    console.log(`✅ ${description} completed`);
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    throw error;
  }
};

const freshSetup = async () => {
  try {
    console.log('🚀 Starting fresh database setup...\n');

    // Step 1: Reset database
    await runCommand('node reset-database.js', 'Resetting database');

    // Step 2: Run migrations
    await runCommand('node src/migrations/migrate.js up', 'Running migrations');

    // Step 3: Seed all default data (includes admin user creation)
    await runCommand('npm run seed:all', 'Seeding all default data');

    console.log('\n🎉 Fresh database setup completed successfully!');
    console.log('\n📋 What was created:');
    console.log('   ✅ All 26 database tables');
    console.log('   ✅ 4 subscription plans (Starter, Professional, Business, Enterprise)');
    console.log('   ✅ Admin user: <EMAIL> / Admin@123');
    console.log('   ✅ Demo tenant with comprehensive master data');
    console.log('   ✅ 4 License Editions, 3 Tally Products, 10 Industries');
    console.log('   ✅ 8 Areas, 9 Designations, 6 Staff Roles');
    console.log('   ✅ 7 Nature of Issues, 6 Call Statuses, 6 Additional Services');
    console.log('   ✅ Sample customers and contacts for testing');
    console.log('\n🌐 Ready to start: http://localhost:3005');

  } catch (error) {
    console.error('\n❌ Fresh setup failed:', error.message);
    process.exit(1);
  }
};

freshSetup();
