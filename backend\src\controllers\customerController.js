import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import { Op } from 'sequelize';

/**
 * Get all customers with pagination and filters
 */
export const getCustomers = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      customerType,
      industryId,
      areaId,
      assignedExecutiveId,
      isActive,
      sortBy = 'created_at',
      sortOrder = 'DESC',
    } = req.query;

    const offset = (page - 1) * limit;
    const where = { tenant_id: req.user.tenant.id };

    // Apply filters - Enhanced search (Task 7)
    if (search) {
      where[Op.or] = [
        { company_name: { [Op.iLike]: `%${search}%` } },
        { customer_code: { [Op.iLike]: `%${search}%` } },
        { tally_serial_number: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } },
        { phone: { [Op.iLike]: `%${search}%` } },
        { display_name: { [Op.iLike]: `%${search}%` } },
        // Search in custom fields for tally version, status, services
        { 'custom_fields.tally_version': { [Op.iLike]: `%${search}%` } },
        { 'custom_fields.profile_status': { [Op.iLike]: `%${search}%` } },
        { customer_type: { [Op.iLike]: `%${search}%` } },
      ];
    }

    if (customerType) {
      where.customer_type = customerType;
    }

    if (industryId) {
      where.industry_id = industryId;
    }

    if (areaId) {
      where.area_id = areaId;
    }

    if (assignedExecutiveId) {
      where.assigned_executive_id = assignedExecutiveId;
    }

    if (isActive !== undefined) {
      where.is_active = isActive === 'true';
    }

    const { count, rows: customers } = await models.Customer.findAndCountAll({
      where,
      include: [
        {
          model: models.Industry,
          as: 'industry',
          attributes: ['id', 'name', 'code'],
        },
        {
          model: models.Area,
          as: 'area',
          attributes: ['id', 'name', 'code', 'city', 'state'],
          required: false, // Make it optional
          where: null, // Don't apply any where conditions
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          attributes: ['id', 'first_name', 'last_name', 'email', 'phone'],
        },
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
        },
      ],
      limit: parseInt(limit),
      offset,
      order: [[sortBy, sortOrder.toUpperCase()]],
    });

    res.json({
      success: true,
      data: {
        customers,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit),
        },
      },
    });

  } catch (error) {
    logger.error('Get customers error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customers',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get customer by ID
 */
export const getCustomerById = async (req, res) => {
  try {
    const { id } = req.params;

    const customer = await models.Customer.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
      include: [
        {
          model: models.Industry,
          as: 'industry',
        },
        {
          model: models.Area,
          as: 'area',
          required: false, // Make it optional
          where: null, // Don't apply any where conditions
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          include: [
            {
              model: models.Designation,
              as: 'designation',
            },
          ],
        },
        {
          model: models.CustomerContact,
          as: 'contacts',
          include: [
            {
              model: models.Designation,
              as: 'designation',
            },
          ],
        },
        {
          model: models.CustomerTSS,
          as: 'tssDetails',
          include: [
            {
              model: models.LicenseEdition,
              as: 'licenseEdition',
            },
          ],
        },
        {
          model: models.CustomerAMC,
          as: 'amcContracts',
        },
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
        },
      ],
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found',
      });
    }

    res.json({
      success: true,
      data: { customer },
    });

  } catch (error) {
    logger.error('Get customer by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customer',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Create new customer
 */
export const createCustomer = async (req, res) => {
  const transaction = await models.sequelize.transaction();

  try {
    const customerData = {
      ...req.body,
      tenant_id: req.user.tenant.id,
      created_by: req.user.id,
    };

    // Generate customer code if not provided
    if (!customerData.customer_code) {
      const lastCustomer = await models.Customer.findOne({
        where: { tenant_id: req.user.tenant.id },
        order: [['created_at', 'DESC']],
        transaction,
      });

      const nextNumber = lastCustomer ?
        parseInt(lastCustomer.customer_code.replace(/\D/g, '')) + 1 : 1;
      customerData.customer_code = `CUST${nextNumber.toString().padStart(4, '0')}`;
    }

    // Check if customer code already exists
    const existingCustomer = await models.Customer.findOne({
      where: {
        tenant_id: req.user.tenant.id,
        customer_code: customerData.customer_code,
      },
      transaction,
    });

    if (existingCustomer) {
      await transaction.rollback();
      return res.status(409).json({
        success: false,
        message: 'Customer code already exists',
        errors: {
          customer_code: 'This customer code is already in use'
        }
      });
    }

    // Check if email already exists (if provided)
    if (customerData.email) {
      const existingEmail = await models.Customer.findOne({
        where: {
          tenant_id: req.user.tenant.id,
          email: customerData.email,
        },
        transaction,
      });

      if (existingEmail) {
        await transaction.rollback();
        return res.status(409).json({
          success: false,
          message: 'Email address already exists',
          errors: {
            email: 'This email address is already registered'
          }
        });
      }
    }

    // Check if GST number already exists (if provided)
    if (customerData.gst_number) {
      const existingGST = await models.Customer.findOne({
        where: {
          tenant_id: req.user.tenant.id,
          gst_number: customerData.gst_number,
        },
        transaction,
      });

      if (existingGST) {
        await transaction.rollback();
        return res.status(409).json({
          success: false,
          message: 'GST number already exists',
          errors: {
            gst_number: 'This GST number is already registered'
          }
        });
      }
    }

    // Check if PAN number already exists (if provided)
    if (customerData.pan_number) {
      const existingPAN = await models.Customer.findOne({
        where: {
          tenant_id: req.user.tenant.id,
          pan_number: customerData.pan_number,
        },
        transaction,
      });

      if (existingPAN) {
        await transaction.rollback();
        return res.status(409).json({
          success: false,
          message: 'PAN number already exists',
          errors: {
            pan_number: 'This PAN number is already registered'
          }
        });
      }
    }

    // Validate foreign key references
    if (customerData.industry_id) {
      const industry = await models.Industry.findOne({
        where: { id: customerData.industry_id, tenant_id: req.user.tenant.id },
        transaction,
      });
      if (!industry) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: 'Invalid industry selected',
          errors: {
            industry_id: 'Selected industry does not exist'
          }
        });
      }
    }

    if (customerData.area_id) {
      const area = await models.Area.findOne({
        where: { id: customerData.area_id, is_active: true },
        transaction,
      });
      if (!area) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: 'Invalid area selected',
          errors: {
            area_id: 'Selected area does not exist'
          }
        });
      }
    }

    if (customerData.assigned_executive_id) {
      const executive = await models.Executive.findOne({
        where: { id: customerData.assigned_executive_id, tenant_id: req.user.tenant.id },
        transaction,
      });
      if (!executive) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: 'Invalid executive selected',
          errors: {
            assigned_executive_id: 'Selected executive does not exist'
          }
        });
      }
    }

    const customer = await models.Customer.create(customerData, { transaction });

    // Fetch the created customer with associations
    const createdCustomer = await models.Customer.findByPk(customer.id, {
      include: [
        {
          model: models.Industry,
          as: 'industry',
        },
        {
          model: models.Area,
          as: 'area',
          required: false, // Make it optional
          where: null, // Don't apply any where conditions
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
        },
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
        },
      ],
      transaction,
    });

    await transaction.commit();

    logger.info('Customer created successfully:', {
      customerId: customer.id,
      customerCode: customer.customer_code,
      companyName: customer.company_name,
      createdBy: req.user.id,
      tenantId: req.user.tenant.id,
    });

    res.status(201).json({
      success: true,
      message: 'Customer created successfully',
      data: { customer: createdCustomer },
    });

  } catch (error) {
    await transaction.rollback();
    logger.error('Create customer error:', error);

    // Handle specific database errors
    if (error.name === 'SequelizeValidationError') {
      const validationErrors = {};
      error.errors.forEach(err => {
        validationErrors[err.path] = err.message;
      });

      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validationErrors,
      });
    }

    if (error.name === 'SequelizeUniqueConstraintError') {
      const field = error.errors[0]?.path || 'field';
      return res.status(409).json({
        success: false,
        message: `${field} already exists`,
        errors: {
          [field]: `This ${field} is already in use`
        }
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create customer',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Update customer
 */
export const updateCustomer = async (req, res) => {
  const transaction = await models.sequelize.transaction();

  try {
    const { id } = req.params;

    const updateData = {
      ...req.body,
      updated_by: req.user.id,
    };

    const customer = await models.Customer.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
      transaction,
    });

    if (!customer) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: 'Customer not found',
      });
    }

    // Check if customer code is being changed and if it already exists
    if (updateData.customer_code && updateData.customer_code !== customer.customer_code) {
      const existingCustomer = await models.Customer.findOne({
        where: {
          tenant_id: req.user.tenant.id,
          customer_code: updateData.customer_code,
          id: { [Op.ne]: id },
        },
        transaction,
      });

      if (existingCustomer) {
        await transaction.rollback();
        return res.status(409).json({
          success: false,
          message: 'Customer code already exists',
          errors: {
            customer_code: 'This customer code is already in use'
          }
        });
      }
    }

    // Check if email is being changed and if it already exists
    if (updateData.email && updateData.email !== customer.email) {
      const existingEmail = await models.Customer.findOne({
        where: {
          tenant_id: req.user.tenant.id,
          email: updateData.email,
          id: { [Op.ne]: id },
        },
        transaction,
      });

      if (existingEmail) {
        await transaction.rollback();
        return res.status(409).json({
          success: false,
          message: 'Email address already exists',
          errors: {
            email: 'This email address is already registered'
          }
        });
      }
    }

    // Check if GST number is being changed and if it already exists
    if (updateData.gst_number && updateData.gst_number !== customer.gst_number) {
      const existingGST = await models.Customer.findOne({
        where: {
          tenant_id: req.user.tenant.id,
          gst_number: updateData.gst_number,
          id: { [Op.ne]: id },
        },
        transaction,
      });

      if (existingGST) {
        await transaction.rollback();
        return res.status(409).json({
          success: false,
          message: 'GST number already exists',
          errors: {
            gst_number: 'This GST number is already registered'
          }
        });
      }
    }

    // Check if PAN number is being changed and if it already exists
    if (updateData.pan_number && updateData.pan_number !== customer.pan_number) {
      const existingPAN = await models.Customer.findOne({
        where: {
          tenant_id: req.user.tenant.id,
          pan_number: updateData.pan_number,
          id: { [Op.ne]: id },
        },
        transaction,
      });

      if (existingPAN) {
        await transaction.rollback();
        return res.status(409).json({
          success: false,
          message: 'PAN number already exists',
          errors: {
            pan_number: 'This PAN number is already registered'
          }
        });
      }
    }

    // Validate foreign key references
    if (updateData.industry_id) {
      const industry = await models.Industry.findOne({
        where: { id: updateData.industry_id, tenant_id: req.user.tenant.id },
        transaction,
      });
      if (!industry) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: 'Invalid industry selected',
          errors: {
            industry_id: 'Selected industry does not exist'
          }
        });
      }
    }

    if (updateData.area_id) {
      const area = await models.Area.findOne({
        where: { id: updateData.area_id, is_active: true },
        transaction,
      });
      if (!area) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: 'Invalid area selected',
          errors: {
            area_id: 'Selected area does not exist'
          }
        });
      }
    }

    if (updateData.assigned_executive_id) {
      const executive = await models.Executive.findOne({
        where: { id: updateData.assigned_executive_id, tenant_id: req.user.tenant.id },
        transaction,
      });
      if (!executive) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: 'Invalid executive selected',
          errors: {
            assigned_executive_id: 'Selected executive does not exist'
          }
        });
      }
    }

    await customer.update(updateData, { transaction });

    // Fetch updated customer with associations
    const updatedCustomer = await models.Customer.findByPk(customer.id, {
      include: [
        {
          model: models.Industry,
          as: 'industry',
        },
        {
          model: models.Area,
          as: 'area',
          required: false, // Make it optional
          where: null, // Don't apply any where conditions
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
        },
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
        },
      ],
      transaction,
    });

    await transaction.commit();

    logger.info('Customer updated successfully:', {
      customerId: customer.id,
      customerCode: customer.customer_code,
      updatedBy: req.user.id,
    });

    res.json({
      success: true,
      message: 'Customer updated successfully',
      data: { customer: updatedCustomer },
    });

  } catch (error) {
    await transaction.rollback();
    logger.error('Update customer error:', error);

    // Handle specific database errors
    if (error.name === 'SequelizeValidationError') {
      const validationErrors = {};
      error.errors.forEach(err => {
        validationErrors[err.path] = err.message;
      });

      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validationErrors,
      });
    }

    if (error.name === 'SequelizeUniqueConstraintError') {
      const field = error.errors[0]?.path || 'field';
      return res.status(409).json({
        success: false,
        message: `${field} already exists`,
        errors: {
          [field]: `This ${field} is already in use`
        }
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to update customer',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Delete customer
 */
export const deleteCustomer = async (req, res) => {
  const transaction = await models.sequelize.transaction();

  try {
    const { id } = req.params;
    const { force = false, reason } = req.query;

    const customer = await models.Customer.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
      transaction,
    });

    if (!customer) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: 'Customer not found',
      });
    }

    // Check if customer has related records
    const [serviceCallsCount, salesCount, quotationsCount, invoicesCount] = await Promise.all([
      models.ServiceCall?.count({ where: { customer_id: id }, transaction }) || 0,
      models.Sale?.count({ where: { customer_id: id }, transaction }) || 0,
      models.Quotation?.count({ where: { customer_id: id }, transaction }) || 0,
      models.Invoice?.count({ where: { customer_id: id }, transaction }) || 0,
    ]);

    const hasRelatedRecords = serviceCallsCount > 0 || salesCount > 0 || quotationsCount > 0 || invoicesCount > 0;

    if (hasRelatedRecords && !force) {
      await transaction.rollback();
      return res.status(409).json({
        success: false,
        message: 'Cannot delete customer with existing related records. Use force=true to perform soft delete.',
        details: {
          serviceCallsCount,
          salesCount,
          quotationsCount,
          invoicesCount,
        },
        suggestion: 'Consider using soft delete by setting is_active=false instead of permanent deletion'
      });
    }

    if (force && hasRelatedRecords) {
      // Soft delete - mark as inactive instead of permanent deletion
      await customer.update({
        is_active: false,
        deleted_at: new Date(),
        deleted_by: req.user.id,
        deletion_reason: reason || 'Force deleted due to related records',
      }, { transaction });

      await transaction.commit();

      logger.info('Customer soft deleted successfully:', {
        customerId: customer.id,
        customerCode: customer.customer_code,
        deletedBy: req.user.id,
        reason: reason || 'Force deleted due to related records',
        relatedRecords: {
          serviceCallsCount,
          salesCount,
          quotationsCount,
          invoicesCount,
        }
      });

      return res.json({
        success: true,
        message: 'Customer deactivated successfully (soft delete)',
        data: {
          type: 'soft_delete',
          reason: reason || 'Force deleted due to related records',
          relatedRecords: {
            serviceCallsCount,
            salesCount,
            quotationsCount,
            invoicesCount,
          }
        }
      });
    }

    // Hard delete - permanent removal
    await customer.destroy({ transaction });
    await transaction.commit();

    logger.info('Customer permanently deleted successfully:', {
      customerId: customer.id,
      customerCode: customer.customer_code,
      deletedBy: req.user.id,
      reason: reason || 'No related records found',
    });

    res.json({
      success: true,
      message: 'Customer deleted permanently',
      data: {
        type: 'hard_delete',
        reason: reason || 'No related records found'
      }
    });

  } catch (error) {
    await transaction.rollback();
    logger.error('Delete customer error:', error);

    res.status(500).json({
      success: false,
      message: 'Failed to delete customer',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get customer statistics
 */
export const getCustomerStats = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;

    const stats = await Promise.all([
      // Total customers
      models.Customer.count({
        where: { tenant_id: tenantId, is_active: true },
      }),

      // Customers by type
      models.Customer.findAll({
        where: { tenant_id: tenantId, is_active: true },
        attributes: [
          'customer_type',
          [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
        ],
        group: ['customer_type'],
        raw: true,
      }),

      // Recent customers (last 30 days)
      models.Customer.count({
        where: {
          tenant_id: tenantId,
          is_active: true,
          created_at: {
            [Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          },
        },
      }),

      // Customers by industry
      models.Customer.findAll({
        where: { tenant_id: tenantId, is_active: true },
        include: [
          {
            model: models.Industry,
            as: 'industry',
            attributes: ['name'],
          },
        ],
        attributes: [
          [models.sequelize.fn('COUNT', models.sequelize.col('Customer.id')), 'count'],
        ],
        group: ['industry.id', 'industry.name'],
        raw: true,
      }),
    ]);

    const [totalCustomers, customersByType, recentCustomers, customersByIndustry] = stats;

    res.json({
      success: true,
      data: {
        totalCustomers,
        recentCustomers,
        customersByType: customersByType.reduce((acc, item) => {
          acc[item.customer_type] = parseInt(item.count);
          return acc;
        }, {}),
        customersByIndustry: customersByIndustry.map(item => ({
          industry: item['industry.name'] || 'Unknown',
          count: parseInt(item.count),
        })),
      },
    });

  } catch (error) {
    logger.error('Get customer stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customer statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};
