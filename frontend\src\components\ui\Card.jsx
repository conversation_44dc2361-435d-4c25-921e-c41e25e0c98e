import React from 'react';
import { cn } from '../../utils/helpers';

const Card = React.forwardRef(({
  children,
  className,
  variant = 'default',
  ...props
}, ref) => {
  const baseClasses = 'bg-white rounded border shadow-sm';

  const variants = {
    default: 'border-gray-200',
    elevated: 'border-gray-200 shadow-md',
    outlined: 'border-gray-300 shadow-none',
    flush: 'border-0 shadow-none',
  };

  return (
    <div
      ref={ref}
      className={cn(baseClasses, variants[variant], className)}
      {...props}
    >
      {children}
    </div>
  );
});

const CardHeader = React.forwardRef(({
  children,
  className,
  ...props
}, ref) => {
  return (
    <div
      ref={ref}
      className={cn('px-4 py-3 border-b border-gray-200 bg-gray-50', className)}
      {...props}
    >
      {children}
    </div>
  );
});

const CardBody = React.forwardRef(({
  children,
  className,
  ...props
}, ref) => {
  return (
    <div
      ref={ref}
      className={cn('p-4', className)}
      {...props}
    >
      {children}
    </div>
  );
});

const CardFooter = React.forwardRef(({
  children,
  className,
  ...props
}, ref) => {
  return (
    <div
      ref={ref}
      className={cn('px-4 py-3 border-t border-gray-200 bg-gray-50', className)}
      {...props}
    >
      {children}
    </div>
  );
});

const CardTitle = React.forwardRef(({
  children,
  className,
  as: Component = 'h5',
  ...props
}, ref) => {
  return (
    <Component
      ref={ref}
      className={cn('text-lg font-semibold text-gray-900 mb-0', className)}
      {...props}
    >
      {children}
    </Component>
  );
});

const CardText = React.forwardRef(({
  children,
  className,
  ...props
}, ref) => {
  return (
    <p
      ref={ref}
      className={cn('text-gray-600', className)}
      {...props}
    >
      {children}
    </p>
  );
});

Card.displayName = 'Card';
CardHeader.displayName = 'CardHeader';
CardBody.displayName = 'CardBody';
CardFooter.displayName = 'CardFooter';
CardTitle.displayName = 'CardTitle';
CardText.displayName = 'CardText';

export { Card, CardHeader, CardBody, CardFooter, CardTitle, CardText };
export default Card;
