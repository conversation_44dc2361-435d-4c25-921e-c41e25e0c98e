# TallyCRM Access Control & Permissions

## 🔐 **Account Types & Access Levels**

### **1. <PERSON><PERSON><PERSON> (Platform Administrator)**
**Email**: `<EMAIL>` | **Password**: `Admin@123`

#### ✅ **What SaaS Admin CAN Do:**
- **Platform Management**
  - Manage all customer tenants across the platform
  - View and modify any tenant's data
  - Create, suspend, or delete customer accounts
  - Monitor platform-wide usage and performance
  
- **Subscription Management**
  - Manage subscription plans and pricing
  - Upgrade/downgrade customer subscriptions
  - Handle billing and payment issues
  - Set subscription limits and quotas
  
- **System Administration**
  - Access system logs and analytics
  - Configure platform-wide settings
  - Manage system maintenance and updates
  - Handle technical support escalations
  
- **User Management**
  - Create and manage platform users
  - Assign roles and permissions
  - Reset passwords for any user
  - Deactivate or reactivate accounts

#### ❌ **What SaaS Admin CANNOT Do:**
- **Customer Data Privacy**
  - Cannot view sensitive customer business data without proper authorization
  - Cannot modify customer CRM data without customer consent
  - Cannot share customer data with third parties
  
- **Financial Restrictions**
  - Cannot procdess refunds without following proper procedures
  - Cannot modify billing without customer approval
  - Cannot access customer payment methods directly

---

### **2. Customer <PERSON>min (Tenant Administrator)**
**Email**: `<EMAIL>` | **Password**: `Demo@123`

#### ✅ **What Customer Admin CAN Do:**
- **CRM Management**
  - Full access to their tenant's CRM data
  - Manage customers, sales, and service calls
  - Create and modify master data (products, executives, etc.)
  - Generate reports and analytics for their business
  
- **User Management (Within Tenant)**
  - Add/remove users within their organization
  - Assign roles to team members
  - Manage user permissions within their tenant
  - Reset passwords for their team members
  
- **Business Operations**
  - Configure business settings and preferences
  - Manage company profile and information
  - Set up business rules and workflows
  - Export and import business data
  
- **Subscription Management**
  - View their subscription details and usage
  - Request subscription upgrades/downgrades
  - Manage billing information
  - Download invoices and payment history

#### ❌ **What Customer Admin CANNOT Do:**
- **Platform Restrictions**
  - Cannot access other tenants' data
  - Cannot modify platform-wide settings
  - Cannot create new tenants
  - Cannot access system administration features
  
- **Technical Limitations**
  - Cannot exceed subscription limits (users, customers, storage)
  - Cannot access platform logs or system analytics
  - Cannot modify core system functionality
  - Cannot perform platform maintenance tasks

---

### **3. Regular User (Team Member)**
**Note**: Created by Customer Admin within their tenant

#### ✅ **What Regular User CAN Do:**
- **Daily Operations**
  - View and manage assigned customers
  - Create and update service calls
  - Record sales activities and follow-ups
  - Access reports relevant to their role
  
- **Data Entry**
  - Add new customer information
  - Update customer details and contact information
  - Log service call activities and resolutions
  - Record sales meetings and outcomes
  
- **Personal Settings**
  - Update their profile information
  - Change password and preferences
  - Configure notification settings
  - Customize dashboard view

#### ❌ **What Regular User CANNOT Do:**
- **Administrative Restrictions**
  - Cannot add or remove other users
  - Cannot modify master data or system settings
  - Cannot access billing or subscription information
  - Cannot delete critical business data
  
- **Data Limitations**
  - Cannot access customers not assigned to them (if role-based access is enabled)
  - Cannot view sensitive financial information
  - Cannot export large datasets
  - Cannot modify system configurations

---

## 🏢 **Tenant Isolation & Security**

### **Data Separation**
- Each customer tenant has completely isolated data
- No cross-tenant data access or visibility
- Secure multi-tenant architecture with row-level security

### **Subscription Limits**
| Feature | SaaS Admin | Demo Customer | Regular User |
|---------|------------|---------------|--------------|
| Users | Unlimited | 10 | N/A |
| Customers | Unlimited | 100 | Limited by role |
| Service Calls | Unlimited | 50 | Limited by role |
| Storage | Unlimited | 5GB | Shared with tenant |
| Reports | All | Tenant-specific | Role-based |

---

## 🔒 **Security Features**

### **Authentication**
- Secure login with encrypted passwords
- Session management and timeout
- Password complexity requirements
- Account lockout after failed attempts

### **Authorization**
- Role-based access control (RBAC)
- Tenant-level data isolation
- Feature-based permissions
- API endpoint protection

### **Data Protection**
- Encrypted data transmission (HTTPS)
- Secure data storage
- Regular security audits
- GDPR compliance measures

---

## 📞 **Support & Escalation**

### **Customer Support Levels**
1. **Self-Service**: Documentation, FAQs, tutorials
2. **Standard Support**: Email support for regular issues
3. **Priority Support**: Phone/chat support for urgent issues
4. **Technical Escalation**: Direct access to technical team

### **SaaS Admin Responsibilities**
- Handle technical escalations
- Resolve platform-wide issues
- Provide advanced troubleshooting
- Coordinate with development team for bug fixes

---

## 🚀 **Getting Started**

### **For New Customers**
1. Sign up for a TallyCRM account
2. Choose appropriate subscription plan
3. Complete company profile setup
4. Add team members and assign roles
5. Import existing customer data
6. Configure business settings and preferences

### **For Team Members**
1. Receive invitation from Customer Admin
2. Complete account setup and profile
3. Familiarize with assigned permissions
4. Start managing assigned customers and tasks
5. Use reporting features for daily activities

---

**📧 Need Help?** Contact <NAME_EMAIL> or login to access the help center.
