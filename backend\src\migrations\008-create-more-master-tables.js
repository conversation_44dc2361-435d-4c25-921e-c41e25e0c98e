import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  // Create staff_roles table
  await queryInterface.createTable('staff_roles', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    department: {
      type: DataTypes.ENUM('sales', 'technical', 'support', 'management', 'accounts', 'hr', 'marketing'),
      allowNull: false,
      defaultValue: 'support',
    },
    level: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
    },
    responsibilities: {
      type: DataTypes.JSONB,
      defaultValue: [],
    },
    permissions: {
      type: DataTypes.JSONB,
      defaultValue: [],
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Create industries table
  await queryInterface.createTable('industries', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    category: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Create areas table
  await queryInterface.createTable('areas', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    city: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    state: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    country: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'India',
    },
    postal_codes: {
      type: DataTypes.JSONB,
      defaultValue: [],
    },
    coordinates: {
      type: DataTypes.JSONB,
      allowNull: true,
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes for staff_roles
  await queryInterface.addIndex('staff_roles', ['code'], { unique: true });
  await queryInterface.addIndex('staff_roles', ['department']);
  await queryInterface.addIndex('staff_roles', ['level']);
  await queryInterface.addIndex('staff_roles', ['is_active']);
  await queryInterface.addIndex('staff_roles', ['sort_order']);

  // Add indexes for industries
  await queryInterface.addIndex('industries', ['code'], { unique: true });
  await queryInterface.addIndex('industries', ['category']);
  await queryInterface.addIndex('industries', ['is_active']);
  await queryInterface.addIndex('industries', ['sort_order']);

  // Add indexes for areas
  await queryInterface.addIndex('areas', ['code'], { unique: true });
  await queryInterface.addIndex('areas', ['city']);
  await queryInterface.addIndex('areas', ['state']);
  await queryInterface.addIndex('areas', ['country']);
  await queryInterface.addIndex('areas', ['is_active']);
  await queryInterface.addIndex('areas', ['sort_order']);
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('areas');
  await queryInterface.dropTable('industries');
  await queryInterface.dropTable('staff_roles');
};
