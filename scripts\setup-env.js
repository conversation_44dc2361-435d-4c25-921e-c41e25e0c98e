#!/usr/bin/env node

/**
 * Environment Setup Script for Docker
 * Helps users set up environment files for Docker deployment
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = (message, color = colors.reset) => {
  console.log(`${color}${message}${colors.reset}`);
};

const checkFileExists = (filePath) => {
  return fs.existsSync(path.join(rootDir, filePath));
};

const copyFile = (source, destination) => {
  const sourcePath = path.join(rootDir, source);
  const destPath = path.join(rootDir, destination);
  
  try {
    fs.copyFileSync(sourcePath, destPath);
    return true;
  } catch (error) {
    log(`❌ Error copying ${source} to ${destination}: ${error.message}`, colors.red);
    return false;
  }
};

const generateSecureSecret = (length = 32) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

const updateEnvFile = (filePath, updates) => {
  try {
    let content = fs.readFileSync(path.join(rootDir, filePath), 'utf8');
    
    Object.entries(updates).forEach(([key, value]) => {
      const regex = new RegExp(`^${key}=.*$`, 'm');
      if (content.match(regex)) {
        content = content.replace(regex, `${key}=${value}`);
      } else {
        content += `\n${key}=${value}`;
      }
    });
    
    fs.writeFileSync(path.join(rootDir, filePath), content);
    return true;
  } catch (error) {
    log(`❌ Error updating ${filePath}: ${error.message}`, colors.red);
    return false;
  }
};

const setupEnvironmentFiles = () => {
  log(`${colors.cyan}${colors.bright}🔧 TallyCRM Docker Environment Setup${colors.reset}`);
  log(`${colors.cyan}======================================${colors.reset}`);

  // Check if .env.docker exists
  if (!checkFileExists('.env.docker')) {
    log(`❌ .env.docker template file not found!`, colors.red);
    process.exit(1);
  }

  // Setup production environment file
  log(`\n${colors.blue}📝 Setting up production environment file...${colors.reset}`);
  
  if (checkFileExists('.env.prod')) {
    log(`⚠️  .env.prod already exists. Skipping creation.`, colors.yellow);
  } else {
    if (copyFile('.env.docker', '.env.prod')) {
      log(`✅ Created .env.prod from template`, colors.green);
      
      // Generate secure secrets
      const jwtSecret = generateSecureSecret(32);
      const jwtRefreshSecret = generateSecureSecret(32);
      const dbPassword = generateSecureSecret(16);
      const redisPassword = generateSecureSecret(16);
      
      const updates = {
        'JWT_SECRET': jwtSecret,
        'JWT_REFRESH_SECRET': jwtRefreshSecret,
        'DB_PASSWORD': dbPassword,
        'REDIS_PASSWORD': redisPassword
      };
      
      if (updateEnvFile('.env.prod', updates)) {
        log(`✅ Generated secure secrets for production`, colors.green);
      }
    }
  }

  // Setup backend environment file
  log(`\n${colors.blue}📝 Setting up backend environment file...${colors.reset}`);
  
  if (checkFileExists('backend/.env')) {
    log(`⚠️  backend/.env already exists. Skipping creation.`, colors.yellow);
  } else if (checkFileExists('backend/.env.example')) {
    if (copyFile('backend/.env.example', 'backend/.env')) {
      log(`✅ Created backend/.env from example`, colors.green);
    }
  } else {
    log(`❌ backend/.env.example not found!`, colors.red);
  }

  // Setup frontend environment file
  log(`\n${colors.blue}📝 Setting up frontend environment file...${colors.reset}`);
  
  if (checkFileExists('frontend/.env')) {
    log(`⚠️  frontend/.env already exists. Skipping creation.`, colors.yellow);
  } else if (checkFileExists('frontend/.env.example')) {
    if (copyFile('frontend/.env.example', 'frontend/.env')) {
      log(`✅ Created frontend/.env from example`, colors.green);
    }
  } else {
    log(`❌ frontend/.env.example not found!`, colors.red);
  }

  // Summary
  log(`\n${colors.cyan}📋 Environment Setup Summary:${colors.reset}`);
  log(`${colors.green}✅ Environment files are ready for Docker deployment${colors.reset}`);
  
  log(`\n${colors.yellow}📝 Next Steps:${colors.reset}`);
  log(`${colors.yellow}1. Edit .env.prod with your production settings${colors.reset}`);
  log(`${colors.yellow}2. Update database passwords and API keys${colors.reset}`);
  log(`${colors.yellow}3. Configure email and Stripe settings${colors.reset}`);
  log(`${colors.yellow}4. Run: npm run docker:prod:build${colors.reset}`);
  
  log(`\n${colors.cyan}🔒 Security Notes:${colors.reset}`);
  log(`${colors.cyan}• Secure secrets have been generated automatically${colors.reset}`);
  log(`${colors.cyan}• Never commit .env.prod to version control${colors.reset}`);
  log(`${colors.cyan}• Change default passwords before production use${colors.reset}`);
};

const main = () => {
  try {
    setupEnvironmentFiles();
  } catch (error) {
    log(`❌ Setup failed: ${error.message}`, colors.red);
    process.exit(1);
  }
};

main();
