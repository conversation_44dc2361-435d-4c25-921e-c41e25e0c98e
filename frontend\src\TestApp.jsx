import React from 'react';

const TestApp = () => {
  console.log('TestApp is rendering!');

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#f3f4f6',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '2rem',
        borderRadius: '8px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        textAlign: 'center'
      }}>
        <h1 style={{
          fontSize: '2rem',
          fontWeight: 'bold',
          color: '#7c3aed',
          marginBottom: '1rem'
        }}>
          TallyCRM Test
        </h1>
        <p style={{ color: '#6b7280', marginBottom: '1rem' }}>
          If you can see this, React is working!
        </p>
        <button style={{
          backgroundColor: '#7c3aed',
          color: 'white',
          padding: '0.5rem 1rem',
          borderRadius: '4px',
          border: 'none',
          cursor: 'pointer'
        }}>
          Test Button
        </button>
        <div style={{ marginTop: '1rem', fontSize: '0.875rem', color: '#6b7280' }}>
          <p>✅ React is loaded</p>
          <p>✅ JavaScript is working</p>
          <p>✅ Component is rendering</p>
        </div>
      </div>
    </div>
  );
};

export default TestApp;
