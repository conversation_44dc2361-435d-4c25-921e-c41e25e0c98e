import React from 'react';
import { cn } from '../../utils/helpers';

const Button = React.forwardRef(({
  children,
  className,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  type = 'button',
  onClick,
  ...props
}, ref) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded border transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';

  const variants = {
    primary: 'btn-primary border-2 transition-all duration-200',
    secondary: 'bg-gray-500 text-white border-gray-500 hover:bg-gray-600 hover:border-gray-600 focus:ring-gray-400',
    success: 'bg-green-600 text-white border-green-600 hover:bg-green-700 hover:border-green-700 focus:ring-green-500',
    danger: 'bg-red-600 text-white border-red-600 hover:bg-red-700 hover:border-red-700 focus:ring-red-500',
    warning: 'bg-yellow-500 text-gray-900 border-yellow-500 hover:bg-yellow-600 hover:border-yellow-600 focus:ring-yellow-400',
    info: 'bg-blue-500 text-white border-blue-500 hover:bg-blue-600 hover:border-blue-600 focus:ring-blue-400',
    outline: 'btn-outline-primary bg-transparent border-2 transition-all duration-200',
    'outline-primary': 'btn-outline-primary bg-transparent border-2 transition-all duration-200',
    'outline-secondary': 'bg-transparent text-gray-500 border-gray-500 hover:bg-gray-500 hover:text-white focus:ring-gray-400',
    'outline-success': 'bg-transparent text-green-600 border-green-600 hover:bg-green-600 hover:text-white focus:ring-green-500',
    'outline-danger': 'bg-transparent text-red-600 border-red-600 hover:bg-red-600 hover:text-white focus:ring-red-500',
    'outline-warning': 'bg-transparent text-yellow-600 border-yellow-600 hover:bg-yellow-600 hover:text-white focus:ring-yellow-500',
    'outline-info': 'bg-transparent text-blue-600 border-blue-600 hover:bg-blue-600 hover:text-white focus:ring-blue-500',
    link: 'btn-link bg-transparent border-transparent hover:underline transition-all duration-200 p-0',
    ghost: 'btn-ghost bg-transparent border-transparent hover:bg-gray-100 transition-all duration-200',
  };

  const sizes = {
    sm: 'px-3 py-1.5 text-xs',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base',
    xl: 'px-8 py-4 text-lg',
  };

  const handleClick = (e) => {
    if (disabled || loading) {
      e.preventDefault();
      return;
    }
    onClick?.(e);
  };

  return (
    <button
      ref={ref}
      type={type}
      className={cn(
        baseClasses,
        variants[variant],
        sizes[size],
        className
      )}
      disabled={disabled || loading}
      onClick={handleClick}
      {...props}
    >
      {loading && (
        <svg
          className="animate-spin -ml-1 mr-2 h-4 w-4"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )}
      {children}
    </button>
  );
});

Button.displayName = 'Button';

export { Button };
export default Button;
