import React, { useState } from 'react';
import { NavLink, useLocation, useSearchParams } from 'react-router-dom';
import useAppStore from '../../store/appStore';

const Sidebar = ({ collapsed, visible, onToggle, onClose }) => {
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const [mastersExpanded, setMastersExpanded] = useState(
    location.pathname.startsWith('/masters') || location.pathname.startsWith('/customers')
  );
  const { masterCounts } = useAppStore();

  // Function to check if a master item is active
  const isMasterItemActive = (path) => {
    // Handle customers path directly
    if (path === '/customers') {
      return location.pathname.startsWith('/customers');
    }

    // Only proceed if we're on the masters page
    if (!location.pathname.startsWith('/masters')) return false;

    // Extract the tab parameter from the path
    const urlParams = new URLSearchParams(path.split('?')[1]);
    const tabFromPath = urlParams.get('tab');
    const currentTab = searchParams.get('tab');

    // Only return true if the tab parameters match exactly
    return tabFromPath === currentTab && tabFromPath !== null;
  };

  const menuItems = [
    { path: '/dashboard', icon: 'bi-speedometer2', label: 'Dashboard' },
    { path: '/services', icon: 'bi-tools', label: 'Services' },
    { path: '/sales', icon: 'bi-graph-up', label: 'Sales' },
    // { path: '/billing/dashboard', icon: 'bi-credit-card', label: 'Billing' }, // Temporarily commented out
    { path: '/reports', icon: 'bi-bar-chart', label: 'Reports' },
    { path: '/settings', icon: 'bi-sliders', label: 'Settings' },
  ];

  const mastersSubItems = [
    { path: '/customers', label: 'Customer List', count: masterCounts['customers'] || 0 },
    { path: '/masters?tab=license-editions', label: 'License Editions', count: masterCounts['license-editions'] },
    { path: '/masters?tab=designations', label: 'Designations', count: masterCounts['designations'] },
    { path: '/masters?tab=tally-products', label: 'Tally Products', count: masterCounts['tally-products'] },
    { path: '/masters?tab=staff-roles', label: 'Staff Roles', count: masterCounts['staff-roles'] },
    { path: '/masters?tab=executives', label: 'Executives', count: masterCounts['executives'] },
    { path: '/masters?tab=industries', label: 'Industries', count: masterCounts['industries'] },
    { path: '/masters?tab=areas', label: 'Areas', count: masterCounts['areas'] },
    { path: '/masters?tab=nature-of-issues', label: 'Nature of Issues', count: masterCounts['nature-of-issues'] },
    { path: '/masters?tab=additional-services', label: 'Additional Services', count: masterCounts['additional-services'] },
    { path: '/masters?tab=call-statuses', label: 'Call Statuses', count: masterCounts['call-statuses'] },
  ];

  return (
    <>
      {/* Mobile Overlay */}
      {visible && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 md:hidden z-fixed"
          onClick={onClose}
        />
      )}

      <div className={`sidebar ${collapsed ? 'collapsed' : ''} ${visible ? 'show' : ''}`}>
        <div className="p-4 border-b" style={{ borderColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)' }}>
          <div className="flex items-center">
            {!collapsed && (
              <>
                <div className="flex items-center mr-auto">
                  <div className="w-8 h-8 rounded-lg flex items-center justify-center mr-3" style={{ backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)' }}>
                    <i className="bi bi-graph-up text-lg" style={{ color: 'var(--primary-text, #ffffff)' }}></i>
                  </div>
                  <h5 className="text-lg font-bold mb-0" style={{ color: 'var(--primary-text, #ffffff)' }}>TallyCRM</h5>
                </div>
                <button
                  className="p-1 hidden md:block focus:outline-none focus:ring-2 focus:ring-opacity-50 rounded"
                  style={{
                    color: 'var(--primary-text, #ffffff)',
                    '--tw-ring-color': 'var(--primary-text, #ffffff)'
                  }}
                  onMouseEnter={(e) => e.target.style.color = 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)'}
                  onMouseLeave={(e) => e.target.style.color = 'var(--primary-text, #ffffff)'}
                  onClick={onToggle}
                >
                  <i className="bi bi-list text-xl"></i>
                </button>
              </>
            )}
            {collapsed && (
              <button
                className="p-1 mx-auto hidden md:block focus:outline-none focus:ring-2 focus:ring-opacity-50 rounded"
                style={{
                  color: 'var(--primary-text, #ffffff)',
                  '--tw-ring-color': 'var(--primary-text, #ffffff)'
                }}
                onMouseEnter={(e) => e.target.style.color = 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)'}
                onMouseLeave={(e) => e.target.style.color = 'var(--primary-text, #ffffff)'}
                onClick={onToggle}
              >
                <i className="bi bi-list text-xl"></i>
              </button>
            )}
            <button
              className="p-1 ml-auto md:hidden focus:outline-none focus:ring-2 focus:ring-opacity-50 rounded"
              style={{
                color: 'var(--primary-text, #ffffff)',
                '--tw-ring-color': 'var(--primary-text, #ffffff)'
              }}
              onMouseEnter={(e) => e.target.style.color = 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)'}
              onMouseLeave={(e) => e.target.style.color = 'var(--primary-text, #ffffff)'}
              onClick={onClose}
            >
              <i className="bi bi-x-lg text-xl"></i>
            </button>
          </div>
        </div>

        <nav className="flex-1 py-3">
          {menuItems.map((item) => (
            <NavLink
              key={item.path}
              to={item.path}
              className={({ isActive }) =>
                `nav-link ${isActive ? 'active' : ''}`
              }
              onClick={() => window.innerWidth < 768 && onClose()}
            >
              <i className={`bi ${item.icon}`}></i>
              <span>{item.label}</span>
            </NavLink>
          ))}

          {/* Masters Dropdown */}
          <div className="masters-dropdown">
            <div
              className={`nav-link cursor-pointer ${
                location.pathname.startsWith('/masters') || location.pathname.startsWith('/customers') ? 'active' : ''
              }`}
              onClick={() => setMastersExpanded(!mastersExpanded)}
            >
              <i className="bi bi-gear"></i>
              <span>Masters</span>
              {!collapsed && (
                <i className={`bi ${mastersExpanded ? 'bi-chevron-up' : 'bi-chevron-down'} ml-auto text-xs`}></i>
              )}
            </div>

            {mastersExpanded && !collapsed && (
              <div className="masters-submenu">
                {mastersSubItems.map((subItem) => {
                  // Determine if this item is active
                  let isActive = false;
                  
                  if (subItem.path === '/customers') {
                    // For customers path
                    isActive = location.pathname.startsWith('/customers');
                  } else if (location.pathname.startsWith('/masters')) {
                    // For masters paths with tab parameter
                    const urlParams = new URLSearchParams(subItem.path.split('?')[1]);
                    const tabFromPath = urlParams.get('tab');
                    const currentTab = searchParams.get('tab');
                    isActive = tabFromPath === currentTab && tabFromPath !== null;
                  }
                  
                  return (
                    <a
                      key={subItem.path}
                      href={subItem.path}
                      className={`nav-sub-link ${isActive ? 'active' : ''}`}
                      onClick={(e) => {
                        e.preventDefault();
                        window.history.pushState({}, '', subItem.path);
                        window.dispatchEvent(new PopStateEvent('popstate'));
                        if (window.innerWidth < 768) onClose();
                      }}
                    >
                      <span className="sub-item-label">{subItem.label}</span>
                      <span className="sub-item-count">{subItem.count}</span>
                    </a>
                  );
                })}
              </div>
            )}
          </div>
        </nav>

        {/* Profile button temporarily commented out due to 431 header size error */}
        {/*
        <div className="p-4 border-t border-white/20">
          <NavLink
            to="/profile"
            className={({ isActive }) =>
              `nav-link ${isActive ? 'active' : ''}`
            }
            onClick={() => window.innerWidth < 768 && onClose()}
          >
            <i className="bi bi-person-circle"></i>
            <span>Profile</span>
          </NavLink>
        </div>
        */}
      </div>
    </>
  );
};

export default Sidebar;
