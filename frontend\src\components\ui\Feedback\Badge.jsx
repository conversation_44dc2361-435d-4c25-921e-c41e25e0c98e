import React from 'react';
import { cn } from '../../../utils/helpers';

const Badge = React.forwardRef(({
  children,
  className,
  variant = 'primary',
  size = 'md',
  pill = false,
  ...props
}, ref) => {
  const baseClasses = 'inline-flex items-center font-medium';
  
  const variants = {
    primary: 'bg-primary-100 text-primary-800',
    secondary: 'bg-secondary-100 text-secondary-800',
    success: 'bg-success-100 text-success-800',
    danger: 'bg-danger-100 text-danger-800',
    warning: 'bg-warning-100 text-warning-800',
    info: 'bg-info-100 text-info-800',
    light: 'bg-gray-100 text-gray-800',
    dark: 'bg-gray-800 text-white',
  };

  const sizes = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-0.5 text-xs',
    lg: 'px-3 py-1 text-sm',
  };

  const shape = pill ? 'rounded-full' : 'rounded';

  return (
    <span
      ref={ref}
      className={cn(
        baseClasses,
        variants[variant],
        sizes[size],
        shape,
        className
      )}
      {...props}
    >
      {children}
    </span>
  );
});

Badge.displayName = 'Badge';

export default Badge;
