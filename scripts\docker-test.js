#!/usr/bin/env node

/**
 * Docker Setup Test Script
 * Tests the Docker configuration and services
 */

import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = (message, color = colors.reset) => {
  console.log(`${color}${message}${colors.reset}`);
};

const runCommand = async (command, description) => {
  try {
    log(`\n${colors.blue}🔄 ${description}...${colors.reset}`);
    const { stdout, stderr } = await execAsync(command);
    
    if (stderr && !stderr.includes('WARNING')) {
      log(`${colors.yellow}⚠️  Warning: ${stderr}${colors.reset}`);
    }
    
    log(`${colors.green}✅ ${description} completed${colors.reset}`);
    return { success: true, output: stdout };
  } catch (error) {
    log(`${colors.red}❌ ${description} failed: ${error.message}${colors.reset}`);
    return { success: false, error: error.message };
  }
};

const checkService = async (url, serviceName, timeout = 30000) => {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    try {
      const response = await fetch(url);
      if (response.ok) {
        log(`${colors.green}✅ ${serviceName} is healthy${colors.reset}`);
        return true;
      }
    } catch (error) {
      // Service not ready yet, continue waiting
    }
    
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  log(`${colors.red}❌ ${serviceName} health check failed${colors.reset}`);
  return false;
};

const main = async () => {
  log(`${colors.cyan}${colors.bright}🐳 TallyCRM Docker Setup Test${colors.reset}`);
  log(`${colors.cyan}================================${colors.reset}`);

  // Check Docker installation
  const dockerCheck = await runCommand('docker --version', 'Checking Docker installation');
  if (!dockerCheck.success) {
    log(`${colors.red}❌ Docker is not installed or not accessible${colors.reset}`);
    process.exit(1);
  }

  const composeCheck = await runCommand('docker-compose --version', 'Checking Docker Compose installation');
  if (!composeCheck.success) {
    log(`${colors.red}❌ Docker Compose is not installed or not accessible${colors.reset}`);
    process.exit(1);
  }

  // Build and start development environment
  log(`\n${colors.magenta}🏗️  Building and starting development environment...${colors.reset}`);
  
  const buildResult = await runCommand('docker-compose up -d --build', 'Building and starting services');
  if (!buildResult.success) {
    log(`${colors.red}❌ Failed to start Docker services${colors.reset}`);
    process.exit(1);
  }

  // Wait for services to be ready
  log(`\n${colors.yellow}⏳ Waiting for services to be ready...${colors.reset}`);
  await new Promise(resolve => setTimeout(resolve, 10000));

  // Check service health
  log(`\n${colors.blue}🔍 Checking service health...${colors.reset}`);
  
  const services = [
    { url: 'http://localhost:5432', name: 'PostgreSQL', skip: true }, // Can't check PostgreSQL via HTTP
    { url: 'http://localhost:6379', name: 'Redis', skip: true }, // Can't check Redis via HTTP
    { url: 'http://localhost:3001/health', name: 'Backend API' },
    { url: 'http://localhost:3000', name: 'Frontend' }
  ];

  let allHealthy = true;
  for (const service of services) {
    if (service.skip) {
      log(`${colors.yellow}⏭️  Skipping ${service.name} health check${colors.reset}`);
      continue;
    }
    
    const healthy = await checkService(service.url, service.name);
    if (!healthy) {
      allHealthy = false;
    }
  }

  // Show container status
  log(`\n${colors.blue}📊 Container Status:${colors.reset}`);
  await runCommand('docker-compose ps', 'Checking container status');

  // Show logs if there are issues
  if (!allHealthy) {
    log(`\n${colors.yellow}📋 Recent logs:${colors.reset}`);
    await runCommand('docker-compose logs --tail=20', 'Showing recent logs');
  }

  // Summary
  log(`\n${colors.cyan}📋 Test Summary:${colors.reset}`);
  if (allHealthy) {
    log(`${colors.green}✅ All services are running successfully!${colors.reset}`);
    log(`${colors.green}🌐 Frontend: http://localhost:3000${colors.reset}`);
    log(`${colors.green}🔌 Backend API: http://localhost:3001${colors.reset}`);
    log(`${colors.green}💾 PostgreSQL: localhost:5432${colors.reset}`);
    log(`${colors.green}🔴 Redis: localhost:6379${colors.reset}`);
  } else {
    log(`${colors.red}❌ Some services are not healthy. Check the logs above.${colors.reset}`);
  }

  log(`\n${colors.cyan}🛠️  Useful commands:${colors.reset}`);
  log(`${colors.yellow}  View logs:${colors.reset} npm run docker:dev:logs`);
  log(`${colors.yellow}  Stop services:${colors.reset} npm run docker:dev:down`);
  log(`${colors.yellow}  Rebuild:${colors.reset} npm run docker:dev:build`);
};

main().catch(error => {
  log(`${colors.red}❌ Test script failed: ${error.message}${colors.reset}`);
  process.exit(1);
});
