# =================================
# TallyCRM Backend Environment Configuration
# =================================

# Application Configuration
NODE_ENV=development
PORT=8080
APP_NAME=TallyCRM Backend
APP_VERSION=1.0.0
APP_URL=http://localhost:8080

# Frontend Configuration
FRONTEND_URL=http://localhost:3000
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
ENABLE_CORS=true

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tallycrm_dev
DB_USERNAME=postgres
DB_PASSWORD=your_password_here
DB_DIALECT=postgres
DB_LOGGING=false

# Test Database Configuration
TEST_DB_HOST=localhost
TEST_DB_PORT=5432
TEST_DB_NAME=tallycrm_test
TEST_DB_USERNAME=postgres
TEST_DB_PASSWORD=your_password_here

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your_super_secret_refresh_key_here_change_in_production
JWT_REFRESH_EXPIRES_IN=7d

# Session Configuration
SESSION_SECRET=your_super_secret_session_key_here_change_in_production
SESSION_NAME=tallycrm_session
SESSION_COOKIE_MAX_AGE=86400000

# Security Configuration
BCRYPT_SALT_ROUNDS=12
ENABLE_RATE_LIMITING=true
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Email Configuration (Nodemailer)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
EMAIL_FROM=<EMAIL>

# File Upload Configuration
UPLOAD_PATH=uploads/
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx,xls,xlsx

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=logs/
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d

# Google Maps API (Optional)
GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# SaaS Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# SaaS Features
ENABLE_USAGE_TRACKING=true
ENABLE_BILLING=true
ENABLE_SUBSCRIPTIONS=true
ENABLE_MULTI_TENANT=true

# Encryption Configuration
ENCRYPTION_KEY=your_32_character_encryption_key_here

# API Configuration
API_PREFIX=/api/v1
API_RATE_LIMIT=1000

# Cache Configuration (Redis - Optional)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Monitoring Configuration
ENABLE_MONITORING=true
HEALTH_CHECK_ENDPOINT=/health

# Development Configuration
DEBUG=tallycrm:*
ENABLE_SWAGGER=true
SWAGGER_PATH=/api-docs

# Production Configuration (Override in production)
# NODE_ENV=production
# DB_SSL=true
# ENABLE_SWAGGER=false
# LOG_LEVEL=error
