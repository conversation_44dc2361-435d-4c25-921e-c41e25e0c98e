import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  await queryInterface.addColumn('designations', 'is_mandatory', {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    allowNull: false,
    comment: 'If true, at least one contact with this designation must be entered in Customer Address Book'
  });
};

export const down = async (queryInterface) => {
  await queryInterface.removeColumn('designations', 'is_mandatory');
};
