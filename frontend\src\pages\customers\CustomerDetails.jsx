import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { Helmet } from 'react-helmet-async';
import { apiService } from '../../services/api';
import { handleAuthError } from '../../utils/authErrorHandler';
import { Card, CardHeader, CardBody, Button, Badge, Spinner, Alert } from '../../components/ui';
import LoadingScreen from '../../components/ui/LoadingScreen';

const CustomerDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [customer, setCustomer] = useState(null);
  const [rawCustomerData, setRawCustomerData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');




  // Transform raw customer data to display format
  const transformCustomerData = (customerData) => {
    if (!customerData) return null;

    const customFields = customerData.custom_fields || {};

    return {
      id: customerData.id,
      name: customerData.company_name || customerData.display_name || 'N/A',
      contactPerson: customFields.address_book?.[0]?.contact_person || customerData.contact_person || 'N/A',
      designation: customFields.address_book?.[0]?.type || customerData.designation || 'N/A',
      email: customerData.email || customFields.address_book?.[0]?.email || 'N/A',
      phone: customerData.phone || customFields.address_book?.[0]?.mobile_numbers?.[0] || 'N/A',
      alternatePhone: customFields.address_book?.[0]?.phone || customerData.alternate_phone || 'N/A',
      address: customerData.address_line_1 || customerData.address || customFields.map_location || 'N/A',
      city: customerData.city || 'N/A',
      state: customerData.state || 'N/A',
      pincode: customerData.postal_code || customerData.pincode || 'N/A',
      country: customerData.country || 'India',
      businessType: customerData.business_type || 'N/A',
      industry: customerData.industry?.name || customerData.industry || 'N/A',
      gstNumber: customerData.gst_number || 'N/A',
      panNumber: customerData.pan_number || 'N/A',
      annualTurnover: customerData.annual_turnover ? `${customerData.annual_turnover} Lakhs` : 'N/A',
      employeeCount: customerData.employee_count || 'N/A',
      status: customerData.is_active ? 'active' : 'inactive',
      tallyVersion: customFields.tally_version || customerData.tally_version || 'TallyPrime',
      tallySerialNumber: customerData.tally_serial_number || customerData.customer_code || 'N/A',
      licenseType: customFields.license_edition_id || customerData.license_type || 'N/A',
      installationDate: customerData.installation_date ? new Date(customerData.installation_date).toLocaleDateString() : 'N/A',
      registrationDate: customerData.created_at ? new Date(customerData.created_at).toLocaleDateString() : 'N/A',
      lastContact: customerData.updated_at ? new Date(customerData.updated_at).toLocaleDateString() : 'N/A',
      notes: customerData.notes || 'No notes available',

      // Additional fields from custom_fields
      profileStatus: customFields.profile_status || 'N/A',
      customerStatus: customerData.is_active ? 'active' : 'inactive',
      followUpExecutive: customFields.follow_up_executive_name || 'N/A',
      licenseEdition: customFields.license_edition_name || 'N/A',
      location: customFields.location_name || 'N/A',
      mapLocation: customFields.map_location || customerData.address_line_1 || 'N/A',
      latitude: customerData.latitude || 'N/A',
      longitude: customerData.longitude || 'N/A',
      remarks: customerData.notes || 'No remarks available',

      // Address Book
      addressBook: customFields.address_book || [],

      // TSS Status
      tssStatus: customFields.tss_status || 'NO',
      tssExpiryDate: customFields.tss_expiry_date || 'N/A',
      adminEmail: customFields.admin_email || 'N/A',

      // AMC Status
      amcStatus: customFields.amc_status || 'NO',
      amcFromDate: customFields.amc_from_date || 'N/A',
      amcToDate: customFields.amc_to_date || 'N/A',
      renewalDate: customFields.renewal_date || 'N/A',
      noOfVisits: customFields.no_of_visits || 'N/A',
      currentAmcAmount: customFields.current_amc_amount || 'N/A',
      lastYearAmcAmount: customFields.last_year_amc_amount || 'N/A',



      // Additional Features with expiry dates
      tdlAddons: customFields.tdl_addons || false,
      tdlAddonsExpiryDate: customFields.tdl_addons_expiry_date || 'N/A',
      whatsappTelegramGroup: customFields.whatsapp_telegram_group || false,
      whatsappTelegramGroupExpiryDate: customFields.whatsapp_telegram_group_expiry_date || 'N/A',
      autoBackup: customFields.auto_backup || false,
      autoBackupExpiryDate: customFields.auto_backup_expiry_date || 'N/A',
      cloudUser: customFields.cloud_user || false,
      cloudUserExpiryDate: customFields.cloud_user_expiry_date || 'N/A',
      mobileApp: customFields.mobile_app || false,
      mobileAppExpiryDate: customFields.mobile_app_expiry_date || 'N/A',

      // Mock data for services and payments until we implement those APIs
      services: [],
      payments: [],

      // Mock statistics
      stats: {
        totalServices: 0,
        completedServices: 0,
        pendingServices: 0,
        totalRevenue: 0,
        pendingAmount: customerData.pending_amount || 0,
        lastServiceDate: 'N/A'
      }
    };
  };

  // Fetch customer data from API
  useEffect(() => {
    if (id && !rawCustomerData) { // Only fetch if we don't already have customer data
      fetchCustomer();
    }
  }, [id]); // Removed customer from dependencies to prevent infinite loops

  // Transform customer data when raw data changes
  useEffect(() => {
    if (rawCustomerData) {
      const transformed = transformCustomerData(rawCustomerData);
      setCustomer(transformed);
    }
  }, [rawCustomerData]);



  const fetchCustomer = async () => {
    try {
      setLoading(true);
      const response = await apiService.get(`/customers/${id}`);

      if (response.data?.success && response.data?.data?.customer) {
        const customerData = response.data.data.customer;
        setRawCustomerData(customerData); // Store raw data for transformation
      } else {
        toast.error('Customer not found');
        navigate('/customers');
      }
    } catch (error) {
      console.error('Error fetching customer:', error);

      // Handle authentication errors
      if (handleAuthError(error)) {
        return; // Auth error handled, component will unmount
      }

      toast.error('Failed to load customer data');
      navigate('/customers');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this customer? This action cannot be undone.')) {
      try {
        await apiService.delete(`/customers/${id}`);
        toast.success('Customer deleted successfully');
        navigate('/customers');
      } catch (error) {
        console.error('Error deleting customer:', error);
        toast.error('Failed to delete customer');
      }
    }
  };

  const getStatusBadge = (status) => {
    const normalizedStatus = status?.toLowerCase();
    const variant = normalizedStatus === 'active' ? 'success' :
                   normalizedStatus === 'inactive' ? 'secondary' : 'warning';
    return <Badge variant={variant}>{status?.toUpperCase() || 'UNKNOWN'}</Badge>;
  };

  const getServiceStatusBadge = (status) => {
    const variant = status === 'completed' ? 'success' :
                   status === 'pending' ? 'warning' :
                   status === 'in-progress' ? 'info' : 'danger';
    return <Badge variant={variant}>{status.toUpperCase()}</Badge>;
  };

  const getPaymentStatusBadge = (status) => {
    const variant = status === 'received' ? 'success' :
                   status === 'pending' ? 'warning' : 'danger';
    return <Badge variant={variant}>{status.toUpperCase()}</Badge>;
  };

  if (loading) {
    return (
      <LoadingScreen
        title="Loading Customer Details..."
        subtitle="Fetching customer information and history"
        variant="page"
      />
    );
  }

  if (!customer) {
    return (
      <div className="w-full">
        <Alert variant="danger">
          Customer not found.
        </Alert>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>{customer.name} - Customer Details - TallyCRM</title>
      </Helmet>

      <div className="w-full">
        {/* Header */}
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between space-y-4 sm:space-y-0">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">{customer.name}</h1>
              <p className="text-gray-600 mb-3">{customer.contactPerson} • {customer.designation}</p>
              <div className="flex flex-wrap items-center gap-3">
                {getStatusBadge(customer.customerStatus)}
                <Badge variant="info">{customer.tallyVersion}</Badge>
                <span className="text-sm text-gray-600 flex items-center">
                  <i className="bi bi-calendar mr-1"></i>
                  Customer since {customer.registrationDate}
                </span>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <Button
                variant="outline"
                onClick={() => navigate(`/customers/${customer.id}/edit`)}
                className="flex items-center"
              >
                <i className="bi bi-pencil mr-2"></i>
                Edit
              </Button>
              <Button
                variant="danger"
                onClick={handleDelete}
                className="flex items-center"
              >
                <i className="bi bi-trash mr-2"></i>
                Delete
              </Button>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card className="bg-gradient-to-r from-purple-600 to-purple-700 text-white border-0 shadow-lg">
            <CardBody className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-2xl font-bold text-white mb-1">{customer.stats.totalServices}</h3>
                  <p className="text-purple-100 text-sm">Total Services</p>
                </div>
                <i className="bi bi-tools text-2xl text-purple-200"></i>
              </div>
            </CardBody>
          </Card>

          <Card className="bg-gradient-to-r from-green-600 to-green-700 text-white border-0 shadow-lg">
            <CardBody className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-2xl font-bold text-white mb-1">₹{customer.stats.totalRevenue.toLocaleString()}</h3>
                  <p className="text-green-100 text-sm">Total Revenue</p>
                </div>
                <i className="bi bi-currency-rupee text-2xl text-green-200"></i>
              </div>
            </CardBody>
          </Card>

          <Card className="bg-gradient-to-r from-yellow-600 to-yellow-700 text-white border-0 shadow-lg">
            <CardBody className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-2xl font-bold text-white mb-1">₹{customer.stats.pendingAmount.toLocaleString()}</h3>
                  <p className="text-yellow-100 text-sm">Pending Amount</p>
                </div>
                <i className="bi bi-file-earmark-text text-2xl text-yellow-200"></i>
              </div>
            </CardBody>
          </Card>

          <Card className="bg-gradient-to-r from-blue-600 to-blue-700 text-white border-0 shadow-lg">
            <CardBody className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-2xl font-bold text-white mb-1">{customer.stats.pendingServices}</h3>
                  <p className="text-blue-100 text-sm">Pending Services</p>
                </div>
                <i className="bi bi-graph-up text-2xl text-blue-200"></i>
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'overview'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('overview')}
              >
                Overview
              </button>
              <button
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'services'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('services')}
              >
                Services ({customer.services.length})
              </button>
              <button
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'payments'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('payments')}
              >
                Payments ({customer.payments.length})
              </button>
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Basic Information Row */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Tally Information */}
              <Card>
                <CardHeader className="bg-blue-50 border-b">
                  <h5 className="text-lg font-semibold text-blue-900 flex items-center">
                    <i className="bi bi-calculator mr-2"></i>
                    Tally Information
                  </h5>
                </CardHeader>
                <CardBody className="p-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Tally Serial Number:</label>
                      <p className="text-gray-900 font-mono">{customer.tallySerialNumber}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">License Edition:</label>
                      <p className="text-gray-900">{customer.licenseEdition}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Tally Version:</label>
                      <p className="text-gray-900">{customer.tallyVersion}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Installation Date:</label>
                      <p className="text-gray-900">{customer.installationDate}</p>
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* Profile Information */}
              <Card>
                <CardHeader className="bg-purple-50 border-b">
                  <h5 className="text-lg font-semibold text-purple-900 flex items-center">
                    <i className="bi bi-person-badge mr-2"></i>
                    Profile Information
                  </h5>
                </CardHeader>
                <CardBody className="p-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Profile Status:</label>
                      <p className="text-gray-900">{customer.profileStatus}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Customer Status:</label>
                      <div className="mt-1">
                        {getStatusBadge(customer.customerStatus)}
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Follow-up Executive:</label>
                      <p className="text-gray-900">{customer.followUpExecutive}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Location:</label>
                      <p className="text-gray-900">{customer.location}</p>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </div>

            {/* Company Information Row */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Company Information */}
              <Card>
                <CardHeader className="bg-green-50 border-b">
                  <h5 className="text-lg font-semibold text-green-900 flex items-center">
                    <i className="bi bi-building mr-2"></i>
                    Company Information
                  </h5>
                </CardHeader>
                <CardBody className="p-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Business Type:</label>
                      <p className="text-gray-900">{customer.businessType}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Industry:</label>
                      <p className="text-gray-900">{customer.industry}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">GST Number:</label>
                      <p className="text-gray-900">{customer.gstNumber}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">PAN Number:</label>
                      <p className="text-gray-900">{customer.panNumber}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Annual Turnover:</label>
                      <p className="text-gray-900">{customer.annualTurnover}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Employee Count:</label>
                      <p className="text-gray-900">{customer.employeeCount}</p>
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* Map Location */}
              <Card>
                <CardHeader className="bg-orange-50 border-b">
                  <h5 className="text-lg font-semibold text-orange-900 flex items-center">
                    <i className="bi bi-geo-alt mr-2"></i>
                    Location Information
                  </h5>
                </CardHeader>
                <CardBody className="p-6">
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Map Location:</label>
                      <p className="text-gray-900">{customer.mapLocation}</p>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Latitude:</label>
                        <p className="text-gray-900 font-mono">{customer.latitude}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Longitude:</label>
                        <p className="text-gray-900 font-mono">{customer.longitude}</p>
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Full Address:</label>
                      <p className="text-gray-900">
                        {customer.address}<br />
                        {customer.city}, {customer.state} - {customer.pincode}<br />
                        {customer.country}
                      </p>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </div>

            {/* Contact Address Book */}
            <Card>
              <CardHeader className="bg-indigo-50 border-b">
                <h5 className="text-lg font-semibold text-indigo-900 flex items-center">
                  <i className="bi bi-person-lines-fill mr-2"></i>
                  Contact Address Book
                </h5>
              </CardHeader>
              <CardBody className="p-6">
                {customer.addressBook && customer.addressBook.length > 0 ? (
                  <div className="space-y-4">
                    {customer.addressBook.map((contact, index) => (
                      <div key={index} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                          <div>
                            <label className="text-sm font-medium text-gray-700">Type:</label>
                            <p className="text-gray-900 font-semibold">{contact.type || 'N/A'}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">Contact Person:</label>
                            <p className="text-gray-900">{contact.contact_person || 'N/A'}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">Phone:</label>
                            <p className="text-gray-900">{contact.phone || 'N/A'}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">Email:</label>
                            <p className="text-gray-900">{contact.email || 'N/A'}</p>
                          </div>
                          {contact.mobile_numbers && contact.mobile_numbers.length > 0 && (
                            <div className="md:col-span-2 lg:col-span-4">
                              <label className="text-sm font-medium text-gray-700">Mobile Numbers:</label>
                              <div className="flex flex-wrap gap-2 mt-1">
                                {contact.mobile_numbers.map((mobile, mobileIndex) => (
                                  <span key={mobileIndex} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                                    {mobile}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">No contact information available.</p>
                )}
              </CardBody>
            </Card>

            {/* TSS and AMC Status Row */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* TSS Status */}
              <Card>
                <CardHeader className="bg-emerald-50 border-b">
                  <h5 className="text-lg font-semibold text-emerald-900 flex items-center">
                    <i className="bi bi-shield-check mr-2"></i>
                    TSS Status
                  </h5>
                </CardHeader>
                <CardBody className="p-6">
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700">TSS Status:</label>
                      <div className="mt-1">
                        <Badge variant={customer.tssStatus === 'YES' ? 'success' : 'secondary'}>
                          {customer.tssStatus === 'YES' ? '✅ Active' : '❌ Inactive'}
                        </Badge>
                      </div>
                    </div>
                    {customer.tssStatus === 'YES' && (
                      <>
                        <div>
                          <label className="text-sm font-medium text-gray-700">TSS Expiry Date:</label>
                          <p className="text-gray-900">{customer.tssExpiryDate}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-700">Admin Email:</label>
                          <p className="text-gray-900">{customer.adminEmail}</p>
                        </div>
                      </>
                    )}
                  </div>
                </CardBody>
              </Card>

              {/* AMC Status */}
              <Card>
                <CardHeader className="bg-red-50 border-b">
                  <h5 className="text-lg font-semibold text-red-900 flex items-center">
                    <i className="bi bi-calendar-check mr-2"></i>
                    AMC Status
                  </h5>
                </CardHeader>
                <CardBody className="p-6">
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700">AMC Status:</label>
                      <div className="mt-1">
                        <Badge variant={customer.amcStatus === 'YES' ? 'success' : 'secondary'}>
                          {customer.amcStatus === 'YES' ? '✅ Active' : '❌ Inactive'}
                        </Badge>
                      </div>
                    </div>
                    {customer.amcStatus === 'YES' && (
                      <>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm font-medium text-gray-700">From Date:</label>
                            <p className="text-gray-900">{customer.amcFromDate}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">To Date:</label>
                            <p className="text-gray-900">{customer.amcToDate}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">Renewal Date:</label>
                            <p className="text-gray-900">{customer.renewalDate}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">No of Visits:</label>
                            <p className="text-gray-900">{customer.noOfVisits}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">Current AMC Amount:</label>
                            <p className="text-gray-900">₹{customer.currentAmcAmount}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">Last Year AMC Amount:</label>
                            <p className="text-gray-900">₹{customer.lastYearAmcAmount}</p>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </CardBody>
              </Card>
            </div>

            {/* Additional Features */}
            <div className="grid grid-cols-1 gap-6">
              {/* Additional Features */}
              <Card>
                <CardHeader className="bg-violet-50 border-b">
                  <h5 className="text-lg font-semibold text-violet-900 flex items-center">
                    <i className="bi bi-star mr-2"></i>
                    Additional Features
                  </h5>
                </CardHeader>
                <CardBody className="p-6">
                  {(() => {
                    // Helper function to check if date is expired
                    const isExpired = (dateString) => {
                      if (!dateString || dateString === 'N/A') return false;
                      const expiryDate = new Date(dateString);
                      const today = new Date();
                      return expiryDate < today;
                    };

                    // Helper function to render feature item
                    const renderFeature = (name, isActive, expiryDate) => {
                      if (!isActive) return null; // Don't show inactive features

                      const expired = isExpired(expiryDate);

                      return (
                        <div key={name} className="flex items-center justify-between">
                          <div className="flex items-center">
                            <Badge variant="success" className="mr-2">
                              ✅
                            </Badge>
                            <span className="text-gray-900">{name}</span>
                          </div>
                          {expiryDate && expiryDate !== 'N/A' && (
                            <span className={`text-sm ${expired ? 'text-red-600 font-semibold' : 'text-gray-600'}`}>
                              {expired ? '🔴 Expired: ' : 'Expires: '}{expiryDate}
                            </span>
                          )}
                        </div>
                      );
                    };

                    // Collect all active features
                    const activeFeatures = [
                      customer.tdlAddons && renderFeature('TDL & Addons', customer.tdlAddons, customer.tdlAddonsExpiryDate),
                      customer.whatsappTelegramGroup && renderFeature('WhatsApp/Telegram Group', customer.whatsappTelegramGroup, customer.whatsappTelegramGroupExpiryDate),
                      customer.autoBackup && renderFeature('Auto Backup', customer.autoBackup, customer.autoBackupExpiryDate),
                      customer.cloudUser && renderFeature('Cloud User', customer.cloudUser, customer.cloudUserExpiryDate),
                      customer.mobileApp && renderFeature('Mobile App', customer.mobileApp, customer.mobileAppExpiryDate),
                    ].filter(Boolean); // Remove null values

                    return activeFeatures.length > 0 ? (
                      <div className="space-y-3">
                        {activeFeatures}
                      </div>
                    ) : (
                      <p className="text-gray-500">No additional features activated.</p>
                    );
                  })()}
                </CardBody>
              </Card>
            </div>

            {/* Remarks */}
            <Card>
              <CardHeader className="bg-gray-50 border-b">
                <h5 className="text-lg font-semibold text-gray-900 flex items-center">
                  <i className="bi bi-chat-text mr-2"></i>
                  Remarks
                </h5>
              </CardHeader>
              <CardBody className="p-6">
                <p className="text-gray-900 whitespace-pre-wrap">{customer.remarks}</p>
              </CardBody>
            </Card>
          </div>
        )}

        {/* Services and Payments tabs would go here - simplified for now */}
        {activeTab === 'services' && (
          <Card>
            <CardHeader>
              <h5 className="text-lg font-semibold text-gray-900">Service History</h5>
            </CardHeader>
            <CardBody>
              <p className="text-gray-500">No services found for this customer.</p>
            </CardBody>
          </Card>
        )}

        {activeTab === 'payments' && (
          <Card>
            <CardHeader>
              <h5 className="text-lg font-semibold text-gray-900">Payment History</h5>
            </CardHeader>
            <CardBody>
              <p className="text-gray-500">No payments found for this customer.</p>
            </CardBody>
          </Card>
        )}
      </div>
    </>
  );
};

export default CustomerDetails;
