import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { Helmet } from 'react-helmet-async';
import { apiService } from '../../services/api';
import { handleAuthError } from '../../utils/authErrorHandler';
import { Card, CardHeader, CardBody, Button, Badge, Spinner, Alert } from '../../components/ui';
import LoadingScreen from '../../components/ui/LoadingScreen';

const CustomerDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [customer, setCustomer] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  // Fetch customer data from API
  useEffect(() => {
    if (id && !customer) { // Only fetch if we don't already have customer data
      fetchCustomer();
    }
  }, [id]); // Removed customer from dependencies to prevent infinite loops

  const fetchCustomer = async () => {
    // Prevent multiple simultaneous requests
    if (loading) {
      return;
    }

    try {
      setLoading(true);
      const response = await apiService.get(`/customers/${id}`);

      if (response.data?.success && response.data?.data?.customer) {
        const customerData = response.data.data.customer;
        const customFields = customerData.custom_fields || {};

        // Transform API data to match component expectations (Task 8 fix)
        const transformedCustomer = {
          id: customerData.id,
          name: customerData.company_name || customerData.display_name || 'N/A',
          contactPerson: customFields.address_book?.[0]?.contact_person || customerData.contact_person || 'N/A',
          designation: customFields.address_book?.[0]?.type || customerData.designation || 'N/A',
          email: customerData.email || customFields.address_book?.[0]?.email || 'N/A',
          phone: customerData.phone || customFields.address_book?.[0]?.mobile_numbers?.[0] || 'N/A',
          alternatePhone: customFields.address_book?.[0]?.phone || customerData.alternate_phone || 'N/A',
          address: customerData.address_line_1 || customerData.address || customFields.map_location || 'N/A',
          city: customerData.city || 'N/A',
          state: customerData.state || 'N/A',
          pincode: customerData.postal_code || customerData.pincode || 'N/A',
          country: customerData.country || 'India',
          businessType: customerData.business_type || 'N/A',
          industry: customerData.industry?.name || customerData.industry || 'N/A',
          gstNumber: customerData.gst_number || 'N/A',
          panNumber: customerData.pan_number || 'N/A',
          annualTurnover: customerData.annual_turnover ? `${customerData.annual_turnover} Lakhs` : 'N/A',
          employeeCount: customerData.employee_count || 'N/A',
          status: customerData.is_active ? 'active' : 'inactive',
          tallyVersion: customFields.tally_version || customerData.tally_version || 'TallyPrime',
          tallySerialNumber: customerData.tally_serial_number || customerData.customer_code || 'N/A',
          licenseType: customFields.license_edition_id || customerData.license_type || 'N/A',
          installationDate: customerData.installation_date ? new Date(customerData.installation_date).toLocaleDateString() : 'N/A',
          registrationDate: customerData.created_at ? new Date(customerData.created_at).toLocaleDateString() : 'N/A',
          lastContact: customerData.updated_at ? new Date(customerData.updated_at).toLocaleDateString() : 'N/A',
          notes: customerData.notes || 'No notes available',

          // Additional fields from custom_fields
          profileStatus: customFields.profile_status || 'N/A',
          tssStatus: customFields.tss_status || 'NO',
          tssExpiryDate: customFields.tss_expiry_date || 'N/A',
          amcStatus: customFields.amc_status || 'NO',
          amcFromDate: customFields.amc_from_date || 'N/A',
          amcToDate: customFields.amc_to_date || 'N/A',
          tdlAddonsStatus: customFields.tdl_addons_status || 'NO',
          tdlAddonsExpiryDate: customFields.tdl_addons_expiry_date || 'N/A',

          // Mock data for services and payments until we implement those APIs
          services: [],
          payments: [],

          // Mock statistics
          stats: {
            totalServices: 0,
            completedServices: 0,
            pendingServices: 0,
            totalRevenue: 0,
            pendingAmount: customerData.pending_amount || 0,
            lastServiceDate: 'N/A'
          }
        };

        setCustomer(transformedCustomer);
      } else {
        toast.error('Customer not found');
        navigate('/customers');
      }
    } catch (error) {
      console.error('Error fetching customer:', error);

      // Handle authentication errors
      if (handleAuthError(error)) {
        return; // Auth error handled, component will unmount
      }

      toast.error('Failed to load customer data');
      navigate('/customers');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this customer? This action cannot be undone.')) {
      try {
        await apiService.delete(`/customers/${id}`);
        toast.success('Customer deleted successfully');
        navigate('/customers');
      } catch (error) {
        console.error('Error deleting customer:', error);
        toast.error('Failed to delete customer');
      }
    }
  };

  const getStatusBadge = (status) => {
    const variant = status === 'active' ? 'success' :
                   status === 'inactive' ? 'secondary' : 'warning';
    return <Badge variant={variant}>{status.toUpperCase()}</Badge>;
  };

  const getServiceStatusBadge = (status) => {
    const variant = status === 'completed' ? 'success' :
                   status === 'pending' ? 'warning' :
                   status === 'in-progress' ? 'info' : 'danger';
    return <Badge variant={variant}>{status.toUpperCase()}</Badge>;
  };

  const getPaymentStatusBadge = (status) => {
    const variant = status === 'received' ? 'success' :
                   status === 'pending' ? 'warning' : 'danger';
    return <Badge variant={variant}>{status.toUpperCase()}</Badge>;
  };

  if (loading) {
    return (
      <LoadingScreen
        title="Loading Customer Details..."
        subtitle="Fetching customer information and history"
        variant="page"
      />
    );
  }

  if (!customer) {
    return (
      <div className="w-full">
        <Alert variant="danger">
          Customer not found.
        </Alert>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>{customer.name} - Customer Details - TallyCRM</title>
      </Helmet>

      <div className="w-full">
        {/* Header */}
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between space-y-4 sm:space-y-0">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">{customer.name}</h1>
              <p className="text-gray-600 mb-3">{customer.contactPerson} • {customer.designation}</p>
              <div className="flex flex-wrap items-center gap-3">
                {getStatusBadge(customer.status)}
                <Badge variant="info">{customer.tallyVersion}</Badge>
                <span className="text-sm text-gray-600 flex items-center">
                  <i className="bi bi-calendar mr-1"></i>
                  Customer since {customer.registrationDate}
                </span>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <Button
                variant="outline"
                onClick={() => navigate(`/customers/${customer.id}/edit`)}
                className="flex items-center"
              >
                <i className="bi bi-pencil mr-2"></i>
                Edit
              </Button>
              <Button
                variant="danger"
                onClick={handleDelete}
                className="flex items-center"
              >
                <i className="bi bi-trash mr-2"></i>
                Delete
              </Button>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card className="bg-gradient-to-r from-purple-600 to-purple-700 text-white border-0 shadow-lg">
            <CardBody className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-2xl font-bold text-white mb-1">{customer.stats.totalServices}</h3>
                  <p className="text-purple-100 text-sm">Total Services</p>
                </div>
                <i className="bi bi-tools text-2xl text-purple-200"></i>
              </div>
            </CardBody>
          </Card>

          <Card className="bg-gradient-to-r from-green-600 to-green-700 text-white border-0 shadow-lg">
            <CardBody className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-2xl font-bold text-white mb-1">₹{customer.stats.totalRevenue.toLocaleString()}</h3>
                  <p className="text-green-100 text-sm">Total Revenue</p>
                </div>
                <i className="bi bi-currency-rupee text-2xl text-green-200"></i>
              </div>
            </CardBody>
          </Card>

          <Card className="bg-gradient-to-r from-yellow-600 to-yellow-700 text-white border-0 shadow-lg">
            <CardBody className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-2xl font-bold text-white mb-1">₹{customer.stats.pendingAmount.toLocaleString()}</h3>
                  <p className="text-yellow-100 text-sm">Pending Amount</p>
                </div>
                <i className="bi bi-file-earmark-text text-2xl text-yellow-200"></i>
              </div>
            </CardBody>
          </Card>

          <Card className="bg-gradient-to-r from-blue-600 to-blue-700 text-white border-0 shadow-lg">
            <CardBody className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-2xl font-bold text-white mb-1">{customer.stats.pendingServices}</h3>
                  <p className="text-blue-100 text-sm">Pending Services</p>
                </div>
                <i className="bi bi-graph-up text-2xl text-blue-200"></i>
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'overview'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('overview')}
              >
                Overview
              </button>
              <button
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'services'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('services')}
              >
                Services ({customer.services.length})
              </button>
              <button
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'payments'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('payments')}
              >
                Payments ({customer.payments.length})
              </button>
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Company Information */}
            <Card className="h-full">
              <CardHeader className="bg-gray-50 border-b">
                <h5 className="text-lg font-semibold text-gray-900 flex items-center">
                  <i className="bi bi-building mr-2"></i>
                  Company Information
                </h5>
              </CardHeader>
              <CardBody className="p-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Business Type:</label>
                    <p className="text-gray-900">{customer.businessType}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Industry:</label>
                    <p className="text-gray-900">{customer.industry}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">GST Number:</label>
                    <p className="text-gray-900">{customer.gstNumber}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">PAN Number:</label>
                    <p className="text-gray-900">{customer.panNumber}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Annual Turnover:</label>
                    <p className="text-gray-900">{customer.annualTurnover}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Employee Count:</label>
                    <p className="text-gray-900">{customer.employeeCount}</p>
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* Contact Information */}
            <Card className="h-full">
              <CardHeader className="bg-gray-50 border-b">
                <h5 className="text-lg font-semibold text-gray-900 flex items-center">
                  <i className="bi bi-person mr-2"></i>
                  Contact Information
                </h5>
              </CardHeader>
              <CardBody className="p-6">
                <div className="space-y-4">
                  <div className="flex items-center">
                    <i className="bi bi-telephone text-gray-600 mr-3"></i>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Primary Phone:</label>
                      <p className="text-gray-900">{customer.phone}</p>
                    </div>
                  </div>
                  {customer.alternatePhone && (
                    <div className="flex items-center">
                      <i className="bi bi-telephone text-gray-600 mr-3"></i>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Alternate Phone:</label>
                        <p className="text-gray-900">{customer.alternatePhone}</p>
                      </div>
                    </div>
                  )}
                  <div className="flex items-center">
                    <i className="bi bi-envelope text-gray-600 mr-3"></i>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Email:</label>
                      <p className="text-gray-900">{customer.email}</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <i className="bi bi-geo-alt text-gray-600 mr-3 mt-1"></i>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Address:</label>
                      <p className="text-gray-900">
                        {customer.address}<br />
                        {customer.city}, {customer.state} - {customer.pincode}<br />
                        {customer.country}
                      </p>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>
        )}

        {/* Services and Payments tabs would go here - simplified for now */}
        {activeTab === 'services' && (
          <Card>
            <CardHeader>
              <h5 className="text-lg font-semibold text-gray-900">Service History</h5>
            </CardHeader>
            <CardBody>
              <p className="text-gray-500">No services found for this customer.</p>
            </CardBody>
          </Card>
        )}

        {activeTab === 'payments' && (
          <Card>
            <CardHeader>
              <h5 className="text-lg font-semibold text-gray-900">Payment History</h5>
            </CardHeader>
            <CardBody>
              <p className="text-gray-500">No payments found for this customer.</p>
            </CardBody>
          </Card>
        )}
      </div>
    </>
  );
};

export default CustomerDetails;
