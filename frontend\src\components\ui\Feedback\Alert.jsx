import React from 'react';
import { cn } from '../../../utils/helpers';

const Alert = React.forwardRef(({
  children,
  className,
  variant = 'primary',
  dismissible = false,
  onClose,
  icon = true,
  ...props
}, ref) => {
  const baseClasses = 'p-4 rounded border-l-4 relative';

  const variants = {
    primary: 'alert-primary',
    secondary: 'bg-gray-50 border-gray-500 text-gray-800',
    success: 'bg-green-50 border-green-600 text-green-800',
    danger: 'bg-red-50 border-red-600 text-red-800',
    warning: 'bg-yellow-50 border-yellow-500 text-yellow-800',
    info: 'bg-blue-50 border-blue-500 text-blue-800',
  };

  const icons = {
    primary: 'bi-info-circle',
    secondary: 'bi-info-circle',
    success: 'bi-check-circle',
    danger: 'bi-exclamation-triangle',
    warning: 'bi-exclamation-triangle',
    info: 'bi-info-circle',
  };

  return (
    <div
      ref={ref}
      className={cn(baseClasses, variants[variant], className)}
      role="alert"
      {...props}
    >
      <div className="flex">
        {icon && (
          <div className="flex-shrink-0">
            <i className={`bi ${icons[variant]} text-lg`}></i>
          </div>
        )}
        <div className={cn('flex-1', icon && 'ml-3')}>
          {children}
        </div>
        {dismissible && (
          <div className="flex-shrink-0 ml-4">
            <button
              type="button"
              className="inline-flex text-current hover:opacity-75 focus:outline-none focus:opacity-75"
              onClick={onClose}
            >
              <span className="sr-only">Close</span>
              <i className="bi bi-x text-lg"></i>
            </button>
          </div>
        )}
      </div>
    </div>
  );
});

const AlertHeading = React.forwardRef(({
  children,
  className,
  ...props
}, ref) => {
  return (
    <h4
      ref={ref}
      className={cn('text-lg font-semibold mb-2', className)}
      {...props}
    >
      {children}
    </h4>
  );
});

Alert.displayName = 'Alert';
AlertHeading.displayName = 'AlertHeading';

export default Alert;
export { AlertHeading };
