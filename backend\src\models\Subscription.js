import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const Subscription = sequelize.define('Subscription', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
    },
    plan_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'subscription_plans',
        key: 'id',
      },
    },
    stripe_subscription_id: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: true,
    },
    stripe_customer_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    status: {
      type: DataTypes.ENUM(
        'trial',
        'active',
        'past_due',
        'canceled',
        'unpaid',
        'incomplete',
        'incomplete_expired',
        'paused'
      ),
      defaultValue: 'trial',
    },
    current_period_start: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    current_period_end: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    trial_start: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    trial_end: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    cancel_at_period_end: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    canceled_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    ended_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    quantity: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    currency: {
      type: DataTypes.STRING(3),
      defaultValue: 'INR',
    },
    interval: {
      type: DataTypes.ENUM('month', 'year'),
      defaultValue: 'month',
    },
    interval_count: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
    },
    metadata: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'subscriptions',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['tenant_id'],
        unique: true,
      },
      {
        fields: ['stripe_subscription_id'],
        unique: true,
        where: {
          stripe_subscription_id: {
            [sequelize.Sequelize.Op.ne]: null,
          },
        },
      },
      {
        fields: ['status'],
      },
      {
        fields: ['current_period_end'],
      },
    ],
  });

  // Instance methods
  Subscription.prototype.isActive = function() {
    return ['trial', 'active'].includes(this.status) && 
           new Date() <= new Date(this.current_period_end);
  };

  Subscription.prototype.isInTrial = function() {
    return this.status === 'trial' && 
           this.trial_end && 
           new Date() <= new Date(this.trial_end);
  };

  Subscription.prototype.daysUntilExpiry = function() {
    const now = new Date();
    const expiry = new Date(this.current_period_end);
    const diffTime = expiry - now;
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  Subscription.prototype.canUpgrade = function() {
    return this.isActive() && !this.cancel_at_period_end;
  };

  Subscription.prototype.canDowngrade = function() {
    return this.isActive() && !this.cancel_at_period_end;
  };

  // Associations
  Subscription.associate = function(models) {
    Subscription.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });

    Subscription.belongsTo(models.SubscriptionPlan, {
      foreignKey: 'plan_id',
      as: 'plan',
    });

    Subscription.hasMany(models.Invoice, {
      foreignKey: 'subscription_id',
      as: 'invoices',
    });

    Subscription.hasMany(models.UsageRecord, {
      foreignKey: 'subscription_id',
      as: 'usageRecords',
    });
  };

  return Subscription;
}
