// Test script to verify theme color API functionality
const fetch = require('node-fetch');

const API_BASE = 'http://localhost:3002/api';

// Test credentials - replace with actual user credentials
const testCredentials = {
  email: '<EMAIL>',
  password: 'Admin@123'
};

async function testThemeAPI() {
  try {
    console.log('🔐 Logging in...');
    
    // Login to get token
    const loginResponse = await fetch(`${API_BASE}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testCredentials)
    });
    
    const loginData = await loginResponse.json();
    
    if (!loginData.success) {
      console.error('❌ Login failed:', loginData.message);
      return;
    }
    
    const token = loginData.data.token;
    console.log('✅ Login successful');
    
    // Get current user preferences
    console.log('\n📖 Getting current user preferences...');
    const getUserResponse = await fetch(`${API_BASE}/settings/user`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      }
    });
    
    const userData = await getUserResponse.json();
    console.log('Current user data:', JSON.stringify(userData, null, 2));
    
    // Update theme color
    console.log('\n🎨 Updating theme color to #ff6b6b...');
    const updateResponse = await fetch(`${API_BASE}/settings/user`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        primary_color: '#ff6b6b'
      })
    });
    
    const updateData = await updateResponse.json();
    console.log('Update response:', JSON.stringify(updateData, null, 2));
    
    // Get updated user preferences
    console.log('\n📖 Getting updated user preferences...');
    const getUpdatedUserResponse = await fetch(`${API_BASE}/settings/user`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      }
    });
    
    const updatedUserData = await getUpdatedUserResponse.json();
    console.log('Updated user data:', JSON.stringify(updatedUserData, null, 2));
    
    // Verify the color was saved
    const savedColor = updatedUserData.data?.user?.primary_color;
    if (savedColor === '#ff6b6b') {
      console.log('\n✅ Theme color successfully saved and retrieved!');
    } else {
      console.log('\n❌ Theme color was not saved correctly. Expected: #ff6b6b, Got:', savedColor);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testThemeAPI();
