# TallyCRM Backend

Backend API for TallyCRM - A comprehensive CRM solution for Tally resellers and service providers.

## Features

- **Multi-tenant SaaS Architecture**: Complete tenant isolation with subscription management
- **Comprehensive Customer Management**: Track prospects, customers, contacts, and relationships
- **Service Call Management**: Handle support tickets, track resolution, and manage SLAs
- **Sales Management**: Quotations, orders, invoicing, and commission tracking
- **AMC Management**: Annual maintenance contracts with usage tracking
- **TSS Management**: Tally Software Service license tracking
- **Role-based Access Control**: Granular permissions and role management
- **Master Data Management**: Industries, areas, products, services, and more

## Tech Stack

- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Database**: PostgreSQL with Sequelize ORM
- **Authentication**: JWT with bcrypt
- **Validation**: express-validator
- **Logging**: Winston
- **Documentation**: Swagger/OpenAPI

## Quick Start

### Prerequisites

- Node.js 18+ and npm 9+
- PostgreSQL 12+
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/cloudstier/tallycrm.git
   cd tallycrm/backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env
   # Edit .env with your database credentials and other settings
   ```

4. **Database setup**
   ```bash
   # Create database
   npm run db:create
   
   # Run migrations and seed data
   npm run db:setup
   
   # Or create with default admin user
   node scripts/setup.js --create-admin
   ```

5. **Start the server**
   ```bash
   # Development mode
   npm run dev
   
   # Production mode
   npm start
   ```

The API will be available at `http://localhost:3000`

### Default Admin Credentials (if created)

- **Email**: <EMAIL>
- **Password**: Admin@123
- **Company**: TallyCRM Demo

⚠️ **Important**: Change the default password after first login!

## API Documentation

Once the server is running, visit:
- **Swagger UI**: http://localhost:3000/api-docs
- **Health Check**: http://localhost:3000/health

## Database Management

### Migrations

```bash
# Run all pending migrations
npm run migrate

# Rollback last migration
npm run migrate:down

# Rollback multiple migrations
npm run migrate:down 3
```

### Seeding

```bash
# Seed master data
npm run seed

# Complete database reset
npm run db:reset
```

## Project Structure

```
src/
├── config/           # Configuration files
├── controllers/      # Route controllers
├── middleware/       # Express middleware
├── models/          # Sequelize models
│   └── masters/     # Master data models
├── routes/          # API routes
├── migrations/      # Database migrations
├── seeders/         # Database seeders
├── utils/           # Utility functions
└── server.js        # Application entry point
```

## Key Models

### Core Models
- **Tenant**: Multi-tenant organization
- **User**: System users with roles
- **Role/Permission**: RBAC system

### Master Data
- **LicenseEdition**: Tally license types
- **TallyProduct**: Products and services
- **Industry**: Customer industries
- **Area**: Geographic areas
- **Executive**: Sales/support staff
- **CallStatus**: Service call statuses
- **NatureOfIssue**: Issue categories

### Business Models
- **Customer**: Customer management
- **CustomerContact**: Customer contacts
- **CustomerTSS**: Tally license tracking
- **CustomerAMC**: Maintenance contracts
- **ServiceCall**: Support tickets
- **Sale**: Sales transactions
- **Referral**: Customer referrals

## API Endpoints

### Authentication
- `POST /api/v1/auth/register` - Register new tenant and user
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/verify-email` - Email verification
- `GET /api/v1/auth/profile` - Get user profile

### Customers
- `GET /api/v1/customers` - List customers
- `POST /api/v1/customers` - Create customer
- `GET /api/v1/customers/:id` - Get customer details
- `PUT /api/v1/customers/:id` - Update customer
- `DELETE /api/v1/customers/:id` - Delete customer
- `GET /api/v1/customers/stats` - Customer statistics

### Service Calls
- `GET /api/v1/service-calls` - List service calls
- `POST /api/v1/service-calls` - Create service call
- `GET /api/v1/service-calls/:id` - Get service call details
- `PUT /api/v1/service-calls/:id` - Update service call
- `GET /api/v1/service-calls/stats` - Service call statistics

## Environment Variables

```env
# Server Configuration
NODE_ENV=development
PORT=3000
API_PREFIX=/api/v1

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tallycrm_dev
DB_USERNAME=postgres
DB_PASSWORD=password

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=24h

# Email Configuration (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Frontend URL (for email links)
FRONTEND_URL=http://localhost:3001
```

## Development

### Code Style

```bash
# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format
```

### Testing

```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage
```

### Database Development

```bash
# Create new migration
# (Create manually in src/migrations/ following the naming convention)

# Create new seeder
# (Create manually in src/seeders/)

# Reset database for development
npm run db:reset
```

## Deployment

### Production Setup

1. **Environment Configuration**
   ```bash
   NODE_ENV=production
   # Set production database credentials
   # Set secure JWT secret
   # Configure email settings
   ```

2. **Database Setup**
   ```bash
   npm run db:create
   npm run migrate
   npm run seed
   ```

3. **Start Application**
   ```bash
   npm start
   ```

### Docker Deployment

```dockerfile
# Dockerfile example
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Run linting and tests
6. Submit a pull request

## License

This project is proprietary software owned by Cloudstier Solutions.

## Support

For support and questions:
- Email: <EMAIL>
- Documentation: [TallyCRM Docs](https://docs.tallycrm.com)
- Issues: [GitHub Issues](https://github.com/cloudstier/tallycrm/issues)
