import React, { useState } from 'react';
import { Outlet } from 'react-router-dom';
import Sidebar from './Sidebar';
import Header from './Header';
import Footer from './Footer';

const MainLayout = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [sidebarVisible, setSidebarVisible] = useState(false);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const toggleSidebarMobile = () => {
    setSidebarVisible(!sidebarVisible);
  };

  return (
    <div className="flex min-h-screen overflow-x-hidden">
      <Sidebar
        collapsed={sidebarCollapsed}
        visible={sidebarVisible}
        onToggle={toggleSidebar}
        onClose={() => setSidebarVisible(false)}
      />

      <div className={`flex-1 flex flex-col min-h-screen transition-all duration-300 ease-in-out overflow-x-hidden ${
        sidebarCollapsed ? 'md:ml-16' : 'md:ml-[220px]'
      } ml-0`}>
        <Header
          onToggleSidebar={toggleSidebar}
          onToggleSidebarMobile={toggleSidebarMobile}
          sidebarCollapsed={sidebarCollapsed}
        />

        <main className="flex-1 p-2 sm:p-3 md:p-4 lg:p-6 bg-gray-50 overflow-x-hidden w-full min-w-0">
          <div className="w-full max-w-full overflow-x-hidden min-w-0">
            <Outlet />
          </div>
        </main>

        <Footer />
      </div>
    </div>
  );
};

export default MainLayout;
