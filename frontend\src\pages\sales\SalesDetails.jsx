import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import {
  FaEdit,
  FaTrash,
  FaUser,
  FaRupeeSign,
  FaCalendar,
  FaChartLine,
  FaPhone,
  FaEnvelope,
  FaCheckCircle,
  FaTimesCircle,
  FaArrowUp,
  FaArrowDown,
  FaPlus
} from 'react-icons/fa';
import LoadingScreen from '../../components/ui/LoadingScreen';

const SalesDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [sale, setSale] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('details');

  // Mock data - replace with API call
  useEffect(() => {
    const mockSale = {
      id: 1,
      leadNumber: 'LEAD-2024-001',
      customer: 'ABC Enterprises',
      customerId: 1,
      contactPerson: '<PERSON>',
      email: '<EMAIL>',
      phone: '+91 **********',
      product: 'Tally Prime',
      productType: 'Software License',
      description: 'Tally Prime multi-user license for 10 users with implementation support',
      stage: 'proposal',
      status: 'active',
      priority: 'high',
      source: 'Website',
      assignedTo: 'Rahul Sharma',
      assignedToId: 1,
      expectedValue: 50000,
      probability: 75,
      currency: 'INR',
      expectedCloseDate: '2024-02-15',
      createdDate: '2024-01-10',
      lastActivity: '2024-01-18',
      requirements: 'Multi-user setup with remote access capability and data migration from existing system',
      notes: 'Customer is very interested, decision expected by month end. Budget approved.',
      competitors: 'Busy Accounting Software, Marg ERP',
      competitiveAdvantage: 'Better integration with existing systems and superior customer support',

      // Activity timeline
      activities: [
        {
          id: 1,
          timestamp: '2024-01-18 14:30',
          action: 'Proposal sent',
          user: 'Rahul Sharma',
          description: 'Detailed proposal with pricing and implementation timeline sent to customer',
          type: 'proposal'
        },
        {
          id: 2,
          timestamp: '2024-01-16 11:00',
          action: 'Demo conducted',
          user: 'Rahul Sharma',
          description: 'Product demonstration conducted for key stakeholders',
          type: 'demo'
        },
        {
          id: 3,
          timestamp: '2024-01-12 16:45',
          action: 'Requirements gathering',
          user: 'Rahul Sharma',
          description: 'Detailed requirements discussion with IT team',
          type: 'meeting'
        },
        {
          id: 4,
          timestamp: '2024-01-10 09:15',
          action: 'Lead created',
          user: 'System',
          description: 'Lead created from website inquiry',
          type: 'system'
        }
      ],

      // Documents
      documents: [
        {
          id: 1,
          name: 'Proposal_ABC_Enterprises.pdf',
          type: 'Proposal',
          size: '2.4 MB',
          uploadedBy: 'Rahul Sharma',
          uploadedDate: '2024-01-18'
        },
        {
          id: 2,
          name: 'Requirements_Document.docx',
          type: 'Requirements',
          size: '156 KB',
          uploadedBy: 'Rahul Sharma',
          uploadedDate: '2024-01-12'
        }
      ],

      // Follow-ups
      followUps: [
        {
          id: 1,
          date: '2024-01-22',
          time: '10:00',
          type: 'Call',
          description: 'Follow up on proposal feedback',
          status: 'scheduled'
        },
        {
          id: 2,
          date: '2024-01-25',
          time: '14:00',
          type: 'Meeting',
          description: 'Final presentation to decision makers',
          status: 'scheduled'
        }
      ]
    };

    setTimeout(() => {
      setSale(mockSale);
      setLoading(false);
    }, 1000);
  }, [id]);

  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this sales opportunity? This action cannot be undone.')) {
      toast.success('Sales opportunity deleted successfully');
      navigate('/sales');
    }
  };

  const handleStageUpdate = (newStage) => {
    setSale(prev => ({ ...prev, stage: newStage }));
    toast.success(`Sales stage updated to ${newStage}`);
  };

  const handleProbabilityUpdate = (change) => {
    const newProbability = Math.max(0, Math.min(100, sale.probability + change));
    setSale(prev => ({ ...prev, probability: newProbability }));
    toast.success(`Probability updated to ${newProbability}%`);
  };

  const getStatusBadge = (status) => {
    const badgeClass = status === 'won' ? 'bg-success' :
                      status === 'lost' ? 'bg-danger' :
                      status === 'active' ? 'bg-primary' : 'bg-secondary';
    return <span className={`badge ${badgeClass}`}>{status.toUpperCase()}</span>;
  };

  const getStageBadge = (stage) => {
    const stageColors = {
      'lead': 'bg-secondary',
      'qualification': 'bg-info',
      'proposal': 'bg-warning',
      'negotiation': 'bg-primary',
      'closed-won': 'bg-success',
      'closed-lost': 'bg-danger'
    };
    const badgeClass = stageColors[stage] || 'bg-secondary';
    return <span className={`badge ${badgeClass}`}>{stage.toUpperCase().replace('-', ' ')}</span>;
  };

  const getPriorityBadge = (priority) => {
    const badgeClass = priority === 'high' ? 'bg-danger' :
                      priority === 'medium' ? 'bg-warning' : 'bg-success';
    return <span className={`badge ${badgeClass}`}>{priority.toUpperCase()}</span>;
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case 'proposal': return <FaChartLine className="text-yellow-600" />;
      case 'demo': return <FaUser className="text-cyan-600" />;
      case 'meeting': return <FaCalendar className="text-primary-600" />;
      case 'system': return <FaCheckCircle className="text-green-600" />;
      default: return <FaCheckCircle className="text-gray-600" />;
    }
  };

  if (loading) {
    return (
      <LoadingScreen
        title="Loading Sales Details..."
        subtitle="Fetching sales opportunity information"
        variant="modal"
      />
    );
  }

  if (!sale) {
    return (
      <div className="w-full px-4">
        <div className="p-4 rounded-md border bg-red-50 border-red-200 text-red-800" role="alert">
          Sales opportunity not found.
        </div>
      </div>
    );
  }

  return (
    <div className="w-full px-4">
      {/* Header */}
      <div className="grid grid-cols-1 mb-6">
        <div className="w-full">
          <div className="flex justify-between items-start">
            <div>
              <h2 className="mb-1 text-2xl font-semibold">{sale.leadNumber}</h2>
              <p className="text-gray-600 mb-2">{sale.customer} • {sale.product}</p>
              <div className="flex items-center gap-3">
                {getStatusBadge(sale.status)}
                {getStageBadge(sale.stage)}
                {getPriorityBadge(sale.priority)}
                <small className="text-gray-600">
                  <FaCalendar className="mr-1 inline" />
                  Created {new Date(sale.createdDate).toLocaleDateString()}
                </small>
              </div>
            </div>
            <div className="flex gap-2">
              {/* Stage Action Buttons */}
              {sale.stage === 'lead' && (
                <button
                  className="bg-cyan-600 hover:bg-cyan-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                  onClick={() => handleStageUpdate('qualification')}
                >
                  Qualify Lead
                </button>
              )}
              {sale.stage === 'qualification' && (
                <button
                  className="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                  onClick={() => handleStageUpdate('proposal')}
                >
                  Send Proposal
                </button>
              )}
              {sale.stage === 'proposal' && (
                <button
                  className="bg-primary-600 hover:bg-primary-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                  onClick={() => handleStageUpdate('negotiation')}
                >
                  Start Negotiation
                </button>
              )}
              {sale.stage === 'negotiation' && (
                <>
                  <button
                    className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors inline-flex items-center"
                    onClick={() => handleStageUpdate('closed-won')}
                  >
                    <FaCheckCircle className="mr-1" />
                    Close Won
                  </button>
                  <button
                    className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors inline-flex items-center"
                    onClick={() => handleStageUpdate('closed-lost')}
                  >
                    <FaTimesCircle className="mr-1" />
                    Close Lost
                  </button>
                </>
              )}

              <Link to={`/sales/${sale.id}/edit`} className="border border-primary-600 text-primary-600 hover:bg-primary-50 px-3 py-1 rounded text-sm font-medium transition-colors inline-flex items-center">
                <FaEdit className="mr-1" />
                Edit
              </Link>
              <button className="border border-red-600 text-red-600 hover:bg-red-50 px-3 py-1 rounded text-sm font-medium transition-colors inline-flex items-center" onClick={handleDelete}>
                <FaTrash className="mr-1" />
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-primary-600 text-white rounded-lg shadow-sm">
          <div className="p-4">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="text-2xl font-bold mb-0">₹{sale.expectedValue.toLocaleString()}</h4>
                <p className="text-primary-100 mb-0">Expected Value</p>
              </div>
              <FaRupeeSign size={24} />
            </div>
          </div>
        </div>
        <div className="bg-cyan-600 text-white rounded-lg shadow-sm">
          <div className="p-4">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="text-2xl font-bold mb-0">{sale.probability}%</h4>
                <p className="text-cyan-100 mb-0">Probability</p>
              </div>
              <div className="flex flex-col">
                <button
                  className="bg-white text-cyan-600 hover:bg-gray-100 mb-1 p-1 rounded text-xs transition-colors"
                  onClick={() => handleProbabilityUpdate(5)}
                >
                  <FaArrowUp size={12} />
                </button>
                <button
                  className="bg-white text-cyan-600 hover:bg-gray-100 p-1 rounded text-xs transition-colors"
                  onClick={() => handleProbabilityUpdate(-5)}
                >
                  <FaArrowDown size={12} />
                </button>
              </div>
            </div>
          </div>
        </div>
        <div className="bg-yellow-600 text-white rounded-lg shadow-sm">
          <div className="p-4">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="text-2xl font-bold mb-0">{Math.ceil((new Date(sale.expectedCloseDate) - new Date()) / (1000 * 60 * 60 * 24))}</h4>
                <p className="text-yellow-100 mb-0">Days to Close</p>
              </div>
              <FaCalendar size={24} />
            </div>
          </div>
        </div>
        <div className="bg-green-600 text-white rounded-lg shadow-sm">
          <div className="p-4">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="text-2xl font-bold mb-0">₹{Math.round(sale.expectedValue * (sale.probability / 100)).toLocaleString()}</h4>
                <p className="text-green-100 mb-0">Weighted Value</p>
              </div>
              <FaChartLine size={24} />
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="grid grid-cols-12 gap-4">
        <div className="col-span-12">
          <ul className="flex space-x-4 flex border-b border-gray-200 mb-6">
            <li className="nav-item">
              <button
                className={`nav-link ${activeTab === 'details' ? 'active' : ''}`}
                onClick={() => setActiveTab('details')}
              >
                Opportunity Details
              </button>
            </li>
            <li className="nav-item">
              <button
                className={`nav-link ${activeTab === 'activities' ? 'active' : ''}`}
                onClick={() => setActiveTab('activities')}
              >
                Activities ({sale.activities.length})
              </button>
            </li>
            <li className="nav-item">
              <button
                className={`nav-link ${activeTab === 'documents' ? 'active' : ''}`}
                onClick={() => setActiveTab('documents')}
              >
                Documents ({sale.documents.length})
              </button>
            </li>
            <li className="nav-item">
              <button
                className={`nav-link ${activeTab === 'followups' ? 'active' : ''}`}
                onClick={() => setActiveTab('followups')}
              >
                Follow-ups ({sale.followUps.length})
              </button>
            </li>
          </ul>

          {/* Tab Content */}
          {activeTab === 'details' && (
            <div className="grid grid-cols-12 gap-4">
              {/* Customer Information */}
              <div className="lg:col-span-6 mb-6">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full">
                  <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
                    <h5 className="text-lg font-semibold text-gray-900 mb-0">
                      <FaUser className="mr-2" />
                      Customer Information
                    </h5>
                  </div>
                  <div className="p-4">
                    <div className="mb-3">
                      <strong>Customer:</strong>
                      <p className="mb-0">
                        <Link to={`/customers/${sale.customerId}`} className="no-underline">
                          {sale.customer}
                        </Link>
                      </p>
                    </div>

                    <div className="mb-3">
                      <strong>Contact Person:</strong>
                      <p className="mb-0">{sale.contactPerson}</p>
                    </div>

                    <div className="mb-3">
                      <div className="flex items-center mb-2">
                        <FaPhone className="text-gray-600 mr-2" />
                        <strong>Phone:</strong>
                      </div>
                      <p className="mb-0 ml-6">{sale.phone}</p>
                    </div>

                    <div className="mb-3">
                      <div className="flex items-center mb-2">
                        <FaEnvelope className="text-gray-600 mr-2" />
                        <strong>Email:</strong>
                      </div>
                      <p className="mb-0 ml-6">{sale.email}</p>
                    </div>

                    <div className="mb-3">
                      <strong>Lead Source:</strong>
                      <p className="mb-0">{sale.source}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Product & Sales Information */}
              <div className="lg:col-span-6 mb-6">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full">
                  <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
                    <h5 className="text-lg font-semibold text-gray-900 mb-0">
                      <FaChartLine className="mr-2" />
                      Product & Sales Information
                    </h5>
                  </div>
                  <div className="p-4">
                    <div className="grid grid-cols-12 gap-4">
                      <div className="sm:col-span-6 mb-3">
                        <strong>Product:</strong>
                        <p className="mb-0">{sale.product}</p>
                      </div>
                      <div className="sm:col-span-6 mb-3">
                        <strong>Product Type:</strong>
                        <p className="mb-0">{sale.productType}</p>
                      </div>
                      <div className="col-span-12 mb-3">
                        <strong>Description:</strong>
                        <p className="mb-0">{sale.description}</p>
                      </div>
                      <div className="sm:col-span-6 mb-3">
                        <strong>Assigned To:</strong>
                        <p className="mb-0">{sale.assignedTo}</p>
                      </div>
                      <div className="sm:col-span-6 mb-3">
                        <strong>Expected Close:</strong>
                        <p className="mb-0">{new Date(sale.expectedCloseDate).toLocaleDateString()}</p>
                      </div>
                      <div className="col-span-12 mb-3">
                        <strong>Requirements:</strong>
                        <p className="mb-0">{sale.requirements}</p>
                      </div>
                      <div className="col-span-12 mb-3">
                        <strong>Notes:</strong>
                        <p className="mb-0">{sale.notes}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Competition Analysis */}
              <div className="lg:col-span-12 mb-6">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
                    <h5 className="text-lg font-semibold text-gray-900 mb-0">Competition Analysis</h5>
                  </div>
                  <div className="p-4">
                    <div className="grid grid-cols-12 gap-4">
                      <div className="md:col-span-6 mb-3">
                        <strong>Competitors:</strong>
                        <p className="mb-0">{sale.competitors}</p>
                      </div>
                      <div className="md:col-span-6 mb-3">
                        <strong>Our Competitive Advantage:</strong>
                        <p className="mb-0">{sale.competitiveAdvantage}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'activities' && (
            <div className="grid grid-cols-12 gap-4">
              <div className="col-span-12">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <div className="px-4 py-3 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
                    <h5 className="text-lg font-semibold text-gray-900 mb-0">Activity Timeline</h5>
                    <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary-600 text-white border-primary-600 hover:bg-primary-700 focus:ring-primary-500 px-3 py-1 text-sm">
                      <FaPlus className="mr-2" />
                      Add Activity
                    </button>
                  </div>
                  <div className="p-4">
                    <div className="timeline">
                      {sale.activities.map((activity, index) => (
                        <div key={activity.id} className="timeline-item">
                          <div className="timeline-marker">
                            {getActivityIcon(activity.type)}
                          </div>
                          <div className="timeline-content">
                            <div className="flex justify-between items-start">
                              <div>
                                <h6 className="mb-1">{activity.action}</h6>
                                <p className="mb-1">{activity.description}</p>
                                <small className="text-gray-600">by {activity.user}</small>
                              </div>
                              <small className="text-gray-600">{new Date(activity.timestamp).toLocaleString()}</small>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'documents' && (
            <div className="grid grid-cols-12 gap-4">
              <div className="col-span-12">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <div className="px-4 py-3 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
                    <h5 className="text-lg font-semibold text-gray-900 mb-0">Documents</h5>
                    <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary-600 text-white border-primary-600 hover:bg-primary-700 focus:ring-primary-500 px-3 py-1 text-sm">
                      <FaPlus className="mr-2" />
                      Upload Document
                    </button>
                  </div>
                  <div className="p-4">
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200 hover:bg-gray-50">
                        <thead className="bg-gray-50">
                          <tr>
                            <th>Document Name</th>
                            <th>Type</th>
                            <th>Size</th>
                            <th>Uploaded By</th>
                            <th>Upload Date</th>
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {sale.documents.map(doc => (
                            <tr key={doc.id}>
                              <td>{doc.name}</td>
                              <td><span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyan-600">{doc.type}</span></td>
                              <td>{doc.size}</td>
                              <td>{doc.uploadedBy}</td>
                              <td>{new Date(doc.uploadedDate).toLocaleDateString()}</td>
                              <td>
                                <div className="inline-flex rounded-md shadow-sm" role="group">
                                  <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 px-3 py-1 text-sm border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500">
                                    Download
                                  </button>
                                  <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 px-3 py-1 text-sm border border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500">
                                    Delete
                                  </button>
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'followups' && (
            <div className="grid grid-cols-12 gap-4">
              <div className="col-span-12">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <div className="px-4 py-3 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
                    <h5 className="text-lg font-semibold text-gray-900 mb-0">Follow-up Schedule</h5>
                    <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary-600 text-white border-primary-600 hover:bg-primary-700 focus:ring-primary-500 px-3 py-1 text-sm">
                      <FaPlus className="mr-2" />
                      Schedule Follow-up
                    </button>
                  </div>
                  <div className="p-4">
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200 hover:bg-gray-50">
                        <thead className="bg-gray-50">
                          <tr>
                            <th>Date</th>
                            <th>Time</th>
                            <th>Type</th>
                            <th>Description</th>
                            <th>Status</th>
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {sale.followUps.map(followUp => (
                            <tr key={followUp.id}>
                              <td>{new Date(followUp.date).toLocaleDateString()}</td>
                              <td>{followUp.time}</td>
                              <td><span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-600">{followUp.type}</span></td>
                              <td>{followUp.description}</td>
                              <td>
                                <span className={`badge ${followUp.status === 'scheduled' ? 'bg-warning' : 'bg-success'}`}>
                                  {followUp.status.toUpperCase()}
                                </span>
                              </td>
                              <td>
                                <div className="inline-flex rounded-md shadow-sm" role="group">
                                  <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 px-3 py-1 text-sm border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500">
                                    Edit
                                  </button>
                                  <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 px-3 py-1 text-sm border border-green-600 text-green-600 hover:bg-green-50 focus:ring-green-500">
                                    Complete
                                  </button>
                                  <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 px-3 py-1 text-sm border border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500">
                                    Cancel
                                  </button>
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SalesDetails;
