import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';

// Import configurations
import appConfig from '../config/app.js';
import { logger } from './utils/logger.js';
import { errorHandler, notFoundHandler } from './middleware/errorHandler.js';
import { requestLogger } from './middleware/requestLogger.js';
import { serveStaticFiles } from './middleware/staticFiles.js';

// Import routes
import healthRoutes from './routes/health.js';
import authRoutes from './routes/auth.js';
import userRoutes from './routes/users.js';
import customerRoutes from './routes/customers.js';
import serviceCallRoutes from './routes/serviceCalls.js';
import masterDataRoutes from './routes/masterData.js';
import executiveRoutes from './routes/executives.js';
import dashboardRoutes from './routes/dashboard.js';
import salesRoutes from './routes/sales.js';
import reportsRoutes from './routes/reports.js';
import settingsRoutes from './routes/settings.js';
import profileRoutes from './routes/profile.js';
// SaaS routes
import subscriptionRoutes from './routes/subscription.js';
import billingRoutes from './routes/billing.js';
import webhookRoutes from './routes/webhooks.js';

// Load environment variables
dotenv.config();

// Create Express application
const app = express();

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);

// Debug middleware to log header sizes and prevent 431 errors
app.use((req, res, next) => {
  // Calculate total header size
  const headerString = Object.entries(req.headers)
    .map(([key, value]) => `${key}: ${value}`)
    .join('\r\n');
  const headerSize = Buffer.byteLength(headerString, 'utf8');

  // Log large headers for debugging
  if (headerSize > 8192) { // 8KB threshold
    console.log(`⚠️ Large headers detected (${headerSize} bytes):`, {
      url: req.url,
      method: req.method,
      headerSize,
      authHeaderLength: req.headers.authorization ? req.headers.authorization.length : 0,
      userAgent: req.headers['user-agent'] ? req.headers['user-agent'].substring(0, 100) : 'none'
    });
  }

  // Set max header size to 32KB to handle large tokens temporarily
  if (req.connection) {
    req.connection.maxHeadersCount = 1000;
  }
  next();
});

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com", "https://cdn.jsdelivr.net"],
      fontSrc: ["'self'", "https://fonts.gstatic.com", "https://cdn.jsdelivr.net"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "http://**************:*", "https://**************:*", "http://localhost:*", "https://localhost:*"],
    },
  },
  crossOriginEmbedderPolicy: false,
}));

// CORS configuration (conditionally applied)
if (appConfig.frontend.enableCors) {
  app.use(cors({
    origin: appConfig.frontend.corsOrigins,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  }));
  logger.info('🌐 CORS enabled for origins:', appConfig.frontend.corsOrigins);
} else {
  // For same-origin deployments, allow all origins
  app.use(cors({
    origin: true,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  }));
  logger.info('🌐 CORS disabled - allowing all origins (same-origin deployment)');
}

// Rate limiting (conditionally applied)
if (appConfig.security.enableRateLimiting) {
  const limiter = rateLimit({
    windowMs: appConfig.security.rateLimitWindowMs,
    max: appConfig.security.rateLimitMaxRequests,
    message: {
      error: 'Too many requests from this IP, please try again later.',
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
  app.use(limiter);
  logger.info('🛡️ Rate limiting enabled');
} else {
  logger.info('⚠️ Rate limiting disabled');
}

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());

// Compression middleware
app.use(compression());

// Request logging middleware
app.use(requestLogger);

// API routes
app.use('/health', healthRoutes);
app.use(`${appConfig.api.prefix}/auth`, authRoutes);
app.use(`${appConfig.api.prefix}/users`, userRoutes);
app.use(`${appConfig.api.prefix}/customers`, customerRoutes);
app.use(`${appConfig.api.prefix}/service-calls`, serviceCallRoutes);
app.use(`${appConfig.api.prefix}/master-data`, masterDataRoutes);
app.use(`${appConfig.api.prefix}/executives`, executiveRoutes);
app.use(`${appConfig.api.prefix}/dashboard`, dashboardRoutes);
app.use(`${appConfig.api.prefix}/sales`, salesRoutes);
app.use(`${appConfig.api.prefix}/reports`, reportsRoutes);
app.use(`${appConfig.api.prefix}/settings`, settingsRoutes);
app.use(`${appConfig.api.prefix}/profile`, profileRoutes);
// SaaS routes
app.use(`${appConfig.api.prefix}/subscription`, subscriptionRoutes);
app.use(`${appConfig.api.prefix}/billing`, billingRoutes);
app.use('/webhooks', webhookRoutes); // Webhooks don't need API prefix

// Serve static files (uploads, etc.)
app.use('/uploads', express.static('uploads'));

// API documentation (Swagger) - temporarily disabled for debugging
// TODO: Re-enable Swagger documentation
logger.info('📚 Swagger documentation temporarily disabled');

// Serve frontend static files in production
serveStaticFiles(app);

// 404 handler (must be after static file serving)
app.use(notFoundHandler);

// Global error handler
app.use(errorHandler);

// Graceful shutdown
const gracefulShutdown = (signal) => {
  logger.info(`Received ${signal}. Starting graceful shutdown...`);

  server.close(() => {
    logger.info('HTTP server closed.');

    // Close database connections, cleanup resources, etc.
    process.exit(0);
  });

  // Force close after 30 seconds
  setTimeout(() => {
    logger.error('Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 30000);
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start server
const PORT = appConfig.app.port;
const server = app.listen(PORT, () => {
  logger.info(`🚀 TallyCRM Backend Server started successfully!`);
  logger.info(`📍 Environment: ${appConfig.app.env}`);
  logger.info(`🌐 Server running on: ${appConfig.app.url}`);
  logger.info(`📡 API Base URL: ${appConfig.app.url}${appConfig.api.prefix}`);

  if (appConfig.development.enableSwagger && appConfig.app.env === 'development') {
    logger.info(`📚 API Docs: ${appConfig.app.url}${appConfig.development.swaggerPath}`);
  }
});

// Configure server to handle large headers (for JWT tokens)
server.maxHeadersCount = 0; // No limit on header count
server.headersTimeout = 60000; // 60 seconds
server.requestTimeout = 60000; // 60 seconds

export default app;
