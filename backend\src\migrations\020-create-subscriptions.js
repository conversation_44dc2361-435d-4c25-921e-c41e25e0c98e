import { DataTypes, Op } from 'sequelize';

export const up = async (queryInterface) => {
  await queryInterface.createTable('subscriptions', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    plan_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'subscription_plans',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT',
    },
    stripe_subscription_id: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: true,
    },
    stripe_customer_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    status: {
      type: DataTypes.ENUM(
        'trial',
        'active',
        'past_due',
        'canceled',
        'unpaid',
        'incomplete',
        'incomplete_expired',
        'paused'
      ),
      defaultValue: 'trial',
    },
    current_period_start: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    current_period_end: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    trial_start: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    trial_end: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    cancel_at_period_end: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    canceled_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    ended_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    quantity: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    currency: {
      type: DataTypes.STRING(3),
      defaultValue: 'INR',
    },
    interval: {
      type: DataTypes.ENUM('month', 'year'),
      defaultValue: 'month',
    },
    interval_count: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
    },
    metadata: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Create indexes
  await queryInterface.addIndex('subscriptions', ['tenant_id'], {
    unique: true,
    name: 'subscriptions_tenant_id_unique',
  });

  await queryInterface.addIndex('subscriptions', ['stripe_subscription_id'], {
    unique: true,
    name: 'subscriptions_stripe_subscription_id_unique',
    where: {
      stripe_subscription_id: {
        [Op.ne]: null,
      },
    },
  });

  await queryInterface.addIndex('subscriptions', ['status'], {
    name: 'subscriptions_status_idx',
  });

  await queryInterface.addIndex('subscriptions', ['current_period_end'], {
    name: 'subscriptions_current_period_end_idx',
  });
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('subscriptions');
};
