/* TallyCRM App Specific Styles */

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Layout Styles - Tailwind Compatible */
.sidebar {
  @apply w-[220px] transition-all duration-300 ease-in-out fixed top-0 left-0 h-screen z-50 overflow-y-auto;
  background: var(--sidebar-gradient, linear-gradient(180deg, #7c3aed 0%, #6b21a8 50%, #581c87 100%));
  color: var(--primary-text, #ffffff);
}

.sidebar.collapsed {
  @apply w-16;
}

.header {
  @apply h-16 bg-white border-b border-gray-200 flex items-center px-6 sticky top-0 z-40;
}

.footer {
  @apply h-16 bg-white border-t border-gray-200 flex items-center justify-center px-6 mt-auto;
}

/* Auth Layout */
.auth-layout {
  @apply min-h-screen flex items-center justify-center p-8;
  background: linear-gradient(135deg, var(--primary-color, #7c3aed), var(--primary-dark, #6b21a8));
}

/* Navigation Links with Dynamic Text Color */
.nav-link {
  @apply flex items-center px-4 py-3 mx-2 my-1 rounded-lg transition-all duration-200 no-underline;
  color: rgba(var(--primary-text-rgb, 255, 255, 255), 0.8);
}

.nav-link:hover {
  color: var(--primary-text, #ffffff);
  background: rgba(var(--primary-text-rgb, 255, 255, 255), 0.1);
}

.nav-link.active {
  color: var(--primary-text, #ffffff);
  background: rgba(var(--primary-text-rgb, 255, 255, 255), 0.2);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Primary Button Styles with Dynamic Text Color */
.btn-primary {
  background: var(--button-gradient, linear-gradient(135deg, var(--primary-color, #7c3aed), var(--primary-dark, #6b21a8)));
  border-color: var(--primary-color, #7c3aed);
  color: var(--primary-text, #ffffff);
}

.btn-primary:hover {
  background: var(--primary-dark, #6b21a8);
  border-color: var(--primary-dark, #6b21a8);
  color: var(--primary-text, #ffffff);
  transform: translateY(-1px);
  box-shadow: var(--shadow-primary, 0 0.5rem 1rem rgba(124, 58, 237, 0.15));
}

.btn-primary:focus {
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb, 124, 58, 237), 0.25);
  color: var(--primary-text, #ffffff);
}

/* Outline Primary Button */
.btn-outline-primary {
  color: var(--primary-color, #7c3aed);
  border-color: var(--primary-color, #7c3aed);
}

.btn-outline-primary:hover {
  background: var(--primary-color, #7c3aed);
  border-color: var(--primary-color, #7c3aed);
  color: var(--primary-text, #ffffff);
  transform: translateY(-1px);
  box-shadow: var(--shadow-primary, 0 0.5rem 1rem rgba(124, 58, 237, 0.15));
}

.btn-outline-primary:focus {
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb, 124, 58, 237), 0.25);
}

/* Ghost Button */
.btn-ghost {
  color: var(--primary-color, #7c3aed);
}

.btn-ghost:hover {
  background: rgba(var(--primary-rgb, 124, 58, 237), 0.1);
  color: var(--primary-dark, #6b21a8);
}

/* Link Button */
.btn-link {
  color: var(--primary-color, #7c3aed);
}

.btn-link:hover {
  color: var(--primary-dark, #6b21a8);
}

/* Settings Page Specific Styles with Dynamic Text Color */
.stats-card-primary {
  background: var(--sidebar-gradient, linear-gradient(180deg, #7c3aed 0%, #6b21a8 50%, #581c87 100%));
  color: var(--primary-text, #ffffff);
}

.header-gradient {
  background: var(--sidebar-gradient, linear-gradient(180deg, #7c3aed 0%, #6b21a8 50%, #581c87 100%));
  color: var(--primary-text, #ffffff);
}

.settings-nav-active {
  background: var(--primary-color, #7c3aed);
  color: var(--primary-text, #ffffff);
  border-right: 4px solid var(--primary-dark, #6b21a8);
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.settings-nav-active .h-5 {
  color: var(--primary-text, #ffffff);
}

/* Spinner Styles */
.spinner-primary {
  border-color: var(--primary-color, #7c3aed);
}

.spinner-primary-text {
  color: var(--primary-color, #7c3aed);
}

/* Alert Styles */
.alert-primary {
  background: rgba(var(--primary-rgb, 124, 58, 237), 0.1);
  border-color: var(--primary-color, #7c3aed);
  color: var(--primary-dark, #6b21a8);
}

/* Dashboard Icon Styles */
.dashboard-icon-bg {
  background: rgba(var(--primary-rgb, 124, 58, 237), 0.1);
}

.dashboard-icon-text {
  color: var(--primary-color, #7c3aed);
}

/* White Stats Card Icon Styles - Ensures visibility on white backgrounds */
.white-stats-icon-bg {
  background: rgba(107, 114, 128, 0.1);
}

.white-stats-icon-text {
  color: #6b7280;
}

/* Badge Styles */
.badge-primary {
  background: rgba(var(--primary-rgb, 124, 58, 237), 0.1);
  color: var(--primary-dark, #6b21a8);
}

/* Focus Ring Colors */
.focus-primary:focus {
  @apply outline-none;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb, 124, 58, 237), 0.1);
  border-color: var(--primary-color, #7c3aed);
}

/* Link Colors */
.link-primary {
  color: var(--primary-color, #7c3aed);
}

.link-primary:hover {
  color: var(--primary-dark, #6b21a8);
}

/* Dynamic Text Color Classes */
.text-primary-dynamic {
  color: var(--primary-text, #ffffff);
}

.bg-primary-dynamic {
  background-color: var(--primary-color, #7c3aed);
  color: var(--primary-text, #ffffff);
}

.border-primary-dynamic {
  border-color: var(--primary-color, #7c3aed);
}

/* Navigation with Dynamic Text */
.nav-primary {
  background: var(--primary-color, #7c3aed);
  color: var(--primary-text, #ffffff);
}

.nav-primary .nav-link {
  color: rgba(var(--primary-text-rgb, 255, 255, 255), 0.8);
}

.nav-primary .nav-link:hover {
  color: var(--primary-text, #ffffff);
}

.nav-primary .nav-link.active {
  color: var(--primary-text, #ffffff);
  background: rgba(var(--primary-text-rgb, 255, 255, 255), 0.2);
}

.nav-link i {
  @apply w-5 text-center mr-3;
}

.sidebar.collapsed .nav-link span {
  @apply hidden;
}

.sidebar.collapsed .nav-link {
  @apply justify-center mx-2 px-3;
}

.sidebar.collapsed .nav-link i {
  @apply mr-0;
}

.auth-card {
  @apply w-full max-w-md bg-white rounded-lg shadow-lg p-8;
}

.auth-logo {
  @apply text-center mb-8;
}

/* Navigation Styles */
.nav-link {
  @apply text-white text-opacity-80 py-3 px-4 flex items-center no-underline transition-all duration-300 rounded mx-2 my-1;
}

.nav-link:hover {
  @apply text-white bg-white bg-opacity-10 no-underline;
}

.nav-link.active {
  @apply text-white bg-purple-600;
}

.nav-link i {
  @apply mr-3 w-5 text-center;
}

.sidebar.collapsed .nav-link span {
  @apply hidden;
}

.sidebar.collapsed .nav-link {
  @apply justify-center;
}

.sidebar.collapsed .nav-link i {
  @apply mr-0;
}

/* Dashboard Styles */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  border-left: 4px solid var(--brand-primary);
  transition: var(--transition);
}

.stat-card:hover {
  box-shadow: var(--shadow);
  transform: translateY(-2px);
}

.stat-card .stat-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.stat-card .stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--brand-dark);
  margin-bottom: 0.5rem;
}

.stat-card .stat-label {
  color: var(--muted);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Table Styles */
.table-responsive {
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  background: white;
}

.table th {
  background-color: var(--bg-secondary);
  border-bottom: 2px solid var(--border-color);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.875rem;
  letter-spacing: 0.5px;
}

/* Form Styles */
.form-floating > label {
  color: var(--muted);
}

.form-control:focus + label {
  color: var(--brand-primary);
}

/* Button Styles - Using theme-aware styles defined above */

.btn-tally {
  background-color: var(--tally-red);
  border-color: var(--tally-red);
  color: white;
}

.btn-tally:hover {
  background-color: #c41e24;
  border-color: #c41e24;
  color: white;
}

/* Loading States */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
}

/* Error States */
.error-boundary {
  padding: 2rem;
  text-align: center;
  background-color: var(--bg-light);
  border-radius: var(--border-radius);
  margin: 2rem;
}

.error-boundary h2 {
  color: var(--brand-danger);
  margin-bottom: 1rem;
}

.error-boundary p {
  color: var(--muted);
  margin-bottom: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    @apply -translate-x-full transition-transform duration-300 ease-in-out;
  }

  .sidebar.show {
    @apply translate-x-0;
  }

  .dashboard-stats {
    @apply grid-cols-1;
  }

  .auth-card {
    @apply m-4 p-6;
  }
}
