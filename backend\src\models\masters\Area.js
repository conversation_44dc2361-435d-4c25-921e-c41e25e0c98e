import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const Area = sequelize.define('Area', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 100],
      },
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        len: [2, 20],
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    city: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    state: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    country: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'India',
    },
    postal_codes: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Array of postal codes covered by this area',
    },
    coordinates: {
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'Geographic coordinates for mapping',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'areas',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['code'],
        unique: true,
      },
      {
        fields: ['city'],
      },
      {
        fields: ['state'],
      },
      {
        fields: ['country'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['sort_order'],
      },
    ],
  });

  // Class methods
  Area.getDefaultAreas = function() {
    return [
      {
        name: 'Bangalore Central',
        code: 'BLR_CENTRAL',
        description: 'Central Bangalore area',
        city: 'Bangalore',
        state: 'Karnataka',
        country: 'India',
        postal_codes: ['560001', '560002', '560003', '560004', '560005'],
        coordinates: { lat: 12.9716, lng: 77.5946 },
        sort_order: 1,
      },
      {
        name: 'Bangalore North',
        code: 'BLR_NORTH',
        description: 'North Bangalore area',
        city: 'Bangalore',
        state: 'Karnataka',
        country: 'India',
        postal_codes: ['560013', '560020', '560021', '560022', '560024'],
        coordinates: { lat: 13.0827, lng: 77.5946 },
        sort_order: 2,
      },
      {
        name: 'Bangalore South',
        code: 'BLR_SOUTH',
        description: 'South Bangalore area',
        city: 'Bangalore',
        state: 'Karnataka',
        country: 'India',
        postal_codes: ['560030', '560034', '560035', '560036', '560041'],
        coordinates: { lat: 12.8611, lng: 77.5946 },
        sort_order: 3,
      },
      {
        name: 'Bangalore East',
        code: 'BLR_EAST',
        description: 'East Bangalore area',
        city: 'Bangalore',
        state: 'Karnataka',
        country: 'India',
        postal_codes: ['560008', '560016', '560017', '560025', '560037'],
        coordinates: { lat: 12.9716, lng: 77.7946 },
        sort_order: 4,
      },
      {
        name: 'Bangalore West',
        code: 'BLR_WEST',
        description: 'West Bangalore area',
        city: 'Bangalore',
        state: 'Karnataka',
        country: 'India',
        postal_codes: ['560010', '560018', '560020', '560023', '560040'],
        coordinates: { lat: 12.9716, lng: 77.3946 },
        sort_order: 5,
      },
      {
        name: 'Mumbai Central',
        code: 'MUM_CENTRAL',
        description: 'Central Mumbai area',
        city: 'Mumbai',
        state: 'Maharashtra',
        country: 'India',
        postal_codes: ['400001', '400002', '400003', '400004', '400005'],
        coordinates: { lat: 19.0760, lng: 72.8777 },
        sort_order: 6,
      },
      {
        name: 'Delhi Central',
        code: 'DEL_CENTRAL',
        description: 'Central Delhi area',
        city: 'Delhi',
        state: 'Delhi',
        country: 'India',
        postal_codes: ['110001', '110002', '110003', '110004', '110005'],
        coordinates: { lat: 28.6139, lng: 77.2090 },
        sort_order: 7,
      },
      {
        name: 'Chennai Central',
        code: 'CHE_CENTRAL',
        description: 'Central Chennai area',
        city: 'Chennai',
        state: 'Tamil Nadu',
        country: 'India',
        postal_codes: ['600001', '600002', '600003', '600004', '600005'],
        coordinates: { lat: 13.0827, lng: 80.2707 },
        sort_order: 8,
      },
      {
        name: 'Hyderabad Central',
        code: 'HYD_CENTRAL',
        description: 'Central Hyderabad area',
        city: 'Hyderabad',
        state: 'Telangana',
        country: 'India',
        postal_codes: ['500001', '500002', '500003', '500004', '500005'],
        coordinates: { lat: 17.3850, lng: 78.4867 },
        sort_order: 9,
      },
      {
        name: 'Pune Central',
        code: 'PUN_CENTRAL',
        description: 'Central Pune area',
        city: 'Pune',
        state: 'Maharashtra',
        country: 'India',
        postal_codes: ['411001', '411002', '411003', '411004', '411005'],
        coordinates: { lat: 18.5204, lng: 73.8567 },
        sort_order: 10,
      },
    ];
  };

  // Instance methods
  Area.prototype.getFullLocation = function() {
    return `${this.name}, ${this.city}, ${this.state}, ${this.country}`;
  };

  Area.prototype.hasPostalCode = function(postalCode) {
    return this.postal_codes && this.postal_codes.includes(postalCode);
  };

  // Associations
  Area.associate = function(models) {
    Area.hasMany(models.Customer, {
      foreignKey: 'area_id',
      as: 'customers',
    });

    Area.hasMany(models.ServiceCall, {
      foreignKey: 'area_id',
      as: 'serviceCalls',
    });
  };

  return Area;
}
