import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const Permission = sequelize.define('Permission', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 100],
      },
    },
    slug: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        len: [2, 100],
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    module: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Module this permission belongs to',
    },
    action: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Action type (create, read, update, delete, etc.)',
    },
    resource: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Resource this permission applies to',
    },
    is_system: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'System permissions cannot be deleted',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'permissions',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['slug'],
        unique: true,
      },
      {
        fields: ['module'],
      },
      {
        fields: ['action'],
      },
      {
        fields: ['resource'],
      },
      {
        fields: ['module', 'action', 'resource'],
      },
    ],
  });

  // Class methods
  Permission.getSystemPermissions = function() {
    const modules = [
      'dashboard',
      'users',
      'roles',
      'customers',
      'services',
      'sales',
      'masters',
      'reports',
      'settings',
    ];

    const actions = ['create', 'read', 'update', 'delete', 'export', 'import'];
    const permissions = [];

    modules.forEach(module => {
      actions.forEach(action => {
        permissions.push({
          name: `${module.charAt(0).toUpperCase() + module.slice(1)} ${action.charAt(0).toUpperCase() + action.slice(1)}`,
          slug: `${module}.${action}`,
          description: `${action.charAt(0).toUpperCase() + action.slice(1)} ${module}`,
          module,
          action,
          resource: module,
          is_system: true,
        });
      });
    });

    // Add special permissions
    permissions.push(
      {
        name: 'View Dashboard',
        slug: 'dashboard.view',
        description: 'View dashboard and analytics',
        module: 'dashboard',
        action: 'view',
        resource: 'dashboard',
        is_system: true,
      },
      {
        name: 'Manage System Settings',
        slug: 'system.manage',
        description: 'Manage system-wide settings',
        module: 'system',
        action: 'manage',
        resource: 'system',
        is_system: true,
      },
      {
        name: 'View All Tenants',
        slug: 'tenants.view_all',
        description: 'View all tenant information',
        module: 'tenants',
        action: 'view_all',
        resource: 'tenants',
        is_system: true,
      }
    );

    return permissions;
  };

  // Associations
  Permission.associate = function(models) {
    Permission.belongsToMany(models.Role, {
      through: models.RolePermission,
      foreignKey: 'permission_id',
      otherKey: 'role_id',
      as: 'roles',
    });
  };

  return Permission;
}
